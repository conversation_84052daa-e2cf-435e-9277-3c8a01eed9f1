package view

import (
	"sort"

	"difyserver/models"
)

type SystemMenuView struct {
	*models.SystemMenu
	Children []*SystemMenuView
}

func BuildMenuTree(menus []*models.SystemMenu) []*SystemMenuView {
	// 创建一个映射，用于快速查找菜单
	menuMap := make(map[int64]*SystemMenuView)

	// 将所有菜单放入映射中，以ID为键
	for _, menu := range menus {
		// 创建一个副本，避免修改原始数据
		menuCopy := SystemMenuView{
			SystemMenu: menu,
			Children:   make([]*SystemMenuView, 0),
		}
		menuMap[menu.ID] = &menuCopy
	}

	// 存储顶级菜单
	var rootMenus []*SystemMenuView

	// 构建树形结构
	for _, menu := range menuMap {
		// 如果是顶级菜单（PID为nil或0）
		if menu.PID == nil || *menu.PID == 0 {
			rootMenus = append(rootMenus, menu)
		} else {
			// 如果有父菜单，将当前菜单添加到父菜单的子菜单列表中
			if parent, exists := menuMap[*menu.PID]; exists {
				parent.Children = append(parent.Children, menu)
			} else {
				// 如果找不到父菜单，则作为顶级菜单处理
				rootMenus = append(rootMenus, menu)
			}
		}
	}

	// 递归排序所有层级的菜单（可选，如果需要按某种顺序排列）
	sortMenuTree(rootMenus)

	return rootMenus
}

// sortMenuTree 递归排序菜单树
// 这里假设按ID排序，可以根据需要修改排序逻辑
func sortMenuTree(menus []*SystemMenuView) {
	// 按ID排序当前层级的菜单
	sort.Slice(menus, func(i, j int) bool {
		return menus[i].ID < menus[j].ID
	})

	// 递归排序子菜单
	for _, menu := range menus {
		if len(menu.Children) > 0 {
			sortMenuTree(menu.Children)
		}
	}
}
