package view

import (
	"encoding/json"
	"errors"

	"difyserver/utils"
)

// DifyResult 用于成功情况的返回结构体
type DifyResult[T any] struct {
	Result string `json:"result"`
	Data   T      `json:"data"`
}

type Result struct {
	Result string `json:"result"`
	Data   any    `json:"data"`
}

// DifyError 错误信息结构体，用于错误情况的返回
type DifyError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Status  int    `json:"status"`
}

func UnWrapError(err error) *DifyError {
	if err == nil {
		return nil
	}
	var reqErr *utils.RequestError
	if errors.As(err, &reqErr) {
		difyErr := new(DifyError)
		er := json.Unmarshal([]byte(reqErr.Message), difyErr)
		if er == nil && difyErr.Code != "" {
			return &DifyError{
				Code:    difyErr.Code,
				Message: difyErr.Message,
				Status:  difyErr.Status,
			}
		}
		var difyErr2 DifyResult[string]
		er2 := json.Unmarshal([]byte(reqErr.Message), &difyErr2)
		if er2 == nil && difyErr2.Data != "" {
			return &DifyError{
				Code:    "Internal Error",
				Message: difyErr2.Data,
				Status:  reqErr.StatusCode,
			}
		}

		return &DifyError{
			Code:    "Internal Error",
			Message: reqErr.Message,
			Status:  reqErr.StatusCode,
		}
	} else {
		return &DifyError{
			Code:    "Internal Error",
			Message: err.Error(),
			Status:  500,
		}
	}
}
