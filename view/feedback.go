package view

// CreateFeedbackRequest 提交反馈请求
type CreateFeedbackRequest struct {
	FeedbackContent string `json:"feedback_content" binding:"required"`
}

// RespondFeedbackRequest 回复反馈请求
type RespondFeedbackRequest struct {
	Response string `json:"response" binding:"required"`
}

// FeedbackResponse 反馈响应
type FeedbackResponse struct {
	ID              int64  `json:"id"`
	UserID          int64  `json:"user_id"`
	UserName        string `json:"user_name,omitempty"`
	TenantID        int64  `json:"tenant_id"`
	FeedbackContent string `json:"feedback_content"`
	Response        string `json:"response,omitempty"`
	Status          string `json:"status"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at,omitempty"`
}
