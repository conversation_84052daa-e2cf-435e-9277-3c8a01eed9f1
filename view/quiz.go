package view

import (
	"time"

	"github.com/google/uuid"
)

// QuizSubmissionRequest 答题提交请求
type QuizSubmissionRequest struct {
	Answers     map[string]string      `json:"answers" binding:"required"`     // 用户答案 question_id -> answer
	SessionInfo map[string]interface{} `json:"session_info,omitempty"`         // 会话信息（用户名等）
}

// QuestionResult 单题结果
type QuestionResult struct {
	QuestionID    string `json:"question_id"`
	Question      string `json:"question"`
	UserAnswer    string `json:"user_answer"`
	CorrectAnswer string `json:"correct_answer"`
	IsCorrect     bool   `json:"is_correct"`
	Explanation   string `json:"explanation"`
}

// QuizSubmissionResponse 答题提交响应
type QuizSubmissionResponse struct {
	SubmissionID    uuid.UUID        `json:"submission_id"`
	Score           int              `json:"score"`
	TotalQuestions  int              `json:"total_questions"`
	CorrectAnswers  int              `json:"correct_answers"`
	Percentage      float64          `json:"percentage"`
	DetailedResults []QuestionResult `json:"detailed_results"`
	Analysis        string           `json:"analysis"`
	SubmittedAt     time.Time        `json:"submitted_at"`
}

// SubmissionSummary 提交摘要
type SubmissionSummary struct {
	SubmissionID uuid.UUID              `json:"submission_id"`
	UserInfo     map[string]interface{} `json:"user_info,omitempty"`
	Score        int                    `json:"score"`
	Percentage   float64                `json:"percentage"`
	SubmittedAt  time.Time              `json:"submitted_at"`
}

// QuizStatisticsResponse 答题统计响应
type QuizStatisticsResponse struct {
	QuizUUID          uuid.UUID           `json:"quiz_uuid"`
	QuizTitle         string              `json:"quiz_title"`
	TotalSubmissions  int                 `json:"total_submissions"`
	AverageScore      float64             `json:"average_score"`
	HighestScore      int                 `json:"highest_score"`
	LowestScore       int                 `json:"lowest_score"`
	PassRate          float64             `json:"pass_rate"`
	ScoreDistribution map[string]int      `json:"score_distribution"` // 分数段分布
	RecentSubmissions []SubmissionSummary `json:"recent_submissions"`
	LastSubmissionAt  time.Time           `json:"last_submission_at"`
}

// BatchStatisticsResponse 批次统计响应
type BatchStatisticsResponse struct {
	BatchTitle        string                   `json:"batch_title"`
	TotalQuizzes      int                      `json:"total_quizzes"`      // 批次下的题目数量
	TotalSubmissions  int                      `json:"total_submissions"`  // 总提交数
	AverageScore      float64                  `json:"average_score"`      // 平均分
	QuizStatistics    []QuizStatisticsResponse `json:"quiz_statistics"`    // 各个quiz的统计
	CreatedAt         time.Time                `json:"created_at"`
}

// QuizDashboardResponse 答题系统仪表板响应
type QuizDashboardResponse struct {
	TotalBatches     int                       `json:"total_batches"`     // 总批次数
	TotalQuizzes     int                       `json:"total_quizzes"`     // 总题目数
	TotalSubmissions int                       `json:"total_submissions"` // 总提交数
	BatchStatistics  []BatchStatisticsResponse `json:"batch_statistics"`  // 各批次统计
}

// QuizResultsResponse 答题结果列表响应
type QuizResultsResponse struct {
	QuizUUID     uuid.UUID           `json:"quiz_uuid"`
	QuizTitle    string              `json:"quiz_title"`
	Submissions  []SubmissionSummary `json:"submissions"`
	TotalCount   int                 `json:"total_count"`
	Page         int                 `json:"page"`
	PageSize     int                 `json:"page_size"`
	TotalPages   int                 `json:"total_pages"`
}

// UserQuizStatistics 用户答题统计
type UserQuizStatistics struct {
	UserID       *uuid.UUID             `json:"user_id,omitempty"`
	UserInfo     map[string]interface{} `json:"user_info,omitempty"`
	QuizUUID     uuid.UUID              `json:"quiz_uuid"`
	QuizTitle    string                 `json:"quiz_title"`
	AttemptCount int                    `json:"attempt_count"`    // 答题次数
	BestScore    int                    `json:"best_score"`       // 最高分
	LatestScore  int                    `json:"latest_score"`     // 最新得分
	AverageScore float64                `json:"average_score"`    // 平均分
	FirstAttempt time.Time              `json:"first_attempt"`    // 首次答题时间
	LatestAttempt time.Time             `json:"latest_attempt"`   // 最新答题时间
	HasAnswered  bool                   `json:"has_answered"`     // 是否已答题
	QuizDetails  []QuizDetail           `json:"quiz_details,omitempty"` // 题目详情列表
}

// BatchUserStatistics 批次用户统计
type BatchUserStatistics struct {
	BatchTitle     string               `json:"batch_title"`
	UserStatistics []UserQuizStatistics `json:"user_statistics"`
	TotalUsers     int                  `json:"total_users"`
	TotalAttempts  int                  `json:"total_attempts"`
	AverageScore   float64              `json:"average_score"`
}

// QuizDetail 题目详情
type QuizDetail struct {
	QuizUUID    uuid.UUID `json:"quiz_uuid"`
	QuizTitle   string    `json:"quiz_title"`
	Score       int       `json:"score"`
	Percentage  float64   `json:"percentage"`
	SubmittedAt time.Time `json:"submitted_at"`
}
