package view

import (
	"time"
)

// UserDocumentRequest 用户文档创建请求
type UserDocumentRequest struct {
	DocType string `json:"doc_type" binding:"required" example:"user_manual"`
	Content string `json:"content" binding:"required" example:"这是用户手册的内容..."`
}

// UserDocumentUpdateRequest 用户文档更新请求
type UserDocumentUpdateRequest struct {
	Content string `json:"content" binding:"required" example:"这是更新后的内容..."`
}

// UserDocumentResponse 用户文档完整响应
type UserDocumentResponse struct {
	ID        int64     `json:"id"`
	DocType   string    `json:"doc_type"`
	Content   string    `json:"content"`
	CreatedBy string    `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UserDocumentListResponse 用户文档列表响应（不包含内容）
type UserDocumentListResponse struct {
	ID        int64     `json:"id"`
	DocType   string    `json:"doc_type"`
	CreatedBy string    `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UserDocumentQueryRequest 用户文档查询请求
type UserDocumentQueryRequest struct {
	DocType   string `json:"doc_type" form:"doc_type" example:"user_manual"`
	CreatedBy string `json:"created_by" form:"created_by" example:"<EMAIL>"`
	Page      int    `json:"page" form:"page" example:"1"`
	PageSize  int    `json:"page_size" form:"page_size" example:"20"`
}
