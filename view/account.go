package view

import "difyserver/models"

type CreateAccountParam struct {
	Name       string `json:"name"`
	Email      string `json:"email"`
	Password   string `json:"password"`
	TenantID   string `json:"tenant_id"`
	Unit       string `json:"unit"`
	Department string `json:"department"`
}

type UpdateAccountInfoParam struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Unit       string `json:"unit"`
	Department string `json:"department"`
}

type SimpleAccount struct {
	ID         string `json:"id" gorm:"id"`
	Name       string `json:"name" gorm:"name"`
	Email      string `json:"email" gorm:"email"`
	Unit       string `json:"unit" gorm:"unit"`
	Department string `json:"department" gorm:"department"`
}

type AccountView struct {
	models.Account
	FirstTenantID        string `json:"firstTenantID"`
	FirstTenantRole      string `json:"firstTenantRole"`
	FirstTenantAccountID string `json:"firstTenantAccountID"`
}
