package view

import (
	"time"

	"github.com/google/uuid"
)

// TempDataCreateRequest 创建临时数据请求
type TempDataCreateRequest struct {
	Content string `json:"content" binding:"required" example:"这是临时数据内容"`
	Title   string `json:"title" binding:"required" example:"临时数据标题"`
}

// TempDataResponse 临时数据响应结构
type TempDataResponse struct {
	UUID      uuid.UUID `json:"uuid"`
	Title     string    `json:"title"`
	Content   string    `json:"content"`
	CreatedAt time.Time `json:"created_at"`
}

// TempDataCreateResponse 创建临时数据响应
type TempDataCreateResponse struct {
	UUID    uuid.UUID `json:"uuid"`
	Message string    `json:"message"`
}
