package view

import (
	"time"

	"difyserver/models"
)

var TenantAccountRole string

const (
	TenantAccountRoleOwner  = "owner"
	TenantAccountRoleAdmin  = "admin"
	TenantAccountRoleEditor = "editor"
	TenantAccountRoleNormal = "normal"
)

var RolesNum = map[string]int{
	TenantAccountRoleOwner:  1,
	TenantAccountRoleAdmin:  2,
	TenantAccountRoleEditor: 3,
	TenantAccountRoleNormal: 4,
}

var ValidRoles = map[string]bool{
	//TenantAccountRoleOwner:  true,
	TenantAccountRoleAdmin:  true,
	TenantAccountRoleEditor: true,
	TenantAccountRoleNormal: true,
}

const (
	TenantStatusNormal  = "normal"
	TenantStatusArchive = "archive"
)

var ValidTenantStatuses = map[string]bool{
	TenantStatusNormal:  true,
	TenantStatusArchive: true,
}

type AddTenantParam struct {
	Name   string `json:"name"`
	UserID string `json:"user_id"`
}

type DelTenantParam struct {
	TenantID string `json:"tenant_id"`
}

type UpdateTenantNameParam struct {
	TenantID string `json:"tenant_id"`
	Name     string `json:"name"`
}

type UpdateTenantStatusParam struct {
	TenantID string `json:"tenant_id"`
	Status   string `json:"status"`
}

type TenantAccountParam struct {
	TenantID string `json:"tenant_id"`
}

type SwitchTenantResult struct {
	Result    string        `json:"result"`
	NewTenant CurrentTenant `json:"new_tenant"`
}

type CurrentTenant struct {
	Id             string      `json:"id"`
	Name           string      `json:"name"`
	Plan           string      `json:"plan"`
	Status         string      `json:"status"`
	CreatedAt      int         `json:"created_at"`
	Role           string      `json:"role"`
	InTrial        interface{} `json:"in_trial"`
	TrialEndReason interface{} `json:"trial_end_reason"`
	CustomConfig   interface{} `json:"custom_config"`
}

type TenantView struct {
	models.Tenant
	OwnerName   string `json:"owner_name"`
	OwnerEmail  string `json:"owner_email"`
	DefaultTeam bool   `json:"default_team"`
}

type EncryptedConfig struct {
	ApiKey                string `json:"api_key"`
	EndpointUrl           string `json:"endpoint_url"`
	Mode                  string `json:"mode"`
	ContextSize           string `json:"context_size"`
	MaxTokensToSample     string `json:"max_tokens_to_sample"`
	AgentThoughSupport    string `json:"agent_though_support"`
	FunctionCallingType   string `json:"function_calling_type"`
	StreamFunctionCalling string `json:"stream_function_calling"`
	VisionSupport         string `json:"vision_support"`
	StreamModeDelimiter   string `json:"stream_mode_delimiter"`
}

type SimpleTenant struct {
	ID   string `gorm:"primaryKey"`
	Name string
}

type TenantAccountView struct {
	ID          string `gorm:"primaryKey"`
	TenantID    string
	TenantName  string
	AccountID   string
	AccountName string
	Unit        string `json:"unit"`
	Department  string `json:"department"`
	Role        string
	InvitedBy   *string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type CurrentProvider struct {
	TenantId string `json:"tenant_id"`
	Provider string `json:"provider"`
	Label    struct {
		ZhHans string `json:"zh_Hans"`
		EnUS   string `json:"en_US"`
	} `json:"label"`
	Description struct {
		ZhHans string `json:"zh_Hans"`
		EnUS   string `json:"en_US"`
	} `json:"description"`
	IconSmall struct {
		ZhHans string `json:"zh_Hans"`
		EnUS   string `json:"en_US"`
	} `json:"icon_small"`
	IconLarge                interface{} `json:"icon_large"`
	Background               interface{} `json:"background"`
	Help                     interface{} `json:"help"`
	SupportedModelTypes      []string    `json:"supported_model_types"`
	ConfigurateMethods       []string    `json:"configurate_methods"`
	ProviderCredentialSchema interface{} `json:"provider_credential_schema"`
	ModelCredentialSchema    struct {
		Model struct {
			Label struct {
				ZhHans string `json:"zh_Hans"`
				EnUS   string `json:"en_US"`
			} `json:"label"`
			Placeholder struct {
				ZhHans string `json:"zh_Hans"`
				EnUS   string `json:"en_US"`
			} `json:"placeholder"`
		} `json:"model"`
		CredentialFormSchemas []struct {
			Variable string `json:"variable"`
			Label    struct {
				ZhHans string `json:"zh_Hans"`
				EnUS   string `json:"en_US"`
			} `json:"label"`
			Type     string  `json:"type"`
			Required bool    `json:"required"`
			Default  *string `json:"default"`
			Options  []struct {
				Label struct {
					ZhHans string `json:"zh_Hans"`
					EnUS   string `json:"en_US"`
				} `json:"label"`
				Value  string        `json:"value"`
				ShowOn []interface{} `json:"show_on"`
			} `json:"options"`
			Placeholder *struct {
				ZhHans string `json:"zh_Hans"`
				EnUS   string `json:"en_US"`
			} `json:"placeholder"`
			MaxLength int `json:"max_length"`
			ShowOn    []struct {
				Variable string `json:"variable"`
				Value    string `json:"value"`
			} `json:"show_on"`
		} `json:"credential_form_schemas"`
	} `json:"model_credential_schema"`
	PreferredProviderType string `json:"preferred_provider_type"`
	CustomConfiguration   struct {
		Status string `json:"status"`
	} `json:"custom_configuration"`
	SystemConfiguration struct {
		Enabled             bool          `json:"enabled"`
		CurrentQuotaType    interface{}   `json:"current_quota_type"`
		QuotaConfigurations []interface{} `json:"quota_configurations"`
	} `json:"system_configuration"`
}
