<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商洛市烟草专卖局规章制度答题系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .quiz-container {
            padding: 40px;
        }

        .question-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .question-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .question-number {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .question-text {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.6;
        }

        .options {
            margin: 25px 0;
        }

        .option {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .option:hover {
            border-color: #3498db;
            background: #f0f8ff;
        }

        .option input[type="radio"] {
            margin-right: 12px;
            transform: scale(1.2);
            pointer-events: none;
        }

        .option label {
            cursor: pointer;
            font-size: 16px;
            line-height: 1.5;
            flex: 1;
            pointer-events: none;
        }

        .option label[for*="_True"] {
            color: #27ae60;
            font-weight: 600;
        }

        .option label[for*="_False"] {
            color: #e74c3c;
            font-weight: 600;
        }

        .option input[type="radio"]:checked + label {
            font-weight: bold;
        }

        .option:has(input[type="radio"]:checked) {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .explanation {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .explanation-title {
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .explanation-text {
            color: #2c3e50;
            line-height: 1.6;
        }

        .controls {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(149, 165, 166, 0.4);
        }

        .result {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 30px 0;
            display: none;
            box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
        }

        .result.show {
            display: block;
            animation: slideIn 0.6s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        .result h2 {
            font-size: 24px;
            margin-bottom: 15px;
        }

        .result p {
            font-size: 18px;
            line-height: 1.6;
        }

        .progress-bar {
            background: #ecf0f1;
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #3498db, #2ecc71);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .quiz-container {
                padding: 20px;
            }
            
            .question-card {
                padding: 20px;
            }
            
            .question-text {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>商洛市烟草专卖局规章制度答题系统</h1>
            <p>测试您对规章制度的掌握程度</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="quiz-container">
            <form id="quizForm">
                <!-- 题目将通过JavaScript动态生成 -->
            </form>

            <div class="controls">
                <button type="button" class="btn" id="submitBtn">提交答案</button>
                <button type="button" class="btn btn-secondary" id="showAnswersBtn" style="display: none;">显示解析</button>
                <button type="button" class="btn btn-secondary" id="resetBtn" style="display: none;">重新答题</button>
            </div>

            <div class="result" id="result">
                <!-- 结果将通过JavaScript显示 -->
            </div>
        </div>
    </div>

    <script>
        // 题目数据
        const questions = [
            {
                "question_id": "Q001",
                "question_type": "multiple_choice",
                "question": "根据《商洛市烟草专卖局（公司）党组工作规则》，以下哪项属于市局（公司）党组讨论决定的重大事项？",
                "options": [
                    "A.基层单位日常采购安排",
                    "B.职工年度薪酬制度改革方案",
                    "C.员工文体活动经费审批",
                    "D.机关食堂一般性行政事务"
                ],
                "correct_answer": "B",
                "explanation": "【依据中共商洛市烟草专卖局（公司）党组工作规则第五条】市局（公司）党组负责研究全市系统薪酬调整、分配等事项，因此正确答案为B。"
            },
            {
                "question_id": "Q002",
                "question_type": "true_false",
                "question": "基层单位可以直接向市局（公司）局长（经理）个人报送请示公文。",
                "correct_answer": "False",
                "explanation": "【依据《市局工作规则》第九章第五十一条】除领导成员交办事项和保密事项外，基层单位不得直接向市局（公司）领导成员个人报送公文，需经办公室规范流转。"
            },
            {
                "question_id": "Q003",
                "question_type": "true_false",
                "question": "预算管理委员会会议通过的预算追加方案需经三分之二以上委员同意后生效。",
                "correct_answer": "True",
                "explanation": "【依据预算管理委员会议事规则第五条】预算委员会决议须经三分之二以上委员通过，且办公室需严格审核发文条件，因此判断正确。"
            }
        ];


        let currentAnswers = {};
        let isSubmitted = false;

        // 初始化答题系统
        function initQuiz() {
            const form = document.getElementById('quizForm');
            form.innerHTML = '';

            questions.forEach((q, index) => {
                const questionCard = createQuestionCard(q, index);
                form.appendChild(questionCard);
            });

            updateProgress();
        }

        // 创建题目卡片
        function createQuestionCard(question, index) {
            const card = document.createElement('div');
            card.className = 'question-card';

            let optionsHtml = '';

            if (question.question_type === 'multiple_choice') {
                // 选择题选项
                optionsHtml = question.options.map(option => `
                    <div class="option" onclick="selectOption('${question.question_id}', '${option[0]}')">
                        <input type="radio" id="${question.question_id}_${option[0]}"
                               name="${question.question_id}" value="${option[0]}">
                        <label for="${question.question_id}_${option[0]}">${option}</label>
                    </div>
                `).join('');
            } else if (question.question_type === 'true_false') {
                // 判断题选项
                optionsHtml = `
                    <div class="option" onclick="selectOption('${question.question_id}', 'True')">
                        <input type="radio" id="${question.question_id}_True"
                               name="${question.question_id}" value="True">
                        <label for="${question.question_id}_True">✓ 正确</label>
                    </div>
                    <div class="option" onclick="selectOption('${question.question_id}', 'False')">
                        <input type="radio" id="${question.question_id}_False"
                               name="${question.question_id}" value="False">
                        <label for="${question.question_id}_False">✗ 错误</label>
                    </div>
                `;
            }

            card.innerHTML = `
                <div class="question-header">
                    <div class="question-number">${index + 1}</div>
                    <div class="question-text">
                        ${question.question_type === 'true_false' ? '[判断题] ' : '[选择题] '}
                        ${question.question}
                    </div>
                </div>
                <div class="options">
                    ${optionsHtml}
                </div>
                <div class="explanation" id="explanation_${question.question_id}">
                    <div class="explanation-title">答案解析：</div>
                    <div class="explanation-text">${question.explanation}</div>
                </div>
            `;
            return card;
        }

        // 选择选项（点击整行）
        function selectOption(questionId, answer) {
            if (isSubmitted) return; // 如果已提交，不允许修改

            const radioButton = document.getElementById(`${questionId}_${answer}`);
            if (radioButton && !radioButton.disabled) {
                radioButton.checked = true;
                handleAnswerChange(questionId, answer);
            }
        }

        // 处理答案变化
        function handleAnswerChange(questionId, answer) {
            currentAnswers[questionId] = answer;
            updateProgress();
        }

        // 更新进度条
        function updateProgress() {
            const totalQuestions = questions.length;
            const answeredQuestions = Object.keys(currentAnswers).length;
            const progress = (answeredQuestions / totalQuestions) * 100;
            
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 提交答案
        function submitQuiz() {
            if (Object.keys(currentAnswers).length < questions.length) {
                alert('请完成所有题目后再提交！');
                return;
            }

            let correctCount = 0;
            questions.forEach(q => {
                if (currentAnswers[q.question_id] === q.correct_answer) {
                    correctCount++;
                }
            });

            const score = Math.round((correctCount / questions.length) * 100);
            const resultDiv = document.getElementById('result');
            
            let resultMessage = '';
            if (score >= 90) {
                resultMessage = '优秀！您对工作规则掌握得非常好！';
            } else if (score >= 80) {
                resultMessage = '良好！您对工作规则有较好的掌握。';
            } else if (score >= 60) {
                resultMessage = '及格！建议您进一步学习工作规则。';
            } else {
                resultMessage = '需要加强！请认真学习工作规则相关内容。';
            }

            resultDiv.innerHTML = `
                <h2>答题结果</h2>
                <p>您的得分：<strong>${score}分</strong></p>
                <p>正确题数：<strong>${correctCount}/${questions.length}</strong></p>
                <p>${resultMessage}</p>
            `;
            
            resultDiv.classList.add('show');
            isSubmitted = true;

            // 显示控制按钮
            document.getElementById('submitBtn').style.display = 'none';
            document.getElementById('showAnswersBtn').style.display = 'inline-block';
            document.getElementById('resetBtn').style.display = 'inline-block';

            // 禁用所有选项
            const inputs = document.querySelectorAll('input[type="radio"]');
            inputs.forEach(input => input.disabled = true);
        }

        // 显示解析
        function showAnswers() {
            questions.forEach(q => {
                const explanation = document.getElementById(`explanation_${q.question_id}`);
                explanation.classList.add('show');
                
                // 高亮正确答案
                const correctOption = document.querySelector(`input[name="${q.question_id}"][value="${q.correct_answer}"]`);
                if (correctOption) {
                    correctOption.parentElement.style.background = '#d4edda';
                    correctOption.parentElement.style.borderColor = '#28a745';
                }
                
                // 标记错误答案
                const userAnswer = currentAnswers[q.question_id];
                if (userAnswer && userAnswer !== q.correct_answer) {
                    const wrongOption = document.querySelector(`input[name="${q.question_id}"][value="${userAnswer}"]`);
                    if (wrongOption) {
                        wrongOption.parentElement.style.background = '#f8d7da';
                        wrongOption.parentElement.style.borderColor = '#dc3545';
                    }
                }
            });
            
            document.getElementById('showAnswersBtn').style.display = 'none';
        }

        // 重置答题
        function resetQuiz() {
            currentAnswers = {};
            isSubmitted = false;
            
            // 重置界面
            document.getElementById('result').classList.remove('show');
            document.getElementById('submitBtn').style.display = 'inline-block';
            document.getElementById('showAnswersBtn').style.display = 'none';
            document.getElementById('resetBtn').style.display = 'none';
            
            // 重新初始化
            initQuiz();
        }

        // 事件监听
        document.getElementById('submitBtn').addEventListener('click', submitQuiz);
        document.getElementById('showAnswersBtn').addEventListener('click', showAnswers);
        document.getElementById('resetBtn').addEventListener('click', resetQuiz);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initQuiz);
    </script>
</body>
</html>
