package database

import (
	"time"
	"difyserver/models"
	"gorm.io/gorm"
)

type UserDocumentDao struct {
	*gorm.DB
}

func GetUserDocumentDao() UserDocumentDao {
	return UserDocumentDao{DB: DB}
}

// GetAll 获取所有用户文档列表（不包含内容）
func (d UserDocumentDao) GetAll() ([]*models.UserDocumentListResponse, error) {
	var documents []*models.UserDocument
	if err := d.Select("id, doc_type, created_by, created_at, updated_at").Find(&documents).Error; err != nil {
		return nil, err
	}

	result := make([]*models.UserDocumentListResponse, len(documents))
	for i, doc := range documents {
		result[i] = &models.UserDocumentListResponse{
			ID:        doc.ID,
			DocType:   doc.DocType,
			CreatedBy: doc.CreatedBy,
			CreatedAt: doc.CreatedAt,
			UpdatedAt: doc.UpdatedAt,
		}
	}
	return result, nil
}

// GetByDocType 根据文档类型获取用户文档
func (d UserDocumentDao) GetByDocType(docType string) (*models.UserDocument, error) {
	var document models.UserDocument
	if err := d.Where("doc_type = ?", docType).First(&document).Error; err != nil {
		return nil, err
	}
	return &document, nil
}

// GetListByDocType 根据文档类型获取用户文档列表
func (d UserDocumentDao) GetListByDocType(docType string) ([]*models.UserDocumentResponse, error) {
	var documents []*models.UserDocument
	if err := d.Where("doc_type = ?", docType).Find(&documents).Error; err != nil {
		return nil, err
	}

	result := make([]*models.UserDocumentResponse, len(documents))
	for i, doc := range documents {
		result[i] = &models.UserDocumentResponse{
			ID:        doc.ID,
			DocType:   doc.DocType,
			Content:   doc.Content,
			CreatedBy: doc.CreatedBy,
			CreatedAt: doc.CreatedAt,
			UpdatedAt: doc.UpdatedAt,
		}
	}
	return result, nil
}

// GetByID 根据ID获取用户文档
func (d UserDocumentDao) GetByID(id int64) (*models.UserDocument, error) {
	var document models.UserDocument
	if err := d.Where("id = ?", id).First(&document).Error; err != nil {
		return nil, err
	}
	return &document, nil
}

// Create 创建用户文档
func (d UserDocumentDao) Create(document *models.UserDocument) error {
	return d.DB.Create(document).Error
}

// Update 更新用户文档
func (d UserDocumentDao) Update(document *models.UserDocument) error {
	return d.DB.Save(document).Error
}

// UpdateByID 根据ID更新用户文档内容
func (d UserDocumentDao) UpdateByID(id int64, content string) error {
	return d.DB.Model(&models.UserDocument{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"content":    content,
			"updated_at": time.Now(),
		}).Error
}

// UpdateByDocType 根据文档类型更新用户文档内容
func (d UserDocumentDao) UpdateByDocType(docType, content, updatedBy string) error {
	return d.DB.Model(&models.UserDocument{}).
		Where("doc_type = ?", docType).
		Updates(map[string]interface{}{
			"content":    content,
			"updated_at": time.Now(),
		}).Error
}

// Delete 删除用户文档
func (d UserDocumentDao) Delete(id int64) error {
	return d.DB.Delete(&models.UserDocument{}, id).Error
}

// DeleteByDocType 根据文档类型删除用户文档
func (d UserDocumentDao) DeleteByDocType(docType string) error {
	return d.DB.Where("doc_type = ?", docType).Delete(&models.UserDocument{}).Error
}

// Exists 检查用户文档类型是否存在
func (d UserDocumentDao) Exists(docType string) (bool, error) {
	var count int64
	err := d.DB.Model(&models.UserDocument{}).Where("doc_type = ?", docType).Count(&count).Error
	return count > 0, err
}
