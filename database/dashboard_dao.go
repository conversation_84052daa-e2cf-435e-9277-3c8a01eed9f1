package database

import (
	"time"

	"difyserver/models"

	"gorm.io/gorm"
)

type DashboardDao struct {
	*gorm.DB
}

func GetDashboardDao() DashboardDao {
	return DashboardDao{DB: DB}
}

// GetDashboardSummary 获取Dashboard总览数据（全局统计，不按租户隔离）
func (d DashboardDao) GetDashboardSummary(req *models.DashboardQueryRequest) (*models.DashboardSummary, error) {
	summary := &models.DashboardSummary{}

	// 获取总量统计
	if err := d.getTotalCounts(summary); err != nil {
		return nil, err
	}

	// 获取按日期统计的数据
	if err := d.getDateStatistics(summary, req); err != nil {
		return nil, err
	}

	// 获取每个app的统计数据
	if err := d.getAppStatistics(summary); err != nil {
		return nil, err
	}

	return summary, nil
}

// getTotalCounts 获取总量统计（全局统计）
func (d DashboardDao) getTotalCounts(summary *models.DashboardSummary) error {
	// 统计apps总数（所有租户）
	var appsCount int64
	if err := d.Model(&models.Apps{}).Count(&appsCount); err.Error != nil {
		return err.Error
	}
	summary.TotalApps = int(appsCount)

	// 统计accounts总数（所有账户）
	var accountCount int64
	if err := d.Model(&models.Account{}).Count(&accountCount); err.Error != nil {
		return err.Error
	}
	summary.TotalAccounts = int(accountCount)

	// 统计documents总数（documents + external_knowledge_bindings）
	var userDocCount, externalApiCount int64

	// 统计documents
	if err := d.Table("documents").Count(&userDocCount); err.Error != nil {
		return err.Error
	}

	// external_knowledge_bindings（通过关联datasets表）
	if err := d.Raw("SELECT COUNT(1) FROM external_knowledge_bindings JOIN datasets ON external_knowledge_bindings.dataset_id = datasets.id").Scan(&externalApiCount); err.Error != nil {
		return err.Error
	}

	summary.TotalDocuments = int(userDocCount + externalApiCount)

	// datasets
	var datasets int64
	if err := d.Table("datasets").Count(&datasets); err.Error != nil {
		return err.Error
	}
	summary.TotalDatasets = int(datasets)

	var messagesCount int64
	if err := d.Table("messages").Where("invoke_from = ?", "web-app").Count(&messagesCount); err.Error != nil {
		return err.Error
	}
	var workflowRunsCount int64
	if err := d.Table("workflow_runs").Where("type = ?", "workflow").Count(&workflowRunsCount); err.Error != nil {
		return err.Error
	}
	summary.TotalMessages = int(messagesCount) + int(workflowRunsCount)

	var anchorStatistics int64
	if err := d.Table("anchor_statistics").Select("COALESCE(SUM(action_count), 0)").Scan(&anchorStatistics); err.Error != nil {
		return err.Error
	}
	summary.TotalAnchorStatistics = int(anchorStatistics)

	return nil
}

// getDateStatistics 获取按日期统计的数据（全局统计）
func (d DashboardDao) getDateStatistics(summary *models.DashboardSummary, req *models.DashboardQueryRequest) error {
	// 设置默认日期范围（最近30天）
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30)

	if req.StartDate != "" {
		if parsed, err := time.Parse("2006-01-02", req.StartDate); err == nil {
			startDate = parsed
		}
	}
	if req.EndDate != "" {
		if parsed, err := time.Parse("2006-01-02", req.EndDate); err == nil {
			endDate = parsed
		}
	}

	// 获取messages统计
	messageStats, err := d.getMessageStatistics(req.StatType, startDate, endDate)
	if err != nil {
		return err
	}
	summary.MessageStatistics = messageStats

	// 获取anchor_statistics统计
	anchorStats, err := d.getAnchorStatisticsData(req.StatType, startDate, endDate)
	if err != nil {
		return err
	}
	summary.AnchorStatistics = anchorStats

	return nil
}

// getMessageStatistics 获取messages和workflow_runs按日期统计（全局统计）
func (d DashboardDao) getMessageStatistics(statType string, startDate, endDate time.Time) ([]models.DateStatistics, error) {
	var results []models.DateStatistics
	var query string

	if statType == "monthly" {
		query = `
			SELECT
				date,
				SUM(count) as count
			FROM (
				-- Messages统计（只统计web-app来源）
				SELECT
					TO_CHAR(created_at, 'YYYY-MM') as date,
					COUNT(*) as count
				FROM messages
				WHERE created_at >= ?
					AND created_at <= ?
					AND invoke_from = 'web-app'
				GROUP BY TO_CHAR(created_at, 'YYYY-MM')

				UNION ALL

				-- Workflow runs统计（只统计workflow类型）
				SELECT
					TO_CHAR(created_at, 'YYYY-MM') as date,
					COUNT(*) as count
				FROM workflow_runs
				WHERE created_at >= ?
					AND created_at <= ?
					AND type = 'workflow'
				GROUP BY TO_CHAR(created_at, 'YYYY-MM')
			) combined
			GROUP BY date
			ORDER BY date
		`
	} else {
		query = `
			SELECT
				date,
				SUM(count) as count
			FROM (
				-- Messages统计（只统计web-app来源）
				SELECT
					TO_CHAR(created_at, 'YYYY-MM-DD') as date,
					COUNT(*) as count
				FROM messages
				WHERE created_at >= ?
					AND created_at <= ?
					AND invoke_from = 'web-app'
				GROUP BY TO_CHAR(created_at, 'YYYY-MM-DD')

				UNION ALL

				-- Workflow runs统计（只统计workflow类型）
				SELECT
					TO_CHAR(created_at, 'YYYY-MM-DD') as date,
					COUNT(*) as count
				FROM workflow_runs
				WHERE created_at >= ?
					AND created_at <= ?
					AND type = 'workflow'
				GROUP BY TO_CHAR(created_at, 'YYYY-MM-DD')
			) combined
			GROUP BY date
			ORDER BY date
		`
	}

	if err := d.Raw(query, startDate, endDate, startDate, endDate).Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

// getAnchorStatisticsData 获取anchor_statistics按日期统计（全局统计）
func (d DashboardDao) getAnchorStatisticsData(statType string, startDate, endDate time.Time) ([]models.DateStatistics, error) {
	var results []models.DateStatistics
	var query string

	if statType == "monthly" {
		query = `
			SELECT
				TO_CHAR(record_date, 'YYYY-MM') as date,
				SUM(action_count) as count
			FROM anchor_statistics
			WHERE record_date >= ?
				AND record_date <= ?
			GROUP BY TO_CHAR(record_date, 'YYYY-MM')
			ORDER BY date
		`
	} else {
		query = `
			SELECT
				TO_CHAR(record_date, 'YYYY-MM-DD') as date,
				SUM(action_count) as count
			FROM anchor_statistics
			WHERE record_date >= ?
				AND record_date <= ?
			GROUP BY TO_CHAR(record_date, 'YYYY-MM-DD')
			ORDER BY date
		`
	}

	if err := d.Raw(query, startDate, endDate).Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

// getAppStatistics 获取每个app的点击数和对话消息数统计（全局统计）
func (d DashboardDao) getAppStatistics(summary *models.DashboardSummary) error {
	var appStats []models.AppStatistics

	// 查询每个app的点击数和消息数（包含workflow runs），按创建日期排序
	query := `
		SELECT
			a.id as app_id,
			a.name as app_name,
			COALESCE(clicks.click_count, 0) as click_count,
			COALESCE(messages.message_count, 0) + COALESCE(workflow_runs.workflow_runs_count, 0) as message_count
		FROM apps a
		LEFT JOIN (
			-- 统计每个app的点击数
			SELECT
				resource_id::uuid as app_id,
				SUM(action_count) as click_count
			FROM anchor_statistics
			WHERE resource_type = 'app'
				AND action_type = 'click'
			GROUP BY resource_id
		) clicks ON a.id = clicks.app_id
		LEFT JOIN (
			-- 统计每个app的消息数（直接从messages表统计）
			SELECT
				app_id::uuid as app_id,
				COUNT(*) as message_count
			FROM messages
			WHERE invoke_from = 'web-app'
				AND app_id IS NOT NULL
			GROUP BY app_id
		) messages ON a.id = messages.app_id
		LEFT JOIN (
			-- 统计每个app的workflow runs数（只统计workflow类型）
			SELECT
				app_id::uuid as app_id,
				COUNT(*) as workflow_runs_count
			FROM workflow_runs
			WHERE type = 'workflow'
				AND app_id IS NOT NULL
			GROUP BY app_id
		) workflow_runs ON a.id = workflow_runs.app_id
		ORDER BY a.created_at ASC
	`

	if err := d.Raw(query).Scan(&appStats).Error; err != nil {
		return err
	}

	summary.AppStatistics = appStats
	return nil
}
