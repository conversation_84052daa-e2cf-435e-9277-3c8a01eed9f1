package database

import (
	"fmt"
	"time"

	"difyserver/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// getResourceName 根据资源类型和ID获取资源名称
func getResourceName(resourceType, resourceID string, tenantID uuid.UUID) string {
	var name string

	switch resourceType {
	case models.ResourceTypeApp:
		// 查询应用名称
		err := DB.Table("apps").
			Select("name").
			Where("id = ? AND tenant_id = ?", resourceID, tenantID).
			Scan(&name).Error
		if err != nil {
			return "" // 如果查询失败，返回空字符串
		}

	case models.ResourceTypeDataset:
		// 查询数据集名称
		err := DB.Table("datasets").
			Select("name").
			Where("id = ? AND tenant_id = ?", resourceID, tenantID).
			Scan(&name).Error
		if err != nil {
			return ""
		}

	case models.ResourceTypeFeedback:
		// 反馈没有名称字段，使用反馈内容的前50个字符作为名称
		var content string
		err := DB.Table("feedbacks").
			Select("feedback_content").
			Where("id = ? AND tenant_id = ?", resourceID, tenantID).
			Scan(&content).Error
		if err != nil {
			return ""
		}
		// 截取前50个字符作为名称
		if len(content) > 50 {
			name = content[:50] + "..."
		} else {
			name = content
		}

	case models.ResourceTypeMenu:
		// 查询菜单名称
		err := DB.Table("system_menus").
			Select("title").
			Where("id = ?", resourceID).
			Scan(&name).Error
		if err != nil {
			return ""
		}

	case models.ResourceTypeUser:
		// 查询用户名称
		err := DB.Table("accounts").
			Select("name").
			Where("id = ?", resourceID).
			Scan(&name).Error
		if err != nil {
			return ""
		}

	case models.ResourceTypeTenant:
		// 查询租户名称
		err := DB.Table("tenants").
			Select("name").
			Where("id = ?", resourceID).
			Scan(&name).Error
		if err != nil {
			return ""
		}

	default:
		// 未知资源类型，返回空字符串
		return ""
	}

	return name
}

// RecordAnchorStatistics 记录锚点统计数据
func RecordAnchorStatistics(tenantID, userID uuid.UUID, resourceType, resourceID, actionType string) error {
	today := time.Now().Format("2006-01-02")

	// 获取资源名称
	resourceName := getResourceName(resourceType, resourceID, tenantID)

	// 使用 ON CONFLICT 来处理重复记录，如果存在则增加计数
	query := `
		INSERT INTO anchor_statistics (tenant_id, user_id, resource_type, resource_id, resource_name, action_type, action_count, record_date, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, 1, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		ON CONFLICT (tenant_id, user_id, resource_type, resource_id, action_type, record_date)
		DO UPDATE SET
			action_count = anchor_statistics.action_count + 1,
			resource_name = EXCLUDED.resource_name,
			updated_at = CURRENT_TIMESTAMP
	`

	return DB.Exec(query, tenantID, userID, resourceType, resourceID, resourceName, actionType, today).Error
}

// GetAnchorStatistics 获取锚点统计数据（分页）
func GetAnchorStatistics(req *models.AnchorStatisticsQueryRequest, tenantID uuid.UUID) (*models.AnchorStatisticsListResponse, error) {
	var statistics []models.AnchorStatisticsResponse
	var total int64
	
	// 构建基础查询
	query := DB.Table("anchor_statistics as a").
		Select("a.*, u.name as user_name").
		Joins("LEFT JOIN accounts u ON a.user_id = u.id").
		Where("a.tenant_id = ?", tenantID)
	
	// 添加过滤条件
	if req.ResourceType != "" {
		query = query.Where("a.resource_type = ?", req.ResourceType)
	}
	if req.ResourceID != "" {
		query = query.Where("a.resource_id = ?", req.ResourceID)
	}
	if req.ActionType != "" {
		query = query.Where("a.action_type = ?", req.ActionType)
	}
	if req.UserID != "" {
		userUUID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user_id format: %v", err)
		}
		query = query.Where("a.user_id = ?", userUUID)
	}
	if req.StartDate != "" {
		query = query.Where("a.record_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("a.record_date <= ?", req.EndDate)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}
	
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	
	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("a.record_date DESC, a.created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&statistics).Error; err != nil {
		return nil, err
	}
	
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	
	return &models.AnchorStatisticsListResponse{
		Data:       statistics,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetAnchorStatisticsAggregate 获取聚合统计数据
func GetAnchorStatisticsAggregate(req *models.AnchorStatisticsQueryRequest, tenantID uuid.UUID) (*models.AnchorStatisticsAggregateListResponse, error) {
	var aggregates []models.AnchorStatisticsAggregateResponse
	var total int64
	
	// 根据 group_by 参数确定时间格式
	var dateFormat string
	switch req.GroupBy {
	case "month":
		dateFormat = "TO_CHAR(record_date, 'YYYY-MM')"
	case "year":
		dateFormat = "TO_CHAR(record_date, 'YYYY')"
	default: // day
		dateFormat = "TO_CHAR(record_date, 'YYYY-MM-DD')"
	}
	
	// 构建聚合查询
	selectFields := fmt.Sprintf(`
		%s as period,
		resource_type,
		resource_id,
		resource_name,
		action_type,
		SUM(action_count) as total_count,
		COUNT(DISTINCT user_id) as user_count
	`, dateFormat)
	
	query := DB.Table("anchor_statistics").
		Select(selectFields).
		Where("tenant_id = ?", tenantID)
	
	// 添加过滤条件
	if req.ResourceType != "" {
		query = query.Where("resource_type = ?", req.ResourceType)
	}
	if req.ResourceID != "" {
		query = query.Where("resource_id = ?", req.ResourceID)
	}
	if req.ActionType != "" {
		query = query.Where("action_type = ?", req.ActionType)
	}
	if req.UserID != "" {
		userUUID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user_id format: %v", err)
		}
		query = query.Where("user_id = ?", userUUID)
	}
	if req.StartDate != "" {
		query = query.Where("record_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("record_date <= ?", req.EndDate)
	}
	
	// 分组
	query = query.Group("period, resource_type, resource_id, resource_name, action_type")
	
	// 简化总数计算，直接使用原查询的计数
	var countQuery = DB.Table("anchor_statistics").
		Where("tenant_id = ?", tenantID)

	// 添加相同的过滤条件
	if req.ResourceType != "" {
		countQuery = countQuery.Where("resource_type = ?", req.ResourceType)
	}
	if req.ResourceID != "" {
		countQuery = countQuery.Where("resource_id = ?", req.ResourceID)
	}
	if req.ActionType != "" {
		countQuery = countQuery.Where("action_type = ?", req.ActionType)
	}
	if req.UserID != "" {
		userUUID, err := uuid.Parse(req.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user_id format: %v", err)
		}
		countQuery = countQuery.Where("user_id = ?", userUUID)
	}
	if req.StartDate != "" {
		countQuery = countQuery.Where("record_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		countQuery = countQuery.Where("record_date <= ?", req.EndDate)
	}

	// 计算分组后的总数
	countSelectFields := fmt.Sprintf(`
		%s as period,
		resource_type,
		resource_id,
		resource_name,
		action_type
	`, dateFormat)

	var countResults []struct{}
	if err := countQuery.Select(countSelectFields).
		Group("period, resource_type, resource_id, resource_name, action_type").
		Find(&countResults).Error; err != nil {
		return nil, err
	}
	total = int64(len(countResults))
	
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	
	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("period DESC, total_count DESC").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&aggregates).Error; err != nil {
		return nil, err
	}
	
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	
	return &models.AnchorStatisticsAggregateListResponse{
		Data:       aggregates,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetAnchorStatisticsByID 根据ID获取单条统计记录
func GetAnchorStatisticsByID(id int64, tenantID uuid.UUID) (*models.AnchorStatisticsResponse, error) {
	var statistic models.AnchorStatisticsResponse
	
	query := `
		SELECT a.*, u.name as user_name 
		FROM anchor_statistics a
		LEFT JOIN accounts u ON a.user_id = u.id
		WHERE a.id = ? AND a.tenant_id = ?
	`
	
	if err := DB.Raw(query, id, tenantID).Scan(&statistic).Error; err != nil {
		return nil, err
	}
	
	return &statistic, nil
}

// DeleteAnchorStatistics 删除锚点统计记录
func DeleteAnchorStatistics(id int64, tenantID uuid.UUID) error {
	result := DB.Where("id = ? AND tenant_id = ?", id, tenantID).Delete(&models.AnchorStatistics{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// GetAnchorStatisticsAnalysis 获取分析统计数据（按步长汇总过去N天）
func GetAnchorStatisticsAnalysis(req *models.AnchorStatisticsAnalysisRequest, tenantID uuid.UUID) (*models.AnchorStatisticsAnalysisListResponse, error) {
	// 计算日期范围
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -req.Days+1) // 包含今天，所以是-days+1

	// 按步长分组计算
	var periodResults []models.AnchorStatisticsAnalysisResponse
	currentStart := startDate

	for currentStart.Before(endDate) || currentStart.Equal(endDate) {
		// 计算当前周期的结束日期
		currentEnd := currentStart.AddDate(0, 0, req.Step-1)
		if currentEnd.After(endDate) {
			currentEnd = endDate
		}

		// 查询当前周期的统计数据
		var totalCount int64
		var userCount int64

		var query string
		var queryArgs []interface{}

		if req.ResourceID != "" {
			// 如果指定了resource_id，则按具体资源查询
			query = `
				SELECT
					COALESCE(SUM(action_count), 0) as total_count,
					COUNT(DISTINCT user_id) as user_count
				FROM anchor_statistics
				WHERE tenant_id = ?
					AND resource_type = ?
					AND resource_id = ?
					AND action_type = ?
					AND record_date >= ?
					AND record_date <= ?
			`
			queryArgs = []interface{}{tenantID, req.ResourceType, req.ResourceID, req.ActionType,
				currentStart.Format("2006-01-02"), currentEnd.Format("2006-01-02")}
		} else {
			// 如果没有指定resource_id，则按资源类型查询所有资源
			query = `
				SELECT
					COALESCE(SUM(action_count), 0) as total_count,
					COUNT(DISTINCT user_id) as user_count
				FROM anchor_statistics
				WHERE tenant_id = ?
					AND resource_type = ?
					AND action_type = ?
					AND record_date >= ?
					AND record_date <= ?
			`
			queryArgs = []interface{}{tenantID, req.ResourceType, req.ActionType,
				currentStart.Format("2006-01-02"), currentEnd.Format("2006-01-02")}
		}

		var result struct {
			TotalCount int64 `json:"total_count"`
			UserCount  int64 `json:"user_count"`
		}

		if err := DB.Raw(query, queryArgs...).Scan(&result).Error; err != nil {
			return nil, err
		}

		totalCount = result.TotalCount
		userCount = result.UserCount

		// 添加到结果中
		periodResults = append(periodResults, models.AnchorStatisticsAnalysisResponse{
			StartDate:    currentStart.Format("2006-01-02"),
			EndDate:      currentEnd.Format("2006-01-02"),
			TotalCount:   totalCount,
			UserCount:    userCount,
			ResourceType: req.ResourceType,
			ActionType:   req.ActionType,
		})

		// 移动到下一个周期
		currentStart = currentEnd.AddDate(0, 0, 1)
	}

	// 计算汇总信息
	var summary models.AnchorStatisticsAnalysisSummary
	var totalCount int64
	var maxCount int64 = 0
	var minCount int64 = -1

	for _, period := range periodResults {
		totalCount += period.TotalCount

		if period.TotalCount > maxCount {
			maxCount = period.TotalCount
		}

		if minCount == -1 || period.TotalCount < minCount {
			minCount = period.TotalCount
		}
	}

	// 计算总用户数（需要去重）
	var userQuery string
	var userQueryArgs []interface{}

	if req.ResourceID != "" {
		// 如果指定了resource_id，则按具体资源查询
		userQuery = `
			SELECT DISTINCT user_id
			FROM anchor_statistics
			WHERE tenant_id = ?
				AND resource_type = ?
				AND resource_id = ?
				AND action_type = ?
				AND record_date >= ?
				AND record_date <= ?
		`
		userQueryArgs = []interface{}{tenantID, req.ResourceType, req.ResourceID, req.ActionType,
			startDate.Format("2006-01-02"), endDate.Format("2006-01-02")}
	} else {
		// 如果没有指定resource_id，则按资源类型查询所有资源
		userQuery = `
			SELECT DISTINCT user_id
			FROM anchor_statistics
			WHERE tenant_id = ?
				AND resource_type = ?
				AND action_type = ?
				AND record_date >= ?
				AND record_date <= ?
		`
		userQueryArgs = []interface{}{tenantID, req.ResourceType, req.ActionType,
			startDate.Format("2006-01-02"), endDate.Format("2006-01-02")}
	}

	var userIDs []uuid.UUID
	if err := DB.Raw(userQuery, userQueryArgs...).Scan(&userIDs).Error; err != nil {
		return nil, err
	}

	summary.TotalCount = totalCount
	summary.TotalUserCount = int64(len(userIDs))
	summary.MaxPeriodCount = maxCount
	summary.MinPeriodCount = minCount

	if len(periodResults) > 0 {
		summary.AveragePerPeriod = float64(totalCount) / float64(len(periodResults))
	}

	return &models.AnchorStatisticsAnalysisListResponse{
		Data:         periodResults,
		ResourceType: req.ResourceType,
		ActionType:   req.ActionType,
		TotalDays:    req.Days,
		StepDays:     req.Step,
		Summary:      summary,
	}, nil
}

// GetAnchorStatisticsResourceSummary 获取资源汇总统计数据
func GetAnchorStatisticsResourceSummary(req *models.AnchorStatisticsResourceSummaryRequest, tenantID uuid.UUID) (models.AnchorStatisticsResourceSummaryResponse, error) {
	// 查询所有资源的统计数据
	query := `
		SELECT
			resource_id,
			resource_name,
			SUM(action_count) as total_count,
			COUNT(DISTINCT user_id) as user_count
		FROM anchor_statistics
		WHERE tenant_id = ?
			AND resource_type = ?
			AND action_type = ?
		GROUP BY resource_id, resource_name
		ORDER BY total_count DESC
	`

	var results []struct {
		ResourceID   string `json:"resource_id"`
		ResourceName string `json:"resource_name"`
		TotalCount   int64  `json:"total_count"`
		UserCount    int64  `json:"user_count"`
	}

	if err := DB.Raw(query, tenantID, req.ResourceType, req.ActionType).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 构建以resource_id为key的map响应
	response := make(models.AnchorStatisticsResourceSummaryResponse)

	for _, result := range results {
		response[result.ResourceID] = models.AnchorStatisticsResourceSummaryItem{
			ResourceType: req.ResourceType,
			ResourceID:   result.ResourceID,
			ResourceName: result.ResourceName,
			ActionType:   req.ActionType,
			TotalCount:   result.TotalCount,
			UserCount:    result.UserCount,
		}
	}

	return response, nil
}
