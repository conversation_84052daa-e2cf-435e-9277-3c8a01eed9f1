package database

import (
	"math"
	"time"

	"difyserver/models"
	"difyserver/view"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type QuizDao struct {
	*gorm.DB
}

func GetQuizDao() QuizDao {
	return QuizDao{DB: DB}
}

// SubmitQuiz 提交答题记录
func (d QuizDao) SubmitQuiz(submission *models.QuizSubmission) error {
	if err := d.Create(submission).Error; err != nil {
		return err
	}
	
	// 更新统计信息
	return d.UpdateQuizStatistics(submission.QuizUUID, submission.QuizTitle)
}

// GetQuizSubmissions 获取指定quiz的所有提交记录
func (d QuizDao) GetQuizSubmissions(quizUUID uuid.UUID, page, pageSize int) ([]models.QuizSubmission, int64, error) {
	var submissions []models.QuizSubmission
	var total int64
	
	query := d.Where("quiz_uuid = ?", quizUUID)
	
	// 获取总数
	if err := query.Model(&models.QuizSubmission{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("submitted_at DESC").Offset(offset).Limit(pageSize).Find(&submissions).Error; err != nil {
		return nil, 0, err
	}
	
	return submissions, total, nil
}

// GetQuizStatistics 获取指定quiz的统计信息
func (d QuizDao) GetQuizStatistics(quizUUID uuid.UUID) (*models.QuizStatistics, error) {
	var stats models.QuizStatistics
	if err := d.Where("quiz_uuid = ?", quizUUID).First(&stats).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 返回nil表示没有统计数据
		}
		return nil, err
	}
	return &stats, nil
}

// UpdateQuizStatistics 更新quiz统计信息
func (d QuizDao) UpdateQuizStatistics(quizUUID uuid.UUID, quizTitle string) error {
	// 计算统计数据
	var totalSubmissions int64
	var avgScore, highestScore, lowestScore float64
	var lastSubmissionAt time.Time
	
	// 获取总提交数
	if err := d.Model(&models.QuizSubmission{}).Where("quiz_uuid = ?", quizUUID).Count(&totalSubmissions).Error; err != nil {
		return err
	}
	
	if totalSubmissions == 0 {
		return nil // 没有提交记录，不需要更新统计
	}
	
	// 获取分数统计
	var scoreStats struct {
		AvgScore     float64
		MaxScore     int
		MinScore     int
		LastSubmitAt time.Time
	}
	
	if err := d.Model(&models.QuizSubmission{}).
		Select("AVG(score) as avg_score, MAX(score) as max_score, MIN(score) as min_score, MAX(submitted_at) as last_submit_at").
		Where("quiz_uuid = ?", quizUUID).
		Scan(&scoreStats).Error; err != nil {
		return err
	}
	
	avgScore = scoreStats.AvgScore
	highestScore = float64(scoreStats.MaxScore)
	lowestScore = float64(scoreStats.MinScore)
	lastSubmissionAt = scoreStats.LastSubmitAt
	
	// 计算及格率（假设60分及格）
	var passCount int64
	if err := d.Model(&models.QuizSubmission{}).Where("quiz_uuid = ? AND score >= 60", quizUUID).Count(&passCount).Error; err != nil {
		return err
	}
	
	passRate := float64(passCount) / float64(totalSubmissions) * 100
	
	// 更新或创建统计记录
	now := time.Now()
	stats := models.QuizStatistics{
		QuizUUID:          quizUUID,
		QuizTitle:         quizTitle,
		TotalSubmissions:  int(totalSubmissions),
		AverageScore:      math.Round(avgScore*100) / 100, // 保留两位小数
		HighestScore:      int(highestScore),
		LowestScore:       int(lowestScore),
		PassRate:          math.Round(passRate*100) / 100,
		LastSubmissionAt:  lastSubmissionAt,
		UpdatedAt:         now,
	}

	// 使用UPSERT操作 - 如果不存在则创建，存在则更新
	var existingStats models.QuizStatistics
	result := d.Where("quiz_uuid = ?", quizUUID).First(&existingStats)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 记录不存在，创建新记录
			stats.CreatedAt = now
			return d.Create(&stats).Error
		}
		return result.Error
	}

	// 记录存在，更新现有记录
	return d.Model(&existingStats).Updates(stats).Error
}

// GetBatchStatistics 获取批次统计信息
func (d QuizDao) GetBatchStatistics(batchTitle string) ([]models.QuizStatistics, error) {
	var stats []models.QuizStatistics
	if err := d.Where("quiz_title = ?", batchTitle).Order("created_at ASC").Find(&stats).Error; err != nil {
		return nil, err
	}
	return stats, nil
}

// GetAllBatchTitles 获取所有批次标题
func (d QuizDao) GetAllBatchTitles() ([]string, error) {
	var titles []string
	if err := d.Model(&models.QuizStatistics{}).Distinct("quiz_title").Pluck("quiz_title", &titles).Error; err != nil {
		return nil, err
	}
	return titles, nil
}

// GetScoreDistribution 获取分数分布
func (d QuizDao) GetScoreDistribution(quizUUID uuid.UUID) (map[string]int, error) {
	distribution := make(map[string]int)
	
	// 定义分数段
	ranges := []struct {
		label string
		min   int
		max   int
	}{
		{"0-20", 0, 20},
		{"21-40", 21, 40},
		{"41-60", 41, 60},
		{"61-80", 61, 80},
		{"81-100", 81, 100},
	}
	
	for _, r := range ranges {
		var count int64
		if err := d.Model(&models.QuizSubmission{}).
			Where("quiz_uuid = ? AND score >= ? AND score <= ?", quizUUID, r.min, r.max).
			Count(&count).Error; err != nil {
			return nil, err
		}
		distribution[r.label] = int(count)
	}
	
	return distribution, nil
}

// GetUserQuizStatistics 获取用户在指定题目的答题统计
func (d QuizDao) GetUserQuizStatistics(quizUUID uuid.UUID, userID *uuid.UUID) (*models.QuizSubmission, error) {
	var submission models.QuizSubmission
	query := d.Where("quiz_uuid = ?", quizUUID)

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	// 获取最新的提交记录
	if err := query.Order("submitted_at DESC").First(&submission).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &submission, nil
}

// GetUserBatchStatistics 获取用户在指定批次的答题统计
func (d QuizDao) GetUserBatchStatistics(batchTitle string, userID *uuid.UUID) ([]models.QuizSubmission, error) {
	var submissions []models.QuizSubmission
	query := d.Where("quiz_title = ?", batchTitle)

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if err := query.Order("submitted_at DESC").Find(&submissions).Error; err != nil {
		return nil, err
	}

	return submissions, nil
}

// CheckUserAnswered 检查用户是否已经答过指定题目
func (d QuizDao) CheckUserAnswered(quizUUID uuid.UUID, userID *uuid.UUID) (bool, error) {
	var count int64
	query := d.Model(&models.QuizSubmission{}).Where("quiz_uuid = ?", quizUUID)

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if err := query.Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetUserQuizAttempts 获取用户在指定题目的所有答题记录
func (d QuizDao) GetUserQuizAttempts(quizUUID uuid.UUID, userID *uuid.UUID) ([]models.QuizSubmission, error) {
	var submissions []models.QuizSubmission
	query := d.Where("quiz_uuid = ?", quizUUID)

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	if err := query.Order("submitted_at ASC").Find(&submissions).Error; err != nil {
		return nil, err
	}

	return submissions, nil
}

// GetBatchSubmissions 获取批次下所有提交记录
func (d QuizDao) GetBatchSubmissions(batchTitle string) ([]models.QuizSubmission, error) {
	var submissions []models.QuizSubmission

	// 查询指定批次的所有提交记录，按提交时间排序
	if err := d.Where("quiz_title = ?", batchTitle).
		Order("submitted_at ASC").
		Find(&submissions).Error; err != nil {
		return nil, err
	}

	return submissions, nil
}

// GetQuizRecentSubmissions 获取题目的最近提交记录
func (d QuizDao) GetQuizRecentSubmissions(quizUUID uuid.UUID, limit int) ([]view.SubmissionSummary, error) {
	var submissions []models.QuizSubmission

	// 查询指定题目的最近提交记录
	if err := d.Where("quiz_uuid = ?", quizUUID).
		Order("submitted_at DESC").
		Limit(limit).
		Find(&submissions).Error; err != nil {
		return nil, err
	}

	// 转换为SubmissionSummary
	var summaries []view.SubmissionSummary
	for _, submission := range submissions {
		// 解析用户信息
		var userInfo map[string]interface{}
		if submission.UserInfo != nil {
			userInfo = submission.UserInfo
		}

		summaries = append(summaries, view.SubmissionSummary{
			SubmissionID: submission.ID,
			Score:        submission.Score,
			Percentage:   submission.Percentage,
			SubmittedAt:  submission.SubmittedAt,
			UserInfo:     userInfo,
		})
	}

	return summaries, nil
}

// DeleteSubmissionsByQuizUUID 根据题目UUID删除所有答题记录
func (d QuizDao) DeleteSubmissionsByQuizUUID(quizUUID uuid.UUID) (int, error) {
	result := d.Where("quiz_uuid = ?", quizUUID).Delete(&models.QuizSubmission{})
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}
