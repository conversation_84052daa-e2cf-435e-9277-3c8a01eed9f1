package database

var sql_system_menus_table_init = `
--- system_menus table init
CREATE TABLE system_menus (
                              id BIGSERIAL PRIMARY KEY,
                              pid BIGINT,
                              title VARCHAR(255) NOT NULL,
                              icon VARCHAR(255),
                              index VARCHAR(255),
                              order_num integer default 999,
                              permiss VARCHAR(255),
                              permission_id BIGINT,
                              created_at TIMESTAMP,
                              updated_at TIMESTAMP,
                              CONSTRAINT system_menus_pid_foreign FOREIGN KEY (pid) REFERENCES system_menus (id) ON DELETE CASCADE
);

-- 添加索引
CREATE INDEX system_menus_pid_idx ON system_menus (pid);
CREATE INDEX system_menus_permission_id_idx ON system_menus (permission_id);

-- 添加列注释
COMMENT ON COLUMN system_menus.pid IS '父级菜单ID';
COMMENT ON COLUMN system_menus.title IS '菜单名称';
COMMENT ON COLUMN system_menus.icon IS '菜单图标';
COMMENT ON COLUMN system_menus.index IS '路由路径';
COMMENT ON COLUMN system_menus.permiss IS '权限标识';
COMMENT ON COLUMN system_menus.permission_id IS '权限标识';

ALTER SEQUENCE system_menus_id_seq RESTART WITH 13;

INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (1, null, '我的应用', 'Platform', '/myAgents', 1, 'editor', null, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (2, null, '我的知识库', 'DocumentCopy', '/knowledges', 2, 'editor', null, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (3, null, '我的反馈', 'Setting', '/myFeedbacks', 3, 'normal', null, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (4, null, 'AI 管理', 'Setting', '4', 4, 'admin', 1, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (5, null, '系统管理', 'Setting', '5', 5, 'editor', 1, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (11, null, '反馈管理', 'User', '/feedbacks', 6, 'admin', 1, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (6, 4, '机器人管理', 'Robot', '/agent', 41, 'admin', 1, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (7, 4, '知识库管理', 'Book', '/knowledgeManagement', 42, 'admin', 1, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (8, 5, '角色管理', 'ShieldCheck', '/system-role', 51, 'editor', 1, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (10, 5, '用户管理', 'User', '/system-user', 52, 'owner', 1, '2025-05-16 09:00:02.000000', '2025-05-16 09:00:02.000000');
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (14, 5, '团队管理', 'Tenant', '/tenants', 53, 'owner', 1, null, null);
INSERT INTO public.system_menus (id, pid, title, icon, index, order_num, permiss, permission_id, created_at, updated_at) VALUES (13, 5, '团员管理', 'TenantUser', '/tenant-user', 54, 'admin', 1, null, null);


`

var sql_system_feedback_table_init = `
--- system_feedback table init
 create table public.feedbacks
(
    id              uuid         default uuid_generate_v4()  primary key,
    tenant_id        uuid                                              not null,
    user_id          uuid                                      not null,
    feedback_content text                                              not null,
    response         text,
    status           varchar(255) default 'pending'::character varying not null,
    created_at       timestamp    default CURRENT_TIMESTAMP,
    updated_at       timestamp    default CURRENT_TIMESTAMP
);

comment on column public.feedbacks.feedback_content is '反馈内容';

comment on column public.feedbacks.response is '管理员回答内容';

comment on column public.feedbacks.status is '反馈状态：待回答/已回答';

`

var sql_accounts_update_colum_0530 = `
alter table accounts
    add unit varchar(256);

comment on column accounts.unit is '单位';

alter table accounts
    add department varchar(256);

comment on column accounts.department is '部门';
`

var sql_anchor_statistics_table_init = `
--- anchor_statistics table init
CREATE TABLE anchor_statistics (
    id BIGSERIAL PRIMARY KEY,
    tenant_id UUID NOT NULL,
    user_id UUID NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255) NOT NULL,
    action_type VARCHAR(100) NOT NULL,
    action_count INTEGER DEFAULT 1,
    record_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引
CREATE INDEX anchor_statistics_tenant_id_idx ON anchor_statistics (tenant_id);
CREATE INDEX anchor_statistics_user_id_idx ON anchor_statistics (user_id);
CREATE INDEX anchor_statistics_resource_type_idx ON anchor_statistics (resource_type);
CREATE INDEX anchor_statistics_resource_id_idx ON anchor_statistics (resource_id);
CREATE INDEX anchor_statistics_action_type_idx ON anchor_statistics (action_type);
CREATE INDEX anchor_statistics_record_date_idx ON anchor_statistics (record_date);
CREATE INDEX anchor_statistics_tenant_date_idx ON anchor_statistics (tenant_id, record_date);
CREATE INDEX anchor_statistics_user_date_idx ON anchor_statistics (user_id, record_date);
CREATE INDEX anchor_statistics_resource_date_idx ON anchor_statistics (resource_type, resource_id, record_date);

-- 添加唯一约束，确保同一天同一用户对同一资源的同一操作只有一条记录
CREATE UNIQUE INDEX anchor_statistics_unique_idx ON anchor_statistics (tenant_id, user_id, resource_type, resource_id, action_type, record_date);

-- 添加列注释
COMMENT ON COLUMN anchor_statistics.tenant_id IS '租户ID';
COMMENT ON COLUMN anchor_statistics.user_id IS '用户ID';
COMMENT ON COLUMN anchor_statistics.resource_type IS '资源类型（如：app, dataset, feedback等）';
COMMENT ON COLUMN anchor_statistics.resource_id IS '资源ID';
COMMENT ON COLUMN anchor_statistics.action_type IS '操作类型（如：click, view, create, edit等）';
COMMENT ON COLUMN anchor_statistics.action_count IS '操作次数';
COMMENT ON COLUMN anchor_statistics.record_date IS '记录日期（按天统计）';
COMMENT ON COLUMN anchor_statistics.created_at IS '创建时间';
COMMENT ON COLUMN anchor_statistics.updated_at IS '更新时间';

-- 添加表注释
COMMENT ON TABLE anchor_statistics IS '锚点统计表，用于记录用户对各种资源的操作统计';
`

var sql_anchor_statistics_add_resource_name = `
-- 添加 resource_name 字段到 anchor_statistics 表
ALTER TABLE anchor_statistics
ADD COLUMN resource_name VARCHAR(500);

-- 添加列注释
COMMENT ON COLUMN anchor_statistics.resource_name IS '资源名称（用于展示）';

-- 添加索引
CREATE INDEX anchor_statistics_resource_name_idx ON anchor_statistics (resource_name);
`

var sql_apps_add_only_me_column = `
ALTER TABLE apps
ADD COLUMN only_me BOOLEAN DEFAULT True;
`

var sql_user_documents_table_init = `
--- user_documents table init
CREATE TABLE user_documents (
    id BIGSERIAL PRIMARY KEY,
    doc_type VARCHAR(255) NOT NULL UNIQUE,
    content TEXT NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_user_documents_doc_type ON user_documents(doc_type);
CREATE INDEX idx_user_documents_created_by ON user_documents(created_by);

-- 添加列注释
COMMENT ON COLUMN user_documents.doc_type IS '文档类型（唯一）';
COMMENT ON COLUMN user_documents.content IS '文档内容';
COMMENT ON COLUMN user_documents.created_by IS '创建人邮箱';
COMMENT ON COLUMN user_documents.created_at IS '创建时间';
COMMENT ON COLUMN user_documents.updated_at IS '更新时间';

-- 添加表注释
COMMENT ON TABLE user_documents IS '用户文档记录表，用于存储各种类型的文档内容';

INSERT INTO public.user_documents ( doc_type, content, created_by, created_at, updated_at) VALUES ('User Manual', '# 用户手册', '<EMAIL>', '2025-06-19 14:11:04.000000', '2025-06-19 14:11:09.000000');
`

var sql_temp_data_table_init = `
--- temp_data table init
CREATE TABLE temp_data (
    uuid UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_temp_data_created_at ON temp_data(created_at);

-- 添加列注释
COMMENT ON COLUMN temp_data.uuid IS '主键UUID';
COMMENT ON COLUMN temp_data.content IS '临时数据内容';
COMMENT ON COLUMN temp_data.created_at IS '创建时间';

-- 添加表注释
COMMENT ON TABLE temp_data IS '临时数据记录表，用于存储临时数据';
`

var sql_user_documents_remove_unique_constraint = `
-- 移除 user_documents 表中 doc_type 字段的唯一约束
ALTER TABLE user_documents DROP CONSTRAINT IF EXISTS user_documents_doc_type_key;

-- 更新列注释，移除唯一性说明
COMMENT ON COLUMN user_documents.doc_type IS '文档类型';
`

var sql_quiz_submissions_table_init = `
--- quiz_submissions table init
CREATE TABLE quiz_submissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    quiz_uuid UUID NOT NULL,
    quiz_title VARCHAR(255) NOT NULL,
    user_id UUID,
    session_id VARCHAR(255),
    user_answers JSONB NOT NULL,
    score INTEGER NOT NULL DEFAULT 0,
    total_questions INTEGER NOT NULL DEFAULT 0,
    correct_answers INTEGER NOT NULL DEFAULT 0,
    percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    user_info JSONB,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_uuid) REFERENCES temp_data(uuid) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_quiz_submissions_quiz_uuid ON quiz_submissions (quiz_uuid);
CREATE INDEX idx_quiz_submissions_quiz_title ON quiz_submissions (quiz_title);
CREATE INDEX idx_quiz_submissions_user_id ON quiz_submissions (user_id);
CREATE INDEX idx_quiz_submissions_session_id ON quiz_submissions (session_id);
CREATE INDEX idx_quiz_submissions_submitted_at ON quiz_submissions (submitted_at);

-- 添加列注释
COMMENT ON COLUMN quiz_submissions.id IS '提交记录ID';
COMMENT ON COLUMN quiz_submissions.quiz_uuid IS '关联的题目UUID';
COMMENT ON COLUMN quiz_submissions.quiz_title IS '批次标题';
COMMENT ON COLUMN quiz_submissions.user_id IS '用户ID（可选）';
COMMENT ON COLUMN quiz_submissions.session_id IS '会话ID';
COMMENT ON COLUMN quiz_submissions.user_answers IS '用户答案JSON';
COMMENT ON COLUMN quiz_submissions.score IS '得分';
COMMENT ON COLUMN quiz_submissions.total_questions IS '总题数';
COMMENT ON COLUMN quiz_submissions.correct_answers IS '正确答案数';
COMMENT ON COLUMN quiz_submissions.percentage IS '得分百分比';
COMMENT ON COLUMN quiz_submissions.user_info IS '用户信息JSON';
COMMENT ON COLUMN quiz_submissions.submitted_at IS '提交时间';

-- 添加表注释
COMMENT ON TABLE quiz_submissions IS '答题记录表';
`

var sql_quiz_statistics_table_init = `
--- quiz_statistics table init
CREATE TABLE quiz_statistics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    quiz_uuid UUID NOT NULL UNIQUE,
    quiz_title VARCHAR(255) NOT NULL,
    total_submissions INTEGER NOT NULL DEFAULT 0,
    average_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    highest_score INTEGER NOT NULL DEFAULT 0,
    lowest_score INTEGER NOT NULL DEFAULT 0,
    pass_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    last_submission_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quiz_uuid) REFERENCES temp_data(uuid) ON DELETE CASCADE
);

-- 创建索引
CREATE UNIQUE INDEX idx_quiz_statistics_quiz_uuid ON quiz_statistics (quiz_uuid);
CREATE INDEX idx_quiz_statistics_quiz_title ON quiz_statistics (quiz_title);

-- 添加列注释
COMMENT ON COLUMN quiz_statistics.id IS '统计记录ID';
COMMENT ON COLUMN quiz_statistics.quiz_uuid IS '关联的题目UUID';
COMMENT ON COLUMN quiz_statistics.quiz_title IS '批次标题';
COMMENT ON COLUMN quiz_statistics.total_submissions IS '总提交数';
COMMENT ON COLUMN quiz_statistics.average_score IS '平均分';
COMMENT ON COLUMN quiz_statistics.highest_score IS '最高分';
COMMENT ON COLUMN quiz_statistics.lowest_score IS '最低分';
COMMENT ON COLUMN quiz_statistics.pass_rate IS '及格率';
COMMENT ON COLUMN quiz_statistics.last_submission_at IS '最后提交时间';

-- 添加表注释
COMMENT ON TABLE quiz_statistics IS '答题统计表';
`
