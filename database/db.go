package database

import (
	"context"
	"fmt"
	"log"

	"difyserver/config"
	"difyserver/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

const ctxTxKey = "TxKey"

var dbMap = make(map[context.Context]*gorm.DB)
var DB *gorm.DB
var PluginDB *gorm.DB

type Repository struct {
	db *gorm.DB
	//rdb    *redis.Client
	logger *log.Logger
}

func InitDB() error {
	var err error
	DB, err = getDB()
	if err != nil {
		return err
	}
	PluginDB, err = getPluginDB()
	if err != nil {
		return err
	}
	initSqlEnv()
	return nil
}

func initSqlEnv() {

	if !DB.Migrator().HasTable("system_menus") {
		if result := DB.Exec(sql_system_menus_table_init); result.Error != nil {
			panic("init sql env failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasTable("feedbacks") {
		if result := DB.Exec(sql_system_feedback_table_init); result.Error != nil {
			panic("init sql env failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasColumn(&models.Account{}, "unit") {
		if result := DB.Exec(sql_accounts_update_colum_0530); result.Error != nil {
			panic("init update column failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasTable("anchor_statistics") {
		if result := DB.Exec(sql_anchor_statistics_table_init); result.Error != nil {
			panic("init anchor_statistics table failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasColumn(&models.AnchorStatistics{}, "resource_name") {
		if result := DB.Exec(sql_anchor_statistics_add_resource_name); result.Error != nil {
			panic("add resource_name column failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasColumn(&models.Apps{}, "only_me") {
		if result := DB.Exec(sql_apps_add_only_me_column); result.Error != nil {
			panic("add only_me column failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasTable("user_documents") {
		if result := DB.Exec(sql_user_documents_table_init); result.Error != nil {
			panic("init user_documents table failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasTable("temp_data") {
		if result := DB.Exec(sql_temp_data_table_init); result.Error != nil {
			panic("init temp_data table failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasTable("quiz_submissions") {
		if result := DB.Exec(sql_quiz_submissions_table_init); result.Error != nil {
			panic("init quiz_submissions table failed" + result.Error.Error())
		}
	}
	if !DB.Migrator().HasTable("quiz_statistics") {
		if result := DB.Exec(sql_quiz_statistics_table_init); result.Error != nil {
			panic("init quiz_statistics table failed" + result.Error.Error())
		}
	}

	// 检查并移除 user_documents 表中 doc_type 字段的唯一约束
	// 通过查询系统表来检查约束是否存在
	var constraintExists bool
	DB.Raw(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.table_constraints
			WHERE constraint_name = 'user_documents_doc_type_key'
			AND table_name = 'user_documents'
		)
	`).Scan(&constraintExists)

	if constraintExists {
		if result := DB.Exec(sql_user_documents_remove_unique_constraint); result.Error != nil {
			panic("remove user_documents doc_type unique constraint failed" + result.Error.Error())
		}
	}

}

func getPluginDB() (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable TimeZone=Asia/Shanghai",
		config.GlobalConfig.Database.Host,
		config.GlobalConfig.Database.Port,
		config.GlobalConfig.Database.User,
		config.GlobalConfig.Database.Password,
		"dify_plugin")

	var err error
	pluginDB, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	return pluginDB, nil
}

func getDB() (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable TimeZone=Asia/Shanghai",
		config.GlobalConfig.Database.Host,
		config.GlobalConfig.Database.Port,
		config.GlobalConfig.Database.User,
		config.GlobalConfig.Database.Password,
		config.GlobalConfig.Database.DBName)

	var err error
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})

	if config.GlobalConfig.LogLevel == "debug" {
		db = db.Debug()
	}
	if err != nil {
		return nil, err
	}
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	sqlDB.SetMaxOpenConns(100)
	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(10)
	return db, nil
}

type Transaction interface {
	Transaction(ctx context.Context, fn func(ctx context.Context) error) error
}

func NewTransaction(r *Repository) Transaction {
	return r
}

// DB return tx
// If you need to create a Transaction, you must call DB(ctx) and Transaction(ctx,fn)
func (r *Repository) DB(ctx context.Context) *gorm.DB {
	v := ctx.Value(ctxTxKey)
	if v != nil {
		if tx, ok := v.(*gorm.DB); ok {
			return tx
		}
	}
	return r.db.WithContext(ctx)
}

func (r *Repository) Transaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = context.WithValue(ctx, ctxTxKey, tx)
		return fn(ctx)
	})
}
