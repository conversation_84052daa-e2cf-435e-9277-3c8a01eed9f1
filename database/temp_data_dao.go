package database

import (
	"difyserver/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type TempDataDao struct {
	*gorm.DB
}

func GetTempDataDao() TempDataDao {
	return TempDataDao{DB: DB}
}

// Create 创建临时数据
func (d TempDataDao) CreateTempData(content, title string) (*models.TempData, error) {
	tempData := &models.TempData{
		Content: content,
		Title:   title,
	}

	if err := d.Create(tempData).Error; err != nil {
		return nil, err
	}

	return tempData, nil
}

// GetByUUID 根据UUID获取单条临时数据
func (d TempDataDao) GetByUUID(uuid uuid.UUID) (*models.TempData, error) {
	var tempData models.TempData
	if err := d.Where("uuid = ?", uuid).First(&tempData).Error; err != nil {
		return nil, err
	}
	return &tempData, nil
}

// GetUUIDsByBatchTitle 根据批次标题获取所有题目UUID
func (d TempDataDao) GetUUIDsByBatchTitle(batchTitle string) ([]uuid.UUID, error) {
	var uuids []uuid.UUID
	if err := d.Model(&models.TempData{}).Where("title = ?", batchTitle).Pluck("uuid", &uuids).Error; err != nil {
		return nil, err
	}
	return uuids, nil
}

// DeleteByBatchTitle 根据批次标题删除所有题目数据
func (d TempDataDao) DeleteByBatchTitle(batchTitle string) (int, error) {
	result := d.Where("title = ?", batchTitle).Delete(&models.TempData{})
	if result.Error != nil {
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}
