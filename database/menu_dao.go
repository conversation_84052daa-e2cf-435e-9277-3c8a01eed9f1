package database

import (
	"difyserver/models"
	"gorm.io/gorm"
)

type MenuDao struct {
	*gorm.DB
}

func GetMenuDao() MenuDao {
	return MenuDao{DB: DB}
}

func (d MenuDao) GetAll() ([]*models.SystemMenu, error) {
	var menus []*models.SystemMenu
	if err := d.Find(&menus).Error; err != nil {
		return nil, err
	}
	return menus, nil
}

func (d MenuDao) GetByID(id string) (*models.SystemMenu, error) {
	var menu models.SystemMenu
	if err := d.Where("id = ?", id).First(&menu).Error; err != nil {
		return nil, err
	}
	return &menu, nil
}
