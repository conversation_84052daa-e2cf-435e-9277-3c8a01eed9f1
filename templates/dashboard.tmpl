<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答题系统仪表板</title>
    <link rel="icon" href="/static/logo.bcac7575.png" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, rgba(52, 211, 153, 0.15) 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-links {
            margin-top: 15px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
            padding: 8px 16px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
        }

        .content {
            padding: 30px;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .overview-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .overview-card:hover {
            transform: translateY(-5px);
        }

        .overview-card .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .overview-card .number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .overview-card .label {
            font-size: 16px;
            opacity: 0.9;
        }

        .batch-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .batch-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .batch-card:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            transform: translateY(-3px);
        }

        .batch-card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }

        .batch-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .batch-stat {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .batch-stat .value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .batch-stat .label {
            font-size: 14px;
            color: #666;
        }

        .quiz-list {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .quiz-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .quiz-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .quiz-info {
            flex: 1;
        }

        .quiz-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .quiz-stats {
            font-size: 14px;
            color: #666;
        }

        .quiz-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .overview-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .batch-grid {
                grid-template-columns: 1fr;
            }
            
            .batch-stats {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><img style="width: 50px; height: 50px; margin-right: 10px;" src="/static/logo.bcac7575.png"/>答题系统仪表板</h1>
            <div class="nav-links">
                <a href="javascript:void(0)" onclick="refreshDashboard()">🔄 刷新数据</a>
{{/*                <a href="javascript:void(0)" onclick="exportData()">📊 导出数据</a>*/}}
            </div>
        </div>

        <div class="content">
            <div id="loading" class="loading">
                <p>正在加载仪表板数据...</p>
            </div>
            
            <div id="error" class="error" style="display: none;">
                <p id="errorMessage"></p>
            </div>

            <div id="dashboardContent" style="display: none;">
                <!-- 仪表板内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // API路径配置 - 兼容本地和线上环境
        const API_BASE_PATH = (() => {
            const hostname = window.location.hostname;
            const port = window.location.port;
            const pathname = window.location.pathname;
            if (pathname.startsWith('/admin/')) {
                return '/admin/api';
            }
            return '/api';
        })();

        // 时间格式化函数 - 处理UTC时间转东八区
        function formatDateTime(dateTime) {
            if (!dateTime) return '';

            let date;
            if (typeof dateTime === 'string') {
                // 数据库返回的时间是UTC时间，但没有Z后缀
                // 需要明确告诉JavaScript这是UTC时间
                if (dateTime.indexOf('T') === -1) {
                    // 如果是 "2025-08-01 11:58:11" 格式，转换为ISO格式并添加Z
                    date = new Date(dateTime.replace(' ', 'T') + 'Z');
                } else if (dateTime.indexOf('Z') === -1 && dateTime.indexOf('+') === -1) {
                    // 如果是ISO格式但没有时区信息，添加Z表示UTC
                    date = new Date(dateTime + 'Z');
                } else {
                    date = new Date(dateTime);
                }
            } else if (dateTime instanceof Date) {
                date = dateTime;
            } else {
                return '';
            }

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                return '';
            }

            // 使用东八区时区格式化
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Shanghai'
            };

            return date.toLocaleString('zh-CN', options);
        }

        // 获取会话信息
        function getSessionInfo() {
            const consoleToken = localStorage.getItem('console_token');
            let sessionInfo = {
                sessionId: 'anonymous_' + Date.now(),
                timestamp: new Date().toISOString()
            };

            if (consoleToken) {
                try {
                    const payload = JSON.parse(atob(consoleToken.split('.')[1]));
                    sessionInfo.userId = payload.user_id;
                    sessionInfo.sessionId = 'user_' + payload.user_id + '_' + Date.now();
                    sessionInfo.userType = 'authenticated';
                } catch (e) {
                    console.warn('Failed to parse console_token:', e);
                    sessionInfo.userType = 'anonymous';
                }
            } else {
                sessionInfo.userType = 'anonymous';
            }

            return sessionInfo;
        }

        // 加载仪表板数据
        async function loadDashboard() {
            try {
                const response = await fetch(`${API_BASE_PATH}/quiz/dashboard`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.result === 'success') {
                    // 确保数据结构完整，防止null错误
                    const data = result.data || {};
                    if (!data.batch_statistics) {
                        data.batch_statistics = [];
                    }
                    // 确保每个批次的数据结构完整
                    data.batch_statistics.forEach(batch => {
                        if (!batch.quiz_statistics) {
                            batch.quiz_statistics = [];
                        }
                        batch.quiz_statistics.forEach(quiz => {
                            if (!quiz.recent_submissions) {
                                quiz.recent_submissions = [];
                            }
                        });
                    });

                    displayDashboard(data);
                } else {
                    throw new Error(result.message || '获取仪表板数据失败');
                }
            } catch (error) {
                showError('加载仪表板数据失败：' + error.message);
            }
        }

        // 显示仪表板数据
        function displayDashboard(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'block';

            const content = document.getElementById('dashboardContent');
            content.innerHTML = `
                <div class="overview-grid">
                    <div class="overview-card">
                        <div class="icon">📚</div>
                        <div class="number">${data.total_batches}</div>
                        <div class="label">总批次数</div>
                    </div>
                    <div class="overview-card">
                        <div class="icon">📝</div>
                        <div class="number">${data.total_quizzes}</div>
                        <div class="label">总题目数</div>
                    </div>
                    <div class="overview-card">
                        <div class="icon">👥</div>
                        <div class="number">${data.total_submissions}</div>
                        <div class="label">总提交数</div>
                    </div>
                    <div class="overview-card">
                        <div class="icon">📈</div>
                        <div class="number">${(data.batch_statistics && data.batch_statistics.length > 0) ?
                            (data.batch_statistics.reduce((sum, batch) => sum + (batch.average_score || 0), 0) / data.batch_statistics.length).toFixed(1)
                            : '0'}</div>
                        <div class="label">平均分</div>
                    </div>
                </div>

                <div class="batch-grid">
                    ${(data.batch_statistics && data.batch_statistics.length > 0) ? data.batch_statistics.map(batch => `
                        <div class="batch-card">
                            <h3>${batch.batch_title}</h3>
                            <div class="batch-stats">
                                <div class="batch-stat">
                                    <div class="value">${batch.total_quizzes}</div>
                                    <div class="label">题目数</div>
                                </div>
                                <div class="batch-stat">
                                    <div class="value">${batch.total_submissions}</div>
                                    <div class="label">提交数</div>
                                </div>
                                <div class="batch-stat">
                                    <div class="value">${batch.average_score.toFixed(1)}</div>
                                    <div class="label">平均分</div>
                                </div>
                                <div class="batch-stat">
                                    <div class="value">${new Date(batch.created_at).toLocaleDateString()}</div>
                                    <div class="label">创建时间</div>
                                </div>
                            </div>
                            
                            <div style="margin-top: 20px;">
                                <button class="btn btn-primary" onclick="showBatchUserStatistics('${batch.batch_title}')" style="width: 100%; margin-bottom: 10px;">
                                    📊 查看用户答题统计
                                </button>
                                ${window.currentUser && window.currentUser.is_admin ? `
                                    <button class="btn btn-danger" onclick="deleteBatch('${batch.batch_title}')" style="width: 100%;">
                                        🗑️ 删除批次
                                    </button>
                                ` : ''}
                            </div>

                            ${batch.quiz_statistics && batch.quiz_statistics.length > 0 ? `
                                <div class="quiz-list">
                                    <h4 style="margin: 15px 0 10px 0; color: #666;">我的答题记录：</h4>
                                    ${(() => {
                                        // 只收集当前用户的答题记录
                                        const currentUserEmail = window.currentUser ? window.currentUser.user_email : null;
                                        const currentUserID = window.currentUser ? window.currentUser.user_id : null;
                                        const mySubmissions = [];
                                        batch.quiz_statistics.forEach(quiz => {
                                            if (quiz.recent_submissions && quiz.recent_submissions.length > 0) {
                                                quiz.recent_submissions.forEach(sub => {
                                                    // 检查是否是当前用户的记录
                                                    const isCurrentUser = (
                                                        (sub.user_info && sub.user_info.user_email === currentUserEmail) ||
                                                        (sub.user_info && sub.user_info.user_id === currentUserID)
                                                    );

                                                    if (isCurrentUser) {
                                                        mySubmissions.push({
                                                            ...sub,
                                                            quiz_title: quiz.quiz_title,
                                                            quiz_uuid: quiz.quiz_uuid
                                                        });
                                                    }
                                                });
                                            }
                                        });

                                        // 按时间排序，显示最新的记录
                                        mySubmissions.sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at));

                                        if (mySubmissions.length === 0) {
                                            return '<div style="text-align: center; color: #999; padding: 20px;">您还没有答题记录</div>';
                                        }

                                        return mySubmissions.slice(0, 10).map(sub => `
                                            <div class="quiz-item">
                                                <div class="quiz-info">
                                                    <div class="quiz-title">📝 ${sub.quiz_title}</div>
                                                    <div class="quiz-stats">
                                                        <div style="font-size: 12px; color: #666; margin: 2px 0;">
                                                            📊 ${sub.score}分 (${sub.percentage ? sub.percentage.toFixed(1) : '0'}%) | 🕒 ${formatDateTime(sub.submitted_at)}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="quiz-actions">
                                                    <button class="btn btn-primary" onclick="showQuizAnalysis('${sub.quiz_uuid}', '${sub.quiz_title}')">查看详情</button>
                                                    <button class="btn btn-secondary" onclick="exportQuizToExcel('${sub.quiz_uuid}', '${sub.quiz_title}')" style="margin-left: 8px;">导出Excel</button>
                                                </div>
                                            </div>
                                        `).join('');
                                    })()}
                                </div>
                            ` : ''}
                        </div>
                    `).join('') : `
                        <div style="text-align: center; padding: 60px 20px; color: #666;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📚</div>
                            <h3 style="margin-bottom: 10px; color: #333;">暂无答题数据</h3>
                            <p>还没有任何答题记录，请先创建题目并进行答题。</p>
                        </div>
                    `}
                </div>
            `;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
        }

        // 刷新仪表板数据
        function refreshDashboard() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'none';
            loadDashboard();
        }

        // 显示批次用户统计
        async function showBatchUserStatistics(batchTitle) {
            try {
                const response = await fetch(`${API_BASE_PATH}/quiz/batch/${encodeURIComponent(batchTitle)}/user-statistics`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.result === 'success') {
                    displayBatchUserStatistics(batchTitle, result.data);
                } else {
                    throw new Error(result.message || '获取用户统计失败');
                }
            } catch (error) {
                console.error('Batch user statistics error:', error);
                alert('获取用户统计失败：' + error.message);
            }
        }

        // 显示答题分析
        async function showQuizAnalysis(quizUUID, quizTitle) {
            try {
                // 检查用户是否已答题
                const response = await fetch(`${API_BASE_PATH}/quiz/${quizUUID}/check`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });

                if (!response.ok) {
                    if (response.status === 401) {
                        alert('请先登录后再进行答题');
                        return;
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.result === 'success') {
                    const data = result.data;

                    if (data.has_answered) {
                        // 用户已答题，显示分析选项
                        const choice = confirm(
                            `题目：${quizTitle}\n\n` +
                            `您的得分：${data.latest_score}分 (${data.latest_percentage.toFixed(1)}%)\n` +
                            `答题时间：${formatDateTime(data.submitted_at)}\n\n` +
                            `点击"确定"查看详细解析，点击"取消"返回`
                        );

                        if (choice) {
                            // 查看详细解析
                            window.open(`${API_BASE_PATH}/quiz/${quizUUID}`, '_blank');
                        }
                    } else {
                        // 用户未答题，提示先答题
                        const choice = confirm(
                            `题目：${quizTitle}\n\n` +
                            `您还没有答过这道题。\n\n` +
                            `点击"确定"开始答题，点击"取消"返回`
                        );

                        if (choice) {
                            window.open(`${API_BASE_PATH}/quiz/${quizUUID}`, '_blank');
                        }
                    }
                } else {
                    throw new Error(result.message || '检查答题状态失败');
                }
            } catch (error) {
                console.error('Quiz analysis error:', error);
                alert('获取答题分析失败：' + error.message);
            }
        }

        // 导出测验到Excel
        async function exportQuizToExcel(quizUUID, quizTitle) {
            try {
                // 显示加载提示
                const loadingBtn = event.target;
                const originalText = loadingBtn.textContent;
                loadingBtn.textContent = '导出中...';
                loadingBtn.disabled = true;

                // 获取题目数据
                const response = await fetch(`${API_BASE_PATH}/quiz/${quizUUID}/questions`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.result !== 'success') {
                    throw new Error(result.message || '获取题目数据失败');
                }

                // 准备Excel数据
                const questions = result.data;
                const excelData = questions.map((q, index) => {
                    let options = '';
                    if (q.options && q.options.length > 0) {
                        options = q.options.map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt}`).join('; ');
                    }

                    return {
                        '序号': index + 1,
                        '题目': q.question,
                        '分值': Math.round(q.score * 100) / 100,
                        '类型': getQuestionTypeText(q.question_type),
                        '参考答案': q.correct_answer,
                        '选项': options,
                        '解析': q.explanation || ''
                    };
                });

                // 使用SheetJS导出Excel
                if (typeof XLSX === 'undefined') {
                    // 如果XLSX库未加载，动态加载
                    await loadXLSXLibrary();
                }

                const ws = XLSX.utils.json_to_sheet(excelData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, '题目列表');

                // 设置列宽
                const colWidths = [
                    { wch: 6 },   // 序号
                    { wch: 50 },  // 题目
                    { wch: 8 },   // 分值
                    { wch: 12 },  // 类型
                    { wch: 20 },  // 参考答案
                    { wch: 40 },  // 选项
                    { wch: 30 }   // 解析
                ];
                ws['!cols'] = colWidths;

                // 生成文件名
                const fileName = `${quizTitle}_题目列表_${new Date().toISOString().slice(0, 10)}.xlsx`;

                // 下载文件
                XLSX.writeFile(wb, fileName);

                alert('Excel文件导出成功！');

            } catch (error) {
                console.error('Export error:', error);
                alert('导出失败：' + error.message);
            } finally {
                // 恢复按钮状态
                if (loadingBtn) {
                    loadingBtn.textContent = originalText;
                    loadingBtn.disabled = false;
                }
            }
        }

        // 获取题目类型文本
        function getQuestionTypeText(type) {
            const typeMap = {
                'single_choice': '单选题',
                'multiple_choice': '多选题',
                'true_false': '判断题',
                'fill_blank': '填空题',
                'short_answer': '简答题'
            };
            return typeMap[type] || type;
        }

        // 动态加载XLSX库
        function loadXLSXLibrary() {
            return new Promise((resolve, reject) => {
                if (typeof XLSX !== 'undefined') {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
                script.onload = () => resolve();
                script.onerror = () => reject(new Error('Failed to load XLSX library'));
                document.head.appendChild(script);
            });
        }

        // 创建模态框
        function createModal(title, content) {
            // 移除已存在的模态框
            const existingModal = document.getElementById('statisticsModal');
            if (existingModal) {
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'statisticsModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    max-width: 1400px;
                    max-height: 90vh;
                    overflow-y: auto;
                    position: relative;
                    width: 95%;
                    min-width: 1200px;
                ">
                    <h2 style="margin-bottom: 20px; text-align: center;">${title}</h2>
                    ${content}
                    <button onclick="document.getElementById('statisticsModal').remove()"
                            style="
                                position: absolute;
                                top: 10px;
                                right: 15px;
                                background: none;
                                border: none;
                                font-size: 24px;
                                cursor: pointer;
                                color: #999;
                            ">×</button>
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="document.getElementById('statisticsModal').remove()"
                                style="
                                    background: #007bff;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 5px;
                                    cursor: pointer;
                                ">关闭</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            return modal;
        }

        // 显示批次用户统计弹窗
        function displayBatchUserStatistics(batchTitle, data) {
            const userAttemptData = {};
            let maxAttempts = 0;

            // 处理用户数据，按用户组织答题记录
            if (data.user_statistics && data.user_statistics.length > 0) {
                data.user_statistics.forEach(user => {
                    const userName = user.user_info && user.user_info.user_name ? user.user_info.user_name : '匿名用户';
                    const userKey = user.user_id || userName;

                    if (!userAttemptData[userKey]) {
                        userAttemptData[userKey] = {
                            name: userName,
                            email: user.user_info && user.user_info.user_email ? user.user_info.user_email : '',
                            attempts: [],
                            attemptCount: user.attempt_count
                        };
                    }

                    // 收集该用户的所有答题记录，按时间排序
                    if (user.quiz_details && user.quiz_details.length > 0) {
                        const sortedQuizzes = user.quiz_details.sort((a, b) => new Date(a.submitted_at) - new Date(b.submitted_at));

                        sortedQuizzes.forEach((quiz, index) => {
                            userAttemptData[userKey].attempts.push({
                                attemptNumber: index + 1,
                                score: quiz.score,
                                quizTitle: quiz.quiz_title,
                                timestamp: new Date(quiz.submitted_at)
                            });
                        });

                        // 更新最大答题次数
                        maxAttempts = Math.max(maxAttempts, sortedQuizzes.length);
                    }
                });
            }

            const modal = createModal(`${batchTitle} - 用户答题统计`, `
                <div style="text-align: left;">
                    <div style="margin: 20px 0;">
                        <h3>批次概览</h3>
                        <p><strong>总用户数：</strong>${data.total_users}</p>
                        <p><strong>总答题次数：</strong>${data.total_attempts}</p>
                        <p><strong>平均分：</strong>${data.average_score ? data.average_score.toFixed(1) : '0'}分</p>
                    </div>

                    ${Object.keys(userAttemptData).length > 0 ? `
                        <h4>用户答题统计表：</h4>
                        <div style="max-height: 1200px; overflow: auto; border: 1px solid #ddd; border-radius: 5px;">
                            <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                                <thead style="background: #f8f9fa; position: sticky; top: 0;">
                                    <tr>
                                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left; min-width: 120px;">用户名称</th>
                                        <th style="border: 1px solid #ddd; padding: 8px; text-align: center; min-width: 80px;">答题次数</th>
                                        ${Array.from({length: maxAttempts}, (_, index) => `
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center; min-width: 120px; font-size: 12px;">
                                                第${index + 1}次
                                            </th>
                                        `).join('')}
                                    </tr>
                                </thead>
                                <tbody>
                                    ${Object.values(userAttemptData).map(user => `
                                        <tr style="border-bottom: 1px solid #eee;">
                                            <td style="border: 1px solid #ddd; padding: 8px;">
                                                <div style="font-weight: bold; color: #333;">${user.name}</div>
                                                ${user.email ? `<div style="font-size: 10px; color: #666;">${user.email}</div>` : ''}
                                            </td>
                                            <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px;">
                                                    ${user.attemptCount}次
                                                </span>
                                            </td>
                                            ${Array.from({length: maxAttempts}, (_, index) => {
                                                const attemptNumber = index + 1;
                                                const attempt = user.attempts.find(a => a.attemptNumber === attemptNumber);
                                                return `
                                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">
                                                        ${attempt ? `
                                                            <div style="
                                                                background: ${attempt.score >= 80 ? '#d4edda' : attempt.score >= 60 ? '#fff3cd' : '#f8d7da'};
                                                                color: ${attempt.score >= 80 ? '#155724' : attempt.score >= 60 ? '#856404' : '#721c24'};
                                                                padding: 6px 8px;
                                                                border-radius: 4px;
                                                                font-weight: bold;
                                                                font-size: 12px;
                                                                text-align: center;
                                                            ">
                                                                ${attempt.score}分<br>
                                                                <span style="font-size: 9px; font-weight: normal; opacity: 0.8;">
                                                                    (${formatDateTime(attempt.timestamp)})
                                                                </span>
                                                            </div>
                                                        ` : '<span style="color: #ccc;">-</span>'}
                                                    </td>
                                                `;
                                            }).join('')}
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 12px;">
                            <strong>说明：</strong>
                            <span style="background: #d4edda; padding: 2px 4px; margin: 0 5px;">绿色</span>优秀(≥80分)
                            <span style="background: #fff3cd; padding: 2px 4px; margin: 0 5px;">黄色</span>良好(60-79分)
                            <span style="background: #f8d7da; padding: 2px 4px; margin: 0 5px;">红色</span>需改进(<60分)
                        </div>
                    ` : '<p style="text-align: center; color: #666; margin: 40px 0;">暂无用户答题数据</p>'}
                </div>
            `);
        }

        // 导出数据（占位功能）
        function exportData() {
            alert('导出功能开发中...');
        }

        // 检查登录状态
        async function checkLoginStatus() {
            const token = localStorage.getItem('console_token');
            if (!token) {
                showLoginRequired('请先登录后再查看仪表板');
                return false;
            }

            try {
                // 向服务器验证token是否有效
                const response = await fetch(`${API_BASE_PATH}/verify-login`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.result === 'success') {
                        // 登录状态有效，存储用户信息
                        window.currentUser = result.data;
                        return true;
                    }
                }

                // token无效或过期
                // localStorage.removeItem('console_token');
                showLoginRequired('登录状态已过期，请重新登录');
                return false;

            } catch (error) {
                console.error('验证登录状态失败:', error);
                showLoginRequired('网络错误，请检查网络连接后重试');
                return false;
            }
        }

        // 显示登录要求页面
        function showLoginRequired(message) {
            document.body.innerHTML = `
                <div style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                ">
                    <div style="
                        background: white;
                        padding: 40px;
                        border-radius: 15px;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                        max-width: 400px;
                    ">
                        <h2 style="color: #333; margin-bottom: 20px;">🔐 需要登录</h2>
                        <p style="color: #666; margin-bottom: 30px;">${message}</p>
                        <button onclick="window.location.href='/'" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 12px 30px;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 16px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'">
                            前往登录
                        </button>
                    </div>
                </div>
            `;
        }

        // 删除批次功能
        async function deleteBatch(batchTitle) {
            if (!window.currentUser || !window.currentUser.is_admin) {
                alert('只有超级管理员才能删除批次！');
                return;
            }

            const confirmed = confirm(`确定要删除批次"${batchTitle}"吗？\n\n⚠️ 警告：此操作将删除该批次下的所有题目和答题记录，且无法恢复！`);
            if (!confirmed) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_PATH}/quiz/batch/${encodeURIComponent(batchTitle)}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('console_token'),
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.result === 'success') {
                        alert('批次删除成功！');
                        // 重新加载仪表板数据
                        loadDashboard();
                    } else {
                        alert('删除失败：' + (result.message || '未知错误'));
                    }
                } else {
                    alert('删除失败：HTTP ' + response.status);
                }
            } catch (error) {
                console.error('Delete batch error:', error);
                alert('删除失败：' + error.message);
            }
        }

        // 刷新仪表板
        function refreshDashboard() {
            loadDashboard();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 调试信息：显示当前API路径
            console.log('当前环境API路径:', API_BASE_PATH);
            console.log('当前域名:', window.location.hostname);
            console.log('当前端口:', window.location.port);

            // 首先检查登录状态
            const isLoggedIn = await checkLoginStatus();
            if (!isLoggedIn) {
                return;
            }

            loadDashboard();
        });
    </script>
</body>
</html>
