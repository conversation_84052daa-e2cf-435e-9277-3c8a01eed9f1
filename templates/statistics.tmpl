<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答题统计 - {{.title}}</title>
    <link rel="icon" href="/static/logo.bcac7575.png" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, rgba(52, 211, 153, 0.15) 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-links {
            margin-top: 15px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
            padding: 8px 16px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
        }

        .content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stats-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-weight: 500;
            color: #666;
        }

        .stat-value {
            font-weight: bold;
            color: #333;
        }

        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .submissions-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .submission-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }

        .submission-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .submission-score {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        .submission-time {
            color: #666;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><img style="width: 50px; height: 50px; margin-right: 10px;" src="/static/logo.bcac7575.png"/>答题统计系统</h1>
            <div class="nav-links">
                <a href="javascript:history.back()">🔙 返回答题</a>
                <a href="javascript:void(0)" onclick="loadDashboard()">📈 系统仪表板</a>
                <a href="javascript:void(0)" onclick="refreshStats()">🔄 刷新数据</a>
            </div>
        </div>

        <div class="content">
            <div id="loading" class="loading">
                <p>正在加载统计数据...</p>
            </div>
            
            <div id="error" class="error" style="display: none;">
                <p id="errorMessage"></p>
            </div>

            <div id="statsContent" style="display: none;">
                <!-- 统计内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // API路径配置 - 兼容本地和线上环境
        const API_BASE_PATH = (() => {
            const hostname = window.location.hostname;
            const port = window.location.port;
            const pathname = window.location.pathname;
            if (pathname.startsWith('/admin/')) {
                return '/admin/api';
            }
            return '/api';
        })();

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 时间格式化函数 - 处理UTC时间转东八区
        function formatDateTime(dateTime) {
            if (!dateTime) return '';

            let date;
            if (typeof dateTime === 'string') {
                // 数据库返回的时间是UTC时间，但没有Z后缀
                if (dateTime.indexOf('T') === -1) {
                    // 如果是 "2025-08-01 11:58:11" 格式，转换为ISO格式并添加Z
                    date = new Date(dateTime.replace(' ', 'T') + 'Z');
                } else if (dateTime.indexOf('Z') === -1 && dateTime.indexOf('+') === -1) {
                    // 如果是ISO格式但没有时区信息，添加Z表示UTC
                    date = new Date(dateTime + 'Z');
                } else {
                    date = new Date(dateTime);
                }
            } else if (dateTime instanceof Date) {
                date = dateTime;
            } else {
                return '';
            }

            if (isNaN(date.getTime())) {
                return '';
            }

            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Shanghai'
            };

            return date.toLocaleString('zh-CN', options);
        }

        // 获取会话信息
        function getSessionInfo() {
            const consoleToken = localStorage.getItem('console_token');
            let sessionInfo = {
                sessionId: 'anonymous_' + Date.now(),
                timestamp: new Date().toISOString()
            };

            if (consoleToken) {
                try {
                    const payload = JSON.parse(atob(consoleToken.split('.')[1]));
                    sessionInfo.userId = payload.user_id;
                    sessionInfo.sessionId = 'user_' + payload.user_id + '_' + Date.now();
                    sessionInfo.userType = 'authenticated';
                } catch (e) {
                    console.warn('Failed to parse console_token:', e);
                    sessionInfo.userType = 'anonymous';
                }
            } else {
                sessionInfo.userType = 'anonymous';
            }

            return sessionInfo;
        }

        // 加载统计数据
        async function loadStatistics() {
            try {
                const quizUUID = getUrlParameter('uuid');
                if (!quizUUID) {
                    throw new Error('缺少题目UUID参数');
                }

                const response = await fetch(`${API_BASE_PATH}/quiz/${quizUUID}/statistics`, {
                    headers: {
                        'Authorization': 'Bearer ' + (localStorage.getItem('console_token') || '')
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (result.result === 'success') {
                    displayStatistics(result.data);
                } else {
                    throw new Error(result.message || '获取统计信息失败');
                }
            } catch (error) {
                showError('加载统计数据失败：' + error.message);
            }
        }

        // 显示统计数据
        function displayStatistics(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('statsContent').style.display = 'block';

            const content = document.getElementById('statsContent');
            content.innerHTML = `
                <div class="stats-grid">
                    <div class="stats-card">
                        <h3>📊 基本统计</h3>
                        <div class="stat-item">
                            <span class="stat-label">题目标题</span>
                            <span class="stat-value">${data.quiz_title}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">总提交数</span>
                            <span class="stat-value">${data.total_submissions}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">平均分</span>
                            <span class="stat-value">${data.average_score.toFixed(1)}分</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">最高分</span>
                            <span class="stat-value">${data.highest_score}分</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">最低分</span>
                            <span class="stat-value">${data.lowest_score}分</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">及格率</span>
                            <span class="stat-value">${data.pass_rate.toFixed(1)}%</span>
                        </div>
                    </div>

                    <div class="stats-card">
                        <h3>📈 分数分布</h3>
                        ${Object.entries(data.score_distribution || {}).map(([range, count]) => `
                            <div class="stat-item">
                                <span class="stat-label">${range}分</span>
                                <span class="stat-value">${count}人</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                ${data.recent_submissions && data.recent_submissions.length > 0 ? `
                    <div class="chart-container">
                        <h3>📝 最近提交记录</h3>
                        <div class="submissions-list">
                            ${data.recent_submissions.map(sub => `
                                <div class="submission-item">
                                    <div class="submission-score">${sub.percentage.toFixed(1)}% (${sub.score}分)</div>
                                    <div class="submission-time">提交时间：${sub.submitted_at}</div>
                                    ${sub.user_info && sub.user_info.name ? `<div>用户：${sub.user_info.name}</div>` : ''}
                                    ${sub.user_info && sub.user_info.sessionId ? `<div>会话ID：${sub.user_info.sessionId}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
        }

        // 刷新统计数据
        function refreshStats() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('statsContent').style.display = 'none';
            loadStatistics();
        }

        // 加载仪表板
        function loadDashboard() {
            window.location.href = '/dashboard';
        }

        // 检查登录状态
        async function checkLoginStatus() {
            const token = localStorage.getItem('console_token');
            if (!token) {
                showLoginRequired('请先登录后再查看统计信息');
                return false;
            }

            try {
                // 向服务器验证token是否有效
                const response = await fetch(`${API_BASE_PATH}/verify-login`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.result === 'success') {
                        // 登录状态有效，存储用户信息
                        window.currentUser = result.data;
                        return true;
                    }
                }

                // token无效或过期
                // localStorage.removeItem('console_token');
                showLoginRequired('登录状态已过期，请重新登录');
                return false;

            } catch (error) {
                console.error('验证登录状态失败:', error);
                showLoginRequired('网络错误，请检查网络连接后重试');
                return false;
            }
        }

        // 显示登录要求页面
        function showLoginRequired(message) {
            document.body.innerHTML = `
                <div style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                ">
                    <div style="
                        background: white;
                        padding: 40px;
                        border-radius: 15px;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                        text-align: center;
                        max-width: 400px;
                    ">
                        <h2 style="color: #333; margin-bottom: 20px;">🔐 需要登录</h2>
                        <p style="color: #666; margin-bottom: 30px;">${message}</p>
                        <button onclick="window.location.href='/'" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 12px 30px;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 16px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'">
                            前往登录
                        </button>
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 首先检查登录状态
            if (!checkLoginStatus()) {
                return;
            }

            loadStatistics();
        });
    </script>
</body>
</html>
