# 锚点统计API文档

## 概述

锚点统计系统用于记录用户对各种资源的操作统计，支持多维度统计分析。

## 数据库表结构

### anchor_statistics 表

| 字段名 | 类型 | 说明 | 索引 |
|--------|------|------|------|
| id | BIGSERIAL | 主键 | PRIMARY KEY |
| tenant_id | UUID | 租户ID | INDEX |
| user_id | UUID | 用户ID | INDEX |
| resource_type | VARCHAR(100) | 资源类型 | INDEX |
| resource_id | VARCHAR(255) | 资源ID | INDEX |
| resource_name | VARCHAR(500) | 资源名称（用于展示） | INDEX |
| action_type | VARCHAR(100) | 操作类型 | INDEX |
| action_count | INTEGER | 操作次数 | - |
| record_date | DATE | 记录日期 | INDEX |
| created_at | TIMESTAMP | 创建时间 | - |
| updated_at | TIMESTAMP | 更新时间 | - |

### 索引说明

- 单列索引：tenant_id, user_id, resource_type, resource_id, resource_name, action_type, record_date
- 复合索引：
  - (tenant_id, record_date) - 按租户和日期查询
  - (user_id, record_date) - 按用户和日期查询
  - (resource_type, resource_id, record_date) - 按资源和日期查询
- 唯一索引：(tenant_id, user_id, resource_type, resource_id, action_type, record_date) - 确保同一天同一用户对同一资源的同一操作只有一条记录

## API接口

### 1. 记录锚点统计

**接口地址：** `POST /api/anchor-statistics/record.json`

**权限要求：** 普通用户权限

**请求参数：**
```json
{
  "resource_type": "app",
  "resource_id": "123e4567-e89b-12d3-a456-426614174000",
  "action_type": "click"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "message": "锚点统计记录成功"
  }
}
```

### 2. 获取锚点统计列表

**接口地址：** `GET /api/anchor-statistics.json`

**权限要求：** 管理员权限

**查询参数：**
- `resource_type` (可选): 资源类型
- `resource_id` (可选): 资源ID
- `action_type` (可选): 操作类型
- `user_id` (可选): 用户ID
- `start_date` (可选): 开始日期 (YYYY-MM-DD)
- `end_date` (可选): 结束日期 (YYYY-MM-DD)
- `page` (可选): 页码，默认1
- `page_size` (可选): 每页大小，默认20

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "tenant_id": "123e4567-e89b-12d3-a456-426614174000",
        "user_id": "123e4567-e89b-12d3-a456-426614174001",
        "user_name": "张三",
        "resource_type": "app",
        "resource_id": "app-001",
        "resource_name": "我的智能助手",
        "action_type": "click",
        "action_count": 5,
        "record_date": "2024-01-15T00:00:00Z",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T15:45:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

### 3. 获取聚合统计数据

**接口地址：** `GET /api/anchor-statistics/aggregate.json`

**权限要求：** 管理员权限

**查询参数：**
- `resource_type` (可选): 资源类型
- `resource_id` (可选): 资源ID
- `action_type` (可选): 操作类型
- `user_id` (可选): 用户ID
- `start_date` (可选): 开始日期 (YYYY-MM-DD)
- `end_date` (可选): 结束日期 (YYYY-MM-DD)
- `group_by` (可选): 聚合维度 (day/month/year)，默认day
- `page` (可选): 页码，默认1
- `page_size` (可选): 每页大小，默认20

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "period": "2024-01-15",
        "resource_type": "app",
        "resource_id": "app-001",
        "resource_name": "我的智能助手",
        "action_type": "click",
        "total_count": 150,
        "user_count": 25
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20,
    "total_pages": 3
  }
}
```

### 4. 获取分析统计数据

**接口地址：** `GET /api/anchor-statistics/analysis.json`

**权限要求：** 管理员权限

**查询参数：**
- `resource_type` (必填): 资源类型
- `resource_id` (可选): 资源ID，不指定则统计该类型下所有资源
- `action_type` (必填): 操作类型
- `days` (必填): 过去天数 (1-365)
- `step` (必填): 汇总步长天数 (1-30)

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "start_date": "2024-01-01",
        "end_date": "2024-01-07",
        "total_count": 150,
        "user_count": 25,
        "resource_type": "app",
        "action_type": "click"
      },
      {
        "start_date": "2024-01-08",
        "end_date": "2024-01-14",
        "total_count": 180,
        "user_count": 30,
        "resource_type": "app",
        "action_type": "click"
      }
    ],
    "resource_type": "app",
    "action_type": "click",
    "total_days": 30,
    "step_days": 7,
    "summary": {
      "total_count": 1200,
      "total_user_count": 85,
      "average_per_period": 171.4,
      "max_period_count": 220,
      "min_period_count": 120
    }
  }
}
```

### 5. 获取资源汇总统计数据

**接口地址：** `GET /api/anchor-statistics/resource-summary.json`

**权限要求：** 管理员权限

**查询参数：**
- `resource_type` (必填): 资源类型
- `action_type` (必填): 操作类型

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "f99b7ff2-d722-4dbc-9b04-f6df29376def": {
      "resource_type": "app",
      "resource_id": "f99b7ff2-d722-4dbc-9b04-f6df29376def",
      "resource_name": "我的智能助手",
      "action_type": "click",
      "total_count": 150,
      "user_count": 25
    },
    "a1b2c3d4-e5f6-7890-abcd-ef1234567890": {
      "resource_type": "app",
      "resource_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "resource_name": "数据分析工具",
      "action_type": "click",
      "total_count": 89,
      "user_count": 15
    }
  }
}
```

### 6. 获取统计详情

**接口地址：** `GET /api/anchor-statistics/{id}.json`

**权限要求：** 管理员权限

**路径参数：**
- `id`: 统计记录ID

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": "123e4567-e89b-12d3-a456-426614174000",
    "user_id": "123e4567-e89b-12d3-a456-426614174001",
    "user_name": "张三",
    "resource_type": "app",
    "resource_id": "app-001",
    "resource_name": "我的智能助手",
    "action_type": "click",
    "action_count": 5,
    "record_date": "2024-01-15T00:00:00Z",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T15:45:00Z"
  }
}
```

## 常用资源类型和操作类型

### 资源类型 (resource_type)
- `app`: 应用
- `dataset`: 数据集
- `feedback`: 反馈
- `menu`: 菜单
- `user`: 用户
- `tenant`: 租户

### 操作类型 (action_type)
- `click`: 点击
- `view`: 查看
- `create`: 创建
- `edit`: 编辑
- `delete`: 删除
- `login`: 登录
- `logout`: 登出

## 使用示例

### 前端记录用户点击应用的操作

```javascript
// 用户点击应用时记录统计
async function recordAppClick(appId) {
  try {
    const response = await fetch('/api/anchor-statistics/record.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        resource_type: 'app',
        resource_id: appId,
        action_type: 'click'
      })
    });
    
    const result = await response.json();
    console.log('统计记录成功:', result);
  } catch (error) {
    console.error('统计记录失败:', error);
  }
}
```

### 管理员查看应用点击统计

```javascript
// 查看某个应用的点击统计
async function getAppClickStats(appId, startDate, endDate) {
  try {
    const params = new URLSearchParams({
      resource_type: 'app',
      resource_id: appId,
      action_type: 'click',
      start_date: startDate,
      end_date: endDate,
      group_by: 'day'
    });

    const response = await fetch(`/api/anchor-statistics/aggregate.json?${params}`, {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });

    const result = await response.json();
    console.log('统计数据:', result.data);
  } catch (error) {
    console.error('获取统计失败:', error);
  }
}
```

### 管理员查看分析统计数据

```javascript
// 查看过去30天所有应用点击统计，按7天汇总
async function getAllAppClickAnalysis() {
  try {
    const params = new URLSearchParams({
      resource_type: 'app',
      action_type: 'click',
      days: 30,
      step: 7
    });

    const response = await fetch(`/api/anchor-statistics/analysis.json?${params}`, {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });

    const result = await response.json();
    console.log('所有应用分析数据:', result.data);
    console.log('汇总信息:', result.data.summary);

    return result.data;
  } catch (error) {
    console.error('获取分析数据失败:', error);
  }
}

// 查看过去30天特定应用点击统计，按7天汇总
async function getSpecificAppClickAnalysis(appId) {
  try {
    const params = new URLSearchParams({
      resource_type: 'app',
      resource_id: appId,
      action_type: 'click',
      days: 30,
      step: 7
    });

    const response = await fetch(`/api/anchor-statistics/analysis.json?${params}`, {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });

    const result = await response.json();
    console.log('特定应用分析数据:', result.data);
    console.log('汇总信息:', result.data.summary);

    // 可以用于绘制图表
    const chartData = result.data.data.map(item => ({
      period: `${item.start_date} 至 ${item.end_date}`,
      count: item.total_count,
      users: item.user_count
    }));

    return chartData;
  } catch (error) {
    console.error('获取分析数据失败:', error);
  }
}
```

### 管理员查看资源汇总统计数据

```javascript
// 获取所有应用的点击统计汇总
async function getAppClickResourceSummary() {
  try {
    const params = new URLSearchParams({
      resource_type: 'app',
      action_type: 'click'
    });

    const response = await fetch(`/api/anchor-statistics/resource-summary.json?${params}`, {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });

    const result = await response.json();
    console.log('资源汇总数据:', result.data);

    // 转换为数组格式，方便排序和展示
    const summaryArray = Object.values(result.data).sort((a, b) => b.total_count - a.total_count);

    // 可以用于生成排行榜
    const rankingData = summaryArray.map((item, index) => ({
      rank: index + 1,
      resourceName: item.resource_name,
      resourceId: item.resource_id,
      totalCount: item.total_count,
      userCount: item.user_count
    }));

    return rankingData;
  } catch (error) {
    console.error('获取资源汇总数据失败:', error);
  }
}

// 获取特定资源的统计数据
async function getResourceStatistics(resourceId, resourceSummaryData) {
  // 从汇总数据中查找特定资源
  const resourceStats = resourceSummaryData[resourceId];

  if (resourceStats) {
    console.log(`资源 ${resourceStats.resource_name} 的统计:`, {
      总点击数: resourceStats.total_count,
      用户数: resourceStats.user_count,
      平均每用户点击数: (resourceStats.total_count / resourceStats.user_count).toFixed(2)
    });

    return resourceStats;
  } else {
    console.log('未找到该资源的统计数据');
    return null;
  }
}
```

## 注意事项

1. **数据去重**: 同一天同一用户对同一资源的同一操作会自动累加计数，不会产生重复记录
2. **权限控制**: 记录接口只需要普通用户权限，查询接口需要管理员权限
3. **租户隔离**: 所有数据都按租户隔离，用户只能看到自己租户的数据
4. **性能优化**: 已添加必要的索引，支持高效的多维度查询
5. **日期格式**: 所有日期参数使用 YYYY-MM-DD 格式
6. **资源名称自动获取**: 系统会根据资源类型和ID自动查询并填充资源名称，无需在记录时提供

## 资源名称获取规则

系统会根据不同的资源类型自动获取对应的名称：

- **app**: 从 `apps` 表的 `name` 字段获取应用名称
- **dataset**: 从 `datasets` 表的 `name` 字段获取数据集名称
- **feedback**: 从 `feedbacks` 表的 `feedback_content` 字段获取反馈内容前50个字符作为名称
- **menu**: 从 `system_menus` 表的 `title` 字段获取菜单标题
- **user**: 从 `accounts` 表的 `name` 字段获取用户名称
- **tenant**: 从 `tenants` 表的 `name` 字段获取租户名称

如果查询失败或资源不存在，`resource_name` 字段将为空字符串。
