package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"strings"
	"time"

	"difyserver/config"
)

// HTTPClient is an interface for HTTP client operations
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client is a wrapper around the standard http.Client with additional functionality
type Client struct {
	httpClient     HTTPClient
	baseURL        string
	defaultHeaders map[string]string
	timeout        time.Duration
	bearer         string
}

// ClientOption is a function that configures the Client
type ClientOption func(*Client)

// Response is a wrapper around the standard http.Response
type Response struct {
	StatusCode int
	Body       []byte
	Headers    http.Header
}

// RequestError represents an error that occurred during an HTTP request
type RequestError struct {
	StatusCode int
	Message    string
	URL        string
}

// Error implements the error interface
func (e *RequestError) Error() string {
	return fmt.Sprintf("HTTP request failed: %s (status code: %d, URL: %s)",
		e.Message, e.StatusCode, e.URL)
}

func GetDifyClient() *Client {
	client := NewClient(
		WithBaseURL(config.GlobalConfig.DifyUrl),
		WithTimeout(10*time.Second),
	)
	return client
}

func GetDifyClientWithToken(token string) *Client {
	client := NewClient(
		WithBaseURL(config.GlobalConfig.DifyUrl),
		WithTimeout(10*time.Second),
		WithBearer(token),
	)
	return client
}

// NewClient creates a new HTTP client with the provided options
func NewClient(options ...ClientOption) *Client {
	defaultClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	client := &Client{
		httpClient:     defaultClient,
		defaultHeaders: make(map[string]string),
		timeout:        30 * time.Second,
	}

	// Add default headers
	client.defaultHeaders["Content-Type"] = "application/json"
	client.defaultHeaders["Accept"] = "application/json"

	// Apply options
	for _, option := range options {
		option(client)
	}

	return client
}

// WithBaseURL sets the base URL for the client
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) {
		c.baseURL = strings.TrimRight(baseURL, "/")
	}
}

// WithTimeout sets the timeout for the client
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.timeout = timeout
		if httpClient, ok := c.httpClient.(*http.Client); ok {
			httpClient.Timeout = timeout
		}
	}
}

// WithBearer sets the bearer token for authentication
func WithBearer(token string) ClientOption {
	return func(c *Client) {
		c.bearer = token
	}
}

// WithCustomClient sets a custom HTTP client
func WithCustomClient(httpClient HTTPClient) ClientOption {
	return func(c *Client) {
		c.httpClient = httpClient
	}
}

// WithHeader adds a default header to the client
func WithHeader(key, value string) ClientOption {
	return func(c *Client) {
		c.defaultHeaders[key] = value
	}
}

// SetBearerToken updates the bearer token for the client
func (c *Client) SetBearerToken(token string) {
	c.bearer = token
}

// buildURL constructs the full URL for the request
func (c *Client) buildURL(path string, queryParams map[string]string) (string, error) {
	// Handle absolute URLs
	if strings.HasPrefix(path, "http://") || strings.HasPrefix(path, "https://") {
		return path, nil
	}

	// Make sure path starts with a slash
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	baseURL := c.baseURL
	finalURL := baseURL + path

	// Add query parameters if any
	if len(queryParams) > 0 {
		parsedURL, err := url.Parse(finalURL)
		if err != nil {
			return "", err
		}

		query := parsedURL.Query()
		for key, value := range queryParams {
			query.Add(key, value)
		}

		parsedURL.RawQuery = query.Encode()
		finalURL = parsedURL.String()
	}

	return finalURL, nil
}

// prepareRequest creates an http.Request with headers and body
func (c *Client) prepareRequest(ctx context.Context, method, url string, body interface{}, headers map[string]string) (*http.Request, error) {
	var bodyReader io.Reader

	// Process the body if it exists
	if body != nil {
		switch v := body.(type) {
		case string:
			bodyReader = strings.NewReader(v)
		case []byte:
			bodyReader = bytes.NewReader(v)
		case io.Reader:
			bodyReader = v
		default:
			bodyData, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal body: %w", err)
			}
			bodyReader = bytes.NewReader(bodyData)
		}
	}

	// Create the request
	req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add default headers
	for key, value := range c.defaultHeaders {
		req.Header.Set(key, value)
	}

	// Add request-specific headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Add bearer token if present
	if c.bearer != "" {
		req.Header.Set("Authorization", "Bearer "+c.bearer)
	}

	return req, nil
}

// processResponse processes the HTTP response
func (c *Client) processResponse(resp *http.Response) (*Response, error) {
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	slog.Info("response", "body", string(body))
	// Create the response object
	response := &Response{
		StatusCode: resp.StatusCode,
		Body:       body,
		Headers:    resp.Header,
	}

	// Check for error status codes
	if resp.StatusCode >= 400 {
		return response, &RequestError{
			StatusCode: resp.StatusCode,
			Message:    string(body),
			URL:        resp.Request.URL.String(),
		}
	}

	return response, nil
}

// Request performs an HTTP request with the given method, path, body, query parameters, and headers
func (c *Client) Request(ctx context.Context, method, path string, body interface{}, queryParams, headers map[string]string) (*Response, error) {
	// Build the URL
	url, err := c.buildURL(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to build URL: %w", err)
	}
	slog.Info("request", "url", url)
	// Prepare the request
	req, err := c.prepareRequest(ctx, method, url, body, headers)
	if err != nil {
		return nil, err
	}

	// Perform the request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to perform request: %w", err)
	}

	// Process the response
	return c.processResponse(resp)
}

// Get performs an HTTP GET request
func (c *Client) Get(ctx context.Context, path string, queryParams map[string]string, headers map[string]string) (*Response, error) {
	return c.Request(ctx, http.MethodGet, path, nil, queryParams, headers)
}

// Post performs an HTTP POST request
func (c *Client) Post(ctx context.Context, path string, body interface{}, queryParams map[string]string, headers map[string]string) (*Response, error) {
	return c.Request(ctx, http.MethodPost, path, body, queryParams, headers)
}

// Put performs an HTTP PUT request
func (c *Client) Put(ctx context.Context, path string, body interface{}, queryParams map[string]string, headers map[string]string) (*Response, error) {
	return c.Request(ctx, http.MethodPut, path, body, queryParams, headers)
}

// Patch performs an HTTP PATCH request
func (c *Client) Patch(ctx context.Context, path string, body interface{}, queryParams map[string]string, headers map[string]string) (*Response, error) {
	return c.Request(ctx, http.MethodPatch, path, body, queryParams, headers)
}

// Delete performs an HTTP DELETE request
func (c *Client) Delete(ctx context.Context, path string, queryParams map[string]string, headers map[string]string) (*Response, error) {
	return c.Request(ctx, http.MethodDelete, path, nil, queryParams, headers)
}

// GetJSON performs an HTTP GET request and unmarshals the JSON response into the result
func (c *Client) GetJSON(ctx context.Context, path string, queryParams map[string]string, headers map[string]string, result interface{}) error {
	resp, err := c.Get(ctx, path, queryParams, headers)
	if err != nil {
		return err
	}

	return json.Unmarshal(resp.Body, result)
}

// PostJSON performs an HTTP POST request and unmarshals the JSON response into the result
func (c *Client) PostJSON(ctx context.Context, path string, body interface{}, queryParams map[string]string, headers map[string]string, result interface{}) error {
	resp, err := c.Post(ctx, path, body, queryParams, headers)
	if err != nil {
		slog.ErrorContext(ctx, "post json error", "error", err)
		return err
	}

	return json.Unmarshal(resp.Body, result)
}

// PutJSON performs an HTTP PUT request and unmarshals the JSON response into the result
func (c *Client) PutJSON(ctx context.Context, path string, body interface{}, queryParams map[string]string, headers map[string]string, result interface{}) error {
	resp, err := c.Put(ctx, path, body, queryParams, headers)
	if err != nil {
		return err
	}

	return json.Unmarshal(resp.Body, result)
}

// PatchJSON performs an HTTP PATCH request and unmarshals the JSON response into the result
func (c *Client) PatchJSON(ctx context.Context, path string, body interface{}, queryParams map[string]string, headers map[string]string, result interface{}) error {
	resp, err := c.Patch(ctx, path, body, queryParams, headers)
	if err != nil {
		return err
	}

	return json.Unmarshal(resp.Body, result)
}

// DeleteJSON performs an HTTP DELETE request and unmarshals the JSON response into the result
func (c *Client) DeleteJSON(ctx context.Context, path string, queryParams map[string]string, headers map[string]string, result interface{}) error {
	resp, err := c.Delete(ctx, path, queryParams, headers)
	if err != nil {
		return err
	}

	return json.Unmarshal(resp.Body, result)
}
