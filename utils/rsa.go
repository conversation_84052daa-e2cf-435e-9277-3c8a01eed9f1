package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/sha256" // For fallback in Decrypt
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"fmt"
	"io"       // For io.ReadFull in Encrypt
	"log/slog" // Using structured logging

	"github.com/pedroalbanese/eax"
)

var (
	hybridPrefixGo = []byte("HYBRID:")
	// Error definitions
	ErrInvalidEncryptedDataGo = errors.New("go: invalid encrypted data: too short")
	ErrPrivateKeyGo           = errors.New("go: invalid private key")
	ErrPublicKeyGo            = errors.New("go: invalid public key")
	ErrEncryptionGo           = errors.New("go: encryption failed")
	ErrDecryptionGo           = errors.New("go: decryption failed")
	ErrUnsupportedKeyTypeGo   = errors.New("go: unsupported key type")
)

const (
	aesKeySizeGo = 16 // AES-128
	nonceSizeGo  = 16 // EAX Nonce size
	tagSizeGo    = 16 // EAX Tag size
)

// GenerateKeyPair 生成RSA密钥对
func GenerateKeyPair() (privateKeyPEM, publicKeyPEM string, err error) {
	// 生成RSA密钥对
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return "", "", fmt.Errorf("生成RSA密钥对失败: %w", err)
	}

	// 将私钥转换为PEM格式
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	}
	privateKeyPEM = string(pem.EncodeToMemory(privateKeyBlock))

	// 将公钥转换为PEM格式
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		return "", "", fmt.Errorf("公钥编码失败: %w", err)
	}
	publicKeyBlock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	}
	publicKeyPEM = string(pem.EncodeToMemory(publicKeyBlock))

	return privateKeyPEM, publicKeyPEM, nil
}

// Encrypt encrypts text using hybrid encryption (RSA-OAEP + AES-EAX).
// publicKeyPEM should be a PEM-encoded RSA public key.
func Encrypt(plainText string, publicKeyPEM string) ([]byte, error) {
	slog.Info("Encrypt: Starting encryption...")

	// 1. Parse RSA Public Key
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		slog.Error("Encrypt: failed to decode PEM block containing public key", "error", ErrPublicKeyGo)
		return nil, fmt.Errorf("failed to decode PEM block containing public key: %w", ErrPublicKeyGo)
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		var errPKCS1 error
		pub, errPKCS1 = x509.ParsePKCS1PublicKey(block.Bytes)
		if errPKCS1 != nil {
			slog.Error("Encrypt: failed to parse public key", "pkix_error", err, "pkcs1_error", errPKCS1, "error", ErrPublicKeyGo)
			return nil, fmt.Errorf("failed to parse public key (PKIX: %v, PKCS1: %v): %w", err, errPKCS1, ErrPublicKeyGo)
		}
	}
	rsaPubKey, ok := pub.(*rsa.PublicKey)
	if !ok {
		slog.Error("Encrypt: not an RSA public key", "error", ErrPublicKeyGo)
		return nil, fmt.Errorf("not an RSA public key: %w", ErrPublicKeyGo)
	}
	slog.Debug("Encrypt: Successfully parsed RSA Public Key.")

	// 2. Generate random AES key
	aesKey := make([]byte, aesKeySizeGo)
	if _, err := io.ReadFull(rand.Reader, aesKey); err != nil {
		slog.Error("Encrypt: failed to generate AES key", "error", err)
		return nil, fmt.Errorf("failed to generate AES key: %w: %w", err, ErrEncryptionGo)
	}
	slog.Debug("Encrypt: Generated AES Key", "key_hex", hex.EncodeToString(aesKey))

	// 3. Encrypt AES key with RSA-OAEP (SHA-1, to match Python's likely default)
	encryptedAESKey, err := rsa.EncryptOAEP(sha1.New(), rand.Reader, rsaPubKey, aesKey, nil)
	if err != nil {
		slog.Error("Encrypt: failed to encrypt AES key with RSA", "error", err)
		return nil, fmt.Errorf("failed to encrypt AES key with RSA: %w: %w", err, ErrEncryptionGo)
	}
	slog.Debug("Encrypt: Encrypted AES Key with RSA-OAEP (SHA-1)", "encrypted_key_hex", hex.EncodeToString(encryptedAESKey))

	// 4. Create AES-EAX cipher
	aesBlock, err := aes.NewCipher(aesKey)
	if err != nil {
		slog.Error("Encrypt: failed to create AES cipher for EAX", "error", err)
		return nil, fmt.Errorf("failed to create AES cipher for EAX: %w: %w", err, ErrEncryptionGo)
	}
	aeadEAX, err := eax.NewEAXWithNonceAndTagSize(aesBlock, nonceSizeGo, tagSizeGo)
	if err != nil {
		slog.Error("Encrypt: failed to create AES-EAX cipher", "error", err)
		return nil, fmt.Errorf("failed to create AES-EAX cipher: %w: %w", err, ErrEncryptionGo)
	}
	slog.Debug("Encrypt: Created AES-EAX cipher.")

	// 5. Generate random nonce for EAX
	nonce := make([]byte, nonceSizeGo)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		slog.Error("Encrypt: failed to generate nonce", "error", err)
		return nil, fmt.Errorf("failed to generate nonce: %w: %w", err, ErrEncryptionGo)
	}
	slog.Debug("Encrypt: Generated Nonce for EAX", "nonce_hex", hex.EncodeToString(nonce))

	// 6. Encrypt plaintext with AES-EAX
	sealedData := aeadEAX.Seal(nil, nonce, []byte(plainText), nil)
	if len(sealedData) < tagSizeGo {
		slog.Error("Encrypt: AES-EAX encryption produced data shorter than tag size", "error", ErrEncryptionGo)
		return nil, fmt.Errorf("AES-EAX encryption produced data shorter than tag size: %w", ErrEncryptionGo)
	}
	actualCiphertext := sealedData[:len(sealedData)-tagSizeGo]
	tag := sealedData[len(sealedData)-tagSizeGo:]
	slog.Debug("Encrypt: Plaintext encrypted with AES-EAX",
		"ciphertext_len", len(actualCiphertext),
		"tag_hex", hex.EncodeToString(tag))

	// 7. Concatenate parts
	var result []byte
	result = append(result, hybridPrefixGo...)
	result = append(result, encryptedAESKey...)
	result = append(result, nonce...)
	result = append(result, tag...)
	result = append(result, actualCiphertext...)

	slog.Info("Encrypt: Encryption successful.", "output_length_with_prefix", len(result))
	return result, nil
}

// Decrypt decrypts data using hybrid scheme or direct RSA.
func Decrypt(encryptedData []byte, privateKeyPEM string) (string, error) {
	slog.Info("Decrypt: Starting decryption.", "input_data_length", len(encryptedData))
	slog.Debug("Decrypt: Input Encrypted Data", "first_100_bytes_hex", hex.EncodeToString(encryptedData[:min(len(encryptedData), 100)]))

	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		slog.Error("Decrypt: failed to decode PEM block containing private key", "error", ErrPrivateKeyGo)
		return "", fmt.Errorf("failed to decode PEM block containing private key: %w", ErrPrivateKeyGo)
	}

	var rsaPrivKey *rsa.PrivateKey
	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err == nil {
		rsaPrivKey = priv
	} else {
		key, errPkcs8 := x509.ParsePKCS8PrivateKey(block.Bytes)
		if errPkcs8 != nil {
			slog.Error("Decrypt: failed to parse PKCS1/PKCS8 private key", "error", ErrPrivateKeyGo)
			return "", fmt.Errorf("failed to parse PKCS1/PKCS8 private key: %w", ErrPrivateKeyGo)
		}
		var ok bool
		rsaPrivKey, ok = key.(*rsa.PrivateKey)
		if !ok {
			slog.Error("Decrypt: key is not an RSA private key", "error", ErrUnsupportedKeyTypeGo)
			return "", fmt.Errorf("key is not an RSA private key: %w", ErrUnsupportedKeyTypeGo)
		}
	}
	slog.Debug("Decrypt: Successfully parsed RSA Private Key.")

	if bytes.HasPrefix(encryptedData, hybridPrefixGo) {
		slog.Info("Decrypt: Mode: Hybrid Decryption")
		data := encryptedData[len(hybridPrefixGo):]
		slog.Debug("Decrypt: Data after stripping HYBRID prefix", "length", len(data), "first_100_bytes_hex", hex.EncodeToString(data[:min(len(data), 100)]))

		rsaEncryptedKeySize := rsaPrivKey.PublicKey.Size()
		slog.Debug("Decrypt: RSA Key Size for slicing", "bytes", rsaEncryptedKeySize)

		if len(data) < rsaEncryptedKeySize+nonceSizeGo+tagSizeGo {
			slog.Error("Decrypt: data after prefix is too short",
				"got_len", len(data),
				"expected_min_len", rsaEncryptedKeySize+nonceSizeGo+tagSizeGo,
				"error", ErrInvalidEncryptedDataGo)
			return "", fmt.Errorf("data after prefix is too short. Got %d, expected at least %d: %w", len(data), rsaEncryptedKeySize+nonceSizeGo+tagSizeGo, ErrInvalidEncryptedDataGo)
		}

		offset := 0
		encryptedAESKey := data[offset : offset+rsaEncryptedKeySize]
		offset += rsaEncryptedKeySize
		nonce := data[offset : offset+nonceSizeGo]
		offset += nonceSizeGo
		tag := data[offset : offset+tagSizeGo]
		offset += tagSizeGo
		aesCiphertextPart := data[offset:]

		slog.Debug("Decrypt: Extracted crypto components",
			"enc_aes_key_len", len(encryptedAESKey), "enc_aes_key_hex", hex.EncodeToString(encryptedAESKey[:min(len(encryptedAESKey), 16)]), // Log only first 16 bytes of enc key
			"nonce_len", len(nonce), "nonce_hex", hex.EncodeToString(nonce),
			"tag_len", len(tag), "tag_hex", hex.EncodeToString(tag),
			"aes_ciphertext_len", len(aesCiphertextPart))

		var aesKey []byte
		var rsaDecryptErr error
		slog.Debug("Decrypt: Attempting RSA Decrypt of AES key with SHA-1...")
		aesKey, rsaDecryptErr = rsa.DecryptOAEP(sha1.New(), rand.Reader, rsaPrivKey, encryptedAESKey, nil)
		if rsaDecryptErr != nil {
			slog.Warn("Decrypt: RSA Decryption of AES key with SHA-1 failed, trying SHA-256...", "sha1_error", rsaDecryptErr)
			aesKey, rsaDecryptErr = rsa.DecryptOAEP(sha256.New(), rand.Reader, rsaPrivKey, encryptedAESKey, nil)
			if rsaDecryptErr != nil {
				slog.Error("Decrypt: failed to decrypt AES key with RSA (tried SHA1, SHA256)", "error", rsaDecryptErr)
				return "", fmt.Errorf("failed to decrypt AES key with RSA (tried SHA1, SHA256): %w: %w", rsaDecryptErr, ErrDecryptionGo)
			}
			slog.Info("Decrypt: SUCCESS: RSA Decryption of AES key with SHA-256 succeeded.")
		} else {
			slog.Info("Decrypt: SUCCESS: RSA Decryption of AES key with SHA-1 succeeded.")
		}
		slog.Debug("Decrypt: Decrypted AES Key", "length", len(aesKey), "key_hex", hex.EncodeToString(aesKey))

		if len(aesKey) != aesKeySizeGo {
			slog.Error("Decrypt: decrypted AES key has incorrect length", "expected", aesKeySizeGo, "got", len(aesKey))
			return "", fmt.Errorf("decrypted AES key has incorrect length. Expected %d, got %d", aesKeySizeGo, len(aesKey))
		}

		aesBlock, err := aes.NewCipher(aesKey)
		if err != nil {
			slog.Error("Decrypt: failed to create AES cipher for EAX", "error", err)
			return "", fmt.Errorf("failed to create AES cipher for EAX: %w: %w", err, ErrDecryptionGo)
		}
		aeadEAX, err := eax.NewEAXWithNonceAndTagSize(aesBlock, nonceSizeGo, tagSizeGo)
		if err != nil {
			slog.Error("Decrypt: failed to create AES-EAX", "error", err)
			return "", fmt.Errorf("failed to create AES-EAX: %w: %w", err, ErrDecryptionGo)
		}
		slog.Debug("Decrypt: AES-EAX cipher created successfully.")

		decryptedBytes, err := aeadEAX.Open(nil, nonce, append(aesCiphertextPart, tag...), nil)
		if err != nil {
			slog.Error("Decrypt: AES-EAX decryption/verification failed", "error", err)
			return "", fmt.Errorf("AES-EAX decryption/verification failed: %w: %w", err, ErrDecryptionGo)
		}
		slog.Info("Decrypt: AES-EAX Decrypt and Verify successful.")
		slog.Debug("Decrypt: Decrypted Text", "length", len(decryptedBytes), "partial_hex", hex.EncodeToString(decryptedBytes[:min(len(decryptedBytes), 100)]))

		finalDecodedText := string(decryptedBytes)
		slog.Info("Decrypt: --- Decryption Attempt Finished (Hybrid) ---")
		return finalDecodedText, nil

	} else {
		slog.Info("Decrypt: Mode: Direct RSA Decryption")
		decryptedBytes, err := rsa.DecryptOAEP(sha1.New(), rand.Reader, rsaPrivKey, encryptedData, nil)
		if err != nil {
			slog.Warn("Decrypt: Direct RSA decryption with SHA-1 failed, trying SHA-256...", "sha1_error", err)
			decryptedBytes, err = rsa.DecryptOAEP(sha256.New(), rand.Reader, rsaPrivKey, encryptedData, nil)
			if err != nil {
				slog.Error("Decrypt: direct RSA decryption failed (tried SHA1, SHA256)", "error", err)
				return "", fmt.Errorf("direct RSA decryption failed (tried SHA1, SHA256): %w: %w", err, ErrDecryptionGo)
			}
		}
		slog.Info("Decrypt: Direct RSA Decryption successful.")
		finalDecodedText := string(decryptedBytes)
		slog.Info("Decrypt: --- Decryption Attempt Finished (Direct RSA) ---")
		return finalDecodedText, nil
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
