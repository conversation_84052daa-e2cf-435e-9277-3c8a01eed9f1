package utils

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"time"
)

// Examples of how to use the HTTP client
//
//func TestLogin(t *testing.T) {
//	client := NewClient(
//		WithBaseURL("http://localhost"),
//		WithTimeout(10*time.Second),
//	)
//	ctx := context.Background()
//	requestBody := map[string]interface{}{
//		Email:      "<EMAIL>",
//		Password:   "admin@123",
//		Language:   "zh-Hans",
//		RememberMe: true,
//	}
//	responseData := new(view.DifyResult[view.AuthLoginResult])
//	err := client.PostJSON(ctx, "/console/api/login", requestBody, nil, nil, responseData)
//	if err != nil {
//		fmt.Println("Login failed:", err)
//	}
//	fmt.Println("Login successful")
//	fmt.Println(responseData)
//}

func ExampleNewClient() {
	// Create a new HTTP client with default settings
	client := NewClient()

	// Create a client with custom options
	clientWithOptions := NewClient(
		WithBaseURL("https://api.example.com"),
		WithTimeout(10*time.Second),
		WithBearer("your-token-here"),
		WithHeader("X-Custom-Header", "custom-value"),
	)

	// Use the clients as needed
	fmt.Printf("Client initialized: %v\n", client != nil)
	fmt.Printf("Client with options initialized: %v\n", clientWithOptions != nil)
}

func ExampleClient_Get() {
	// Create a mock server for testing
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request method
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Check authorization header
		auth := r.Header.Get("Authorization")
		if auth != "Bearer test-token" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Return a success response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "Hello, World!", "status": "success"}`))
	}))
	defer server.Close()

	// Create a client with the mock server URL as base URL
	client := NewClient(
		WithBaseURL(server.URL),
		WithBearer("test-token"),
	)

	// Make a GET request
	ctx := context.Background()
	response, err := client.Get(ctx, "/api/resource", nil, nil)
	if err != nil {
		log.Fatalf("Request failed: %v", err)
	}

	fmt.Printf("Status code: %d\n", response.StatusCode)
	fmt.Printf("Response body: %s\n", response.Body)
}

func ExampleClient_PostJSON() {
	// Create a mock server for testing
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check request method
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// Return a success response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusCreated)
		w.Write([]byte(`{"id": 123, "name": "New Resource", "created": true}`))
	}))
	defer server.Close()

	// Create a client with the mock server URL as base URL
	client := NewClient(
		WithBaseURL(server.URL),
		WithBearer("test-token"),
	)

	// Create request body
	requestBody := map[string]interface{}{
		"name": "New Resource",
		"type": "example",
	}

	// Make a POST request and unmarshal the response into a struct
	ctx := context.Background()
	var responseData struct {
		ID      int    `json:"id"`
		Name    string `json:"name"`
		Created bool   `json:"created"`
	}

	err := client.PostJSON(ctx, "/api/resources", requestBody, nil, nil, &responseData)
	if err != nil {
		log.Fatalf("Request failed: %v", err)
	}

	fmt.Printf("Created resource with ID: %d\n", responseData.ID)
	fmt.Printf("Resource name: %s\n", responseData.Name)
	fmt.Printf("Resource created: %v\n", responseData.Created)
}

func ExampleCompleteUsage() {
	// This example shows a more complete usage of the HTTP client

	// Create client with options
	client := NewClient(
		WithBaseURL("https://api.example.com"),
		WithTimeout(30*time.Second),
		WithBearer("initial-token"),
	)

	// Update the bearer token if needed
	client.SetBearerToken("updated-token")

	// Prepare context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 1. GET example with query parameters
	queryParams := map[string]string{
		"page":  "1",
		"limit": "10",
		"sort":  "name",
	}

	var getResponse struct {
		Items []struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
		} `json:"items"`
		Total int `json:"total"`
	}

	if err := client.GetJSON(ctx, "/api/resources", queryParams, nil, &getResponse); err != nil {
		fmt.Printf("GET request failed: %v\n", err)
		return
	}

	// 2. POST example with custom headers
	postBody := map[string]interface{}{
		"name": "New Resource",
		"attributes": map[string]interface{}{
			"color": "blue",
			"size":  "large",
		},
	}

	customHeaders := map[string]string{
		"X-Request-ID":     "unique-request-id-123",
		"X-Client-Version": "1.0.0",
	}

	var postResponse struct {
		ID         int    `json:"id"`
		Name       string `json:"name"`
		Created    bool   `json:"created"`
		CreateTime string `json:"create_time"`
	}

	if err := client.PostJSON(ctx, "/api/resources", postBody, nil, customHeaders, &postResponse); err != nil {
		fmt.Printf("POST request failed: %v\n", err)
		return
	}

	// 3. PUT example to update a resource
	updateBody := map[string]interface{}{
		"name": "Updated Resource",
		"attributes": map[string]interface{}{
			"color": "red",
			"size":  "medium",
		},
	}

	var putResponse struct {
		Updated bool `json:"updated"`
	}

	resourcePath := fmt.Sprintf("/api/resources/%d", postResponse.ID)
	if err := client.PutJSON(ctx, resourcePath, updateBody, nil, nil, &putResponse); err != nil {
		fmt.Printf("PUT request failed: %v\n", err)
		return
	}

	// 4. DELETE example
	var deleteResponse struct {
		Deleted bool `json:"deleted"`
	}

	if err := client.DeleteJSON(ctx, resourcePath, nil, nil, &deleteResponse); err != nil {
		fmt.Printf("DELETE request failed: %v\n", err)
		return
	}

	fmt.Println("All API operations completed successfully")
}
