package utils

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"strconv"
	"time"

	"difyserver/config"
)

func GetSignedFileURL(uploadFileID string) (string, error) {
	baseURL := fmt.Sprintf("/files/%s/file-preview", uploadFileID)

	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	nonceBytes := make([]byte, 16)
	if _, err := rand.Read(nonceBytes); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}
	nonce := hex.EncodeToString(nonceBytes)

	msg := fmt.Sprintf("file-preview|%s|%s|%s", uploadFileID, timestamp, nonce)

	mac := hmac.New(sha256.New, []byte(config.GlobalConfig.DifySecretKey))
	mac.Write([]byte(msg))
	sign := mac.Sum(nil)

	encodedSign := base64.URLEncoding.EncodeToString(sign)

	return fmt.Sprintf("%s?timestamp=%s&nonce=%s&sign=%s", baseURL, timestamp, nonce, encodedSign), nil
}
