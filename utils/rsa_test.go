package utils

import (
	"encoding/base64"
	"testing"
)

func TestDecryptWithPrivateKey(t *testing.T) {
	// 正在测试的私钥
	str := `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
	txt := `SFlCUklEOgDDpRQkYtPom19g7fJpFau79HEPqHoMUQsqpqYW/myglKz6FT8F4HTJ3OlrZMotSGXefaQBy/zPQmkl4vCEpTEqeTBlUPjLfJWhg9yIl6wxuaZ2nigrxWTAP5ZzAuSJlMlP6Gm8yBZ/X6NtsFpUGLQ0rtJ3Qz9RAuVj8hgsz8I4b5FNLTxAP273+ol7rzJqkaF6MRpWHR4/PqTJmhn+FeHwAjFAJcyrLv38xnxVZqGjsve4VdbDLmrgAZj0j172pd1QI3O6hLPrreO7sKWHaBY+CzcKZaM5dWvEW4+3Pr8mMYa6NNLYTX7QQcOGjR2KQ+d1Q4Ny6PJ229d3brYxmcdzew4KIkmokpHQgCCWIC1FWqZwMsUEF7zNOrP8WSnywEZSQqWtqMHMBaMvKYjg0q8hDNlkHDnfOV5DRdtPmRgzuVsN`

	// 问题：SFlCUklEOg 是 "HYBRID:" 的 Base64 编码
	// 新的 Decrypt 函数期望数据格式为原始二进制: HYBRID: + encAESKey + nonce + ciphertext
	// 而不是 Base64 编码的字符串

	// 将原始加密字符串转换为字节数组
	encryptedBytes := []byte(txt)

	// 检查前缀是否为 Base64 编码的 "HYBRID:"
	if len(encryptedBytes) > 10 && string(encryptedBytes[:10]) == "SFlCUklEOg" {
		// 将整个字符串进行 Base64 解码
		decoded, err := base64.StdEncoding.DecodeString(txt)
		if err != nil {
			t.Errorf("Base64 解码失败: %v", err)
			return
		}

		// 如果解码成功，但得到的数据不以 "HYBRID:" 开头，添加前缀
		if len(decoded) > 0 && string(decoded[:7]) != "HYBRID:" {
			decoded = append([]byte("HYBRID:"), decoded...)
		}

		// 尝试解密
		plaintext, err := Decrypt(decoded, str)
		if err != nil {
			t.Errorf("解密失败 (方法1): %v", err)
			return
		} else {
			t.Logf("解密成功 (方法1): %s", plaintext)
		}
		publicKey := `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAymV58qsfAY7jpuRaSGqX
bHUp1mDcY8n+1MQuouK+36SyFpopVCAvCWu0MT42Wg0tqWSNGkYL57mE83B/r9xx
08XAWopciHTgJ1xmtYuUcPmy6jW8PZJNfWHfqsP0EYzEU3aNJ0GED3ZGE/NfhYd+
VCZ8e0wig/tdNHg143oVQWH4tiE3q+3ewWnwotpXbqK/zzh7IB39qA0TJNTbOzB/
XqrivrAjHclCsjdhRAgq3B8t1OgP6G2S5+xBZ3nIsmmqlrlQsZMoYHMRi9BVQXqy
EgI0ugDJpTxZfwUUng7vQm8aiI9hbnCl3LcBLnXiVu0PBCK4oC3iBI8YnOyVqLwL
SwIDAQAB
-----END PUBLIC KEY-----`
		encrypt, err := Encrypt(plaintext, publicKey)
		if err != nil {
			t.Errorf("加密失败: %v", err)
			return
		}
		t.Logf("加密成功: %s", base64.StdEncoding.EncodeToString(encrypt))
	}

}
