package utils

import (
	"time"

	"difyserver/config"
	"github.com/golang-jwt/jwt"
)

type Claims struct {
	ID     string `json:"id"`
	Email  string `json:"email"`
	UserID string `json:"user_id"`
	jwt.StandardClaims
}

func GenerateToken(id, email string) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(24 * time.Hour)

	claims := Claims{
		ID:    id,
		Email: email,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			IssuedAt:  nowTime.Unix(),
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString([]byte(config.GlobalConfig.DifySecretKey))
	return token, err
}

func ParseToken(token string) (*Claims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.GlobalConfig.DifySecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
		if claims.UserID != "" {
			claims.ID = claims.UserID
		}
		return claims, nil
	}

	return nil, err
}
