package utils

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"

	"golang.org/x/crypto/pbkdf2"
)

// HashPassword 使用系统相同的加密逻辑加密密码
func HashPassword(password string) (string, error) {
	// 生成随机盐值
	salt := make([]byte, 16)
	_, err := rand.Read(salt)
	if err != nil {
		return "", err
	}

	// 使用 PBKDF2 算法生成密钥（与系统一致）
	hashedPassword := hashPasswordWithSalt(password, salt)

	// 将密码转换为 base64 编码（与系统一致）
	base64Password := base64.StdEncoding.EncodeToString(hashedPassword)
	return base64Password, nil
}

// HashPasswordWithSalt 使用指定盐值加密密码，返回密码和盐值的base64编码
func HashPasswordWithSalt(password string, salt []byte) (string, string) {
	hashedPassword := hashPasswordWithSalt(password, salt)
	base64Password := base64.StdEncoding.EncodeToString(hashedPassword)
	base64Salt := base64.StdEncoding.EncodeToString(salt)
	return base64Password, base64Salt
}

// hashPasswordWithSalt 内部函数，使用PBKDF2算法加密密码
func hashPasswordWithSalt(password string, salt []byte) []byte {
	return pbkdf2.Key([]byte(password), salt, 10000, 32, sha256.New)
}

func GetPassword(password string) (string, string, error) {
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return "", "", err
	}

	// 使用新盐值加密新密码
	byteArray := hashPassword(password, salt)
	// 将密码和盐值转换为 base64 编码
	hashedPassword := []byte(byteArray)

	base64Password := base64.StdEncoding.EncodeToString(hashedPassword)
	base64Salt := base64.StdEncoding.EncodeToString(salt)
	return base64Password, base64Salt, nil
}

func hashPassword(password string, salt []byte) string {
	// 生成 PBKDF2 密钥
	dk := pbkdf2.Key([]byte(password), salt, 10000, 32, sha256.New)
	// 转换为十六进制字符串
	return hex.EncodeToString(dk)
}
