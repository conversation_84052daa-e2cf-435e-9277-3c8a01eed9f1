package main

import (
	"context"
	"log"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"difyserver/config"
	"difyserver/database"
	"difyserver/handlers"
	"difyserver/scheduler"
	"github.com/gin-gonic/gin"

	// Swagger 文档
	_ "difyserver/docs" // 导入生成的 docs 包
)

// @title Dify Server API
// @version 1.0
// @description Dify Server 的 API 文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.example.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api
// @schemes http https
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization

func main() {
	// 设置全局时区为东八区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Fatal("加载时区失败:", err)
	}
	time.Local = loc

	// 加载配置
	if err := config.LoadConfig(); err != nil {
		log.Fatal("加载配置失败:", err)
	}
	//slog.SetLogLoggerLevel(slog.LevelInfo)
	opts := &slog.HandlerOptions{
		Level: slog.LevelInfo, // Log all DEBUG level messages and above
	}
	handler := slog.NewTextHandler(os.Stdout, opts) // Output to console in text format
	slog.SetDefault(slog.New(handler))

	if err := database.InitDB(); err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 启动定时任务调度器
	taskScheduler := scheduler.NewScheduler(&config.GlobalConfig)
	if err := taskScheduler.Start(); err != nil {
		log.Fatal("启动定时任务调度器失败:", err)
	}

	// 创建Gin服务器
	r := gin.Default()

	// 加载HTML模板
	r.LoadHTMLGlob("templates/*")

	handlers.InitHandler(r)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    ":8080",
		Handler: r,
	}

	// 启动服务器（非阻塞）
	go func() {
		log.Println("开始监听端口 :8080")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器端口监听失败: %s\n", err)
		}
	}()

	// 设置信号通道来等待中断信号
	quit := make(chan os.Signal, 1)
	// kill 发送 syscall.SIGTERM 信号，是默认的信号处理行为
	// kill -2 发送 syscall.SIGINT 信号，ctrl+c也会发送该信号
	// kill -9 发送 syscall.SIGKILL 信号，但是不能被捕获，所以不需要添加到这里
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 设置关闭超时为5秒
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// 优雅关闭服务器
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("服务器关闭失败:", err)
	}

	// 等待关闭操作完成
	<-ctx.Done()
	log.Println("关闭定时任务调度器...")
	taskScheduler.Stop()
	log.Println("关闭数据库连接...")
	// 在这里添加数据库关闭等清理工作
	// TODO: database.Close() 如果有类似方法
	log.Println("服务器已完全关闭")
}
