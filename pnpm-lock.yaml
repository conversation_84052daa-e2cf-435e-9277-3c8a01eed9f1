lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@codemirror/highlight':
        specifier: ^0.19.8
        version: 0.19.8
      '@codemirror/stream-parser':
        specifier: ^0.19.9
        version: 0.19.9
    devDependencies:
      '':
        specifier: link:/
        version: link:../..
      '@codemirror/state':
        specifier: '6'
        version: 6.5.2

packages:

  '@codemirror/highlight@0.19.8':
    resolution: {integrity: sha512-v/lzuHjrYR8MN2mEJcUD6fHSTXXli9C1XGYpr+ElV6fLBIUhMTNKR3qThp611xuWfXfwDxeL7ppcbkM/MzPV3A==}
    deprecated: As of 0.20.0, this package has been split between @lezer/highlight and @codemirror/language

  '@codemirror/language@0.19.10':
    resolution: {integrity: sha512-yA0DZ3RYn2CqAAGW62VrU8c4YxscMQn45y/I9sjBlqB1e2OTQLg4CCkMBuMSLXk4xaqjlsgazeOQWaJQOKfV8Q==}

  '@codemirror/rangeset@0.19.9':
    resolution: {integrity: sha512-V8YUuOvK+ew87Xem+71nKcqu1SXd5QROMRLMS/ljT5/3MCxtgrRie1Cvild0G/Z2f1fpWxzX78V0U4jjXBorBQ==}
    deprecated: As of 0.20.0, this package has been merged into @codemirror/state

  '@codemirror/state@0.19.9':
    resolution: {integrity: sha512-psOzDolKTZkx4CgUqhBQ8T8gBc0xN5z4gzed109aF6x7D7umpDRoimacI/O6d9UGuyl4eYuDCZmDFr2Rq7aGOw==}

  '@codemirror/state@6.5.2':
    resolution: {integrity: sha512-FVqsPqtPWKVVL3dPSxy8wEF/ymIEuVzF1PK3VbUgrxXpJUSHQWWZz4JMToquRxnkw+36LTamCZG2iua2Ptq0fA==}

  '@codemirror/stream-parser@0.19.9':
    resolution: {integrity: sha512-WTmkEFSRCetpk8xIOvV2yyXdZs3DgYckM0IP7eFi4ewlxWnJO/H4BeJZLs4wQaydWsAqTQoDyIwNH1BCzK5LUQ==}
    deprecated: As of 0.20.0, this package has been merged into @codemirror/language

  '@codemirror/text@0.19.6':
    resolution: {integrity: sha512-T9jnREMIygx+TPC1bOuepz18maGq/92q2a+n4qTqObKwvNMg+8cMTslb8yxeEDEq7S3kpgGWxgO1UWbQRij0dA==}
    deprecated: As of 0.20.0, this package has been merged into @codemirror/state

  '@codemirror/view@0.19.48':
    resolution: {integrity: sha512-0eg7D2Nz4S8/caetCTz61rK0tkHI17V/d15Jy0kLOT8dTLGGNJUponDnW28h2B6bERmPlVHKh8MJIr5OCp1nGw==}

  '@lezer/common@0.15.12':
    resolution: {integrity: sha512-edfwCxNLnzq5pBA/yaIhwJ3U3Kz8VAUOTRg0hhxaizaI1N+qxV7EXDv/kLCkLeq2RzSFvxexlaj5Mzfn2kY0Ig==}

  '@lezer/lr@0.15.8':
    resolution: {integrity: sha512-bM6oE6VQZ6hIFxDNKk8bKPa14hqFrV07J/vHGOeiAbJReIaQXmkVb6xQu4MR+JBTLa5arGRyAAjJe1qaQt3Uvg==}

  '@marijn/find-cluster-break@1.0.2':
    resolution: {integrity: sha512-l0h88YhZFyKdXIFNfSWpyjStDjGHwZ/U7iobcK1cQQD8sejsONdQtTVU+1wVN1PBw40PiiHB1vA5S7VTfQiP9g==}

  style-mod@4.1.2:
    resolution: {integrity: sha512-wnD1HyVqpJUI2+eKZ+eo1UwghftP6yuFheBqqe+bWCotBjC2K1YnteJILRMs3SM4V/0dLEW1SC27MWP5y+mwmw==}

  w3c-keyname@2.2.8:
    resolution: {integrity: sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==}

snapshots:

  '@codemirror/highlight@0.19.8':
    dependencies:
      '@codemirror/language': 0.19.10
      '@codemirror/rangeset': 0.19.9
      '@codemirror/state': 0.19.9
      '@codemirror/view': 0.19.48
      '@lezer/common': 0.15.12
      style-mod: 4.1.2

  '@codemirror/language@0.19.10':
    dependencies:
      '@codemirror/state': 0.19.9
      '@codemirror/text': 0.19.6
      '@codemirror/view': 0.19.48
      '@lezer/common': 0.15.12
      '@lezer/lr': 0.15.8

  '@codemirror/rangeset@0.19.9':
    dependencies:
      '@codemirror/state': 0.19.9

  '@codemirror/state@0.19.9':
    dependencies:
      '@codemirror/text': 0.19.6

  '@codemirror/state@6.5.2':
    dependencies:
      '@marijn/find-cluster-break': 1.0.2

  '@codemirror/stream-parser@0.19.9':
    dependencies:
      '@codemirror/highlight': 0.19.8
      '@codemirror/language': 0.19.10
      '@codemirror/state': 0.19.9
      '@codemirror/text': 0.19.6
      '@lezer/common': 0.15.12
      '@lezer/lr': 0.15.8

  '@codemirror/text@0.19.6': {}

  '@codemirror/view@0.19.48':
    dependencies:
      '@codemirror/rangeset': 0.19.9
      '@codemirror/state': 0.19.9
      '@codemirror/text': 0.19.6
      style-mod: 4.1.2
      w3c-keyname: 2.2.8

  '@lezer/common@0.15.12': {}

  '@lezer/lr@0.15.8':
    dependencies:
      '@lezer/common': 0.15.12

  '@marijn/find-cluster-break@1.0.2': {}

  style-mod@4.1.2: {}

  w3c-keyname@2.2.8: {}
