app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: Excel解析生成图表
  use_icon_as_answer_icon: false
dependencies:
  - current_identifier: null
    type: marketplace
    value:
      marketplace_plugin_unique_identifier: langgenius/volcengine_maas:0.0.7@f8e44422cfa5b9a6ac1f2d3b43ef1069868efdad1e5cec2590de3f53ceac37b0
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
        - .JPG
        - .JPEG
        - .PNG
        - .GIF
        - .WEBP
        - .SVG
      allowed_file_types:
        - image
      allowed_file_upload_methods:
        - local_file
        - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 500
        batch_count_limit: 50
        file_size_limit: 1024
        image_file_size_limit: 1000
        video_file_size_limit: 10000
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
          - local_file
          - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
      - data:
          isInIteration: false
          isInLoop: false
          sourceType: document-extractor
          targetType: llm
        id: 1741962070164-source-llm-target
        source: '1741962070164'
        sourceHandle: source
        target: llm
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          isInLoop: false
          sourceType: start
          targetType: if-else
        id: 1741961981530-source-1741962638469-target
        source: '1741961981530'
        sourceHandle: source
        target: '1741962638469'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: if-else
          targetType: document-extractor
        id: 1741962638469-true-1741962070164-target
        source: '1741962638469'
        sourceHandle: 'true'
        target: '1741962070164'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          isInLoop: false
          sourceType: if-else
          targetType: answer
        id: 1741962638469-false-1741962945275-target
        source: '1741962638469'
        sourceHandle: 'false'
        target: '1741962945275'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          isInLoop: false
          sourceType: code
          targetType: answer
        id: 1741965295287-source-answer-target
        source: '1741965295287'
        sourceHandle: source
        target: answer
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInLoop: false
          sourceType: llm
          targetType: code
        id: llm-source-1741965295287-target
        source: llm
        sourceHandle: source
        target: '1741965295287'
        targetHandle: target
        type: custom
        zIndex: 0
    nodes:
      - data:
          desc: ''
          selected: false
          title: 开始
          type: start
          variables:
            - allowed_file_extensions: []
              allowed_file_types:
                - document
              allowed_file_upload_methods:
                - local_file
              label: 请提供excel
              max_length: 1
              options: []
              required: true
              type: file-list
              variable: excel
        height: 90
        id: '1741961981530'
        position:
          x: 30
          y: 275
        positionAbsolute:
          x: 30
          y: 275
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          context:
            enabled: false
            variable_selector: []
          desc: ''
          model:
            completion_params:
              temperature: 0.7
            mode: chat
            name: DeepSeek-V3
            provider: langgenius/volcengine_maas/volcengine_maas
          prompt_template:
            - id: 5146b7a9-3769-4493-831d-a75bc903a1f7
              role: system
              text: "你是一个文档提取专家，请根据用户上传文档内容{{#1741962070164.text#}}提取该表格里面的数据返回json格式数据。\n\
            其中内容格式‘文章日阅读量’、‘Dify安装部署’、‘RAGFlow安装部署’、‘Dify联网查询’、‘Dify翻译小助手’、‘Dify创建知识库’字段返回信息，返回的结果信息以json格式返回\n\
            \n 返回数据格式如下：\n{\n\"文章日阅读量\": [\n{\n\"date\": \"2025-3-1\",\n\"Dify安装部署\"\
            : 200,\n\"RAGFlow安装部署\": 300,\n\"Dify联网查询\": 125,\n\"Dify翻译小助手\": 351，\n\
            “Dify创建知识库”: 123\n},\n{\n\"date\": \"2025-3-2\",\n\"Dify安装部署\": 323,\n\
            \"RAGFlow安装部署\": 335,\n\"Dify联网查询\": 1321,\n\"Dify翻译小助手\": 514,\n“Dify创建知识库”:\
            \ 153\n},\n]\n}\n"
          selected: false
          title: 文档处理LLM
          type: llm
          variables: []
          vision:
            enabled: false
        height: 90
        id: llm
        position:
          x: 944.6224968394765
          y: 275
        positionAbsolute:
          x: 944.6224968394765
          y: 275
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          answer: '{{#1741965295287.result#}}

          '
          desc: ''
          selected: false
          title: 直接回复
          type: answer
          variables: []
        height: 105
        id: answer
        position:
          x: 1215.100652446408
          y: 414.8319557205739
        positionAbsolute:
          x: 1215.100652446408
          y: 414.8319557205739
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          desc: ''
          is_array_file: true
          selected: false
          title: 文档提取器
          type: document-extractor
          variable_selector:
            - '1741961981530'
            - excel
        height: 92
        id: '1741962070164'
        position:
          x: 638
          y: 275
        positionAbsolute:
          x: 638
          y: 275
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          cases:
            - case_id: 'true'
              conditions:
                - comparison_operator: contains
                  id: 3b2c44f2-2090-4fa6-a66f-d6e99cb90481
                  sub_variable_condition:
                    case_id: 24fd5e4a-804e-4a45-914f-d86ad7711a68
                    conditions:
                      - comparison_operator: contains
                        id: ecf0b999-0b72-49ab-8c37-c5487ca2bf47
                        key: extension
                        value: xls
                        varType: string
                    logical_operator: and
                  value: ''
                  varType: array[file]
                  variable_selector:
                    - '1741961981530'
                    - excel
              id: 'true'
              logical_operator: or
          desc: ''
          selected: false
          title: 条件分支
          type: if-else
        height: 150
        id: '1741962638469'
        position:
          x: 334
          y: 275
        positionAbsolute:
          x: 334
          y: 275
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          answer: 当前文档不是excel
          desc: ''
          selected: true
          title: 直接回复 2
          type: answer
          variables: []
        height: 102
        id: '1741962945275'
        position:
          x: 638
          y: 407
        positionAbsolute:
          x: 638
          y: 407
        selected: true
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          code: "import json\nimport os\nimport re\n\ndef main(json_str: str) -> dict:\n\
          \    try:\n        # 预处理：清理非 JSON 部分\n        json_match = re.search(r'\\\
          {.*\\}', json_str, re.DOTALL)\n        if not json_match:\n            return\
          \ {\"result\": \"Error: Invalid JSON format\"}\n        \n        # 提取合法的\
          \ JSON 部分\n        cleaned_json_str = json_match.group(0)\n        \n  \
          \      # 解析JSON数据\n        data = json.loads(cleaned_json_str)\n       \
          \ filename = data.get(\"filename\", \"stock_chart.html\")\n        stock_data\
          \ = data.get(\"文章日阅读量\", [])\n        \n        # 准备数据格式\n        dates\
          \ = [item['date'].split(' ')[0] for item in stock_data]  # 提取日期部分（去掉时间）\n\
          \        articles_read_num1 = [item['Dify安装部署'] for item in stock_data]\n\
          \        articles_read_num2 = [item['RAGFlow安装部署'] for item in stock_data]\n\
          \        articles_read_num3 = [item['Dify联网查询'] for item in stock_data]\n\
          \        articles_read_num4 = [item['Dify翻译小助手'] for item in stock_data]\n\
          \        articles_read_num5 = [item['Dify创建知识库'] for item in stock_data]\n\
          \        \n        # 构建ECharts配置\n        echarts_config = {\n         \
          \   \"title\": {\n                \"text\": \"文章日阅读量\"\n            },\n\
          \            \"legend\": {\n                \"data\": [\"Dify安装部署\", \"\
          RAGFlow安装部署\", \"Dify联网查询\", \"Dify翻译小助手\", \"Dify创建知识库\"]\n           \
          \ },\n            \"tooltip\": {},\n            \"dataset\": {\n       \
          \         \"source\": [\n                     [\"日期\", \"Dify安装部署\", \"\
          RAGFlow安装部署\", \"Dify联网查询\", \"Dify翻译小助手\", \"Dify创建知识库\"],\n          \
          \          *[[dates[i], articles_read_num1[i], articles_read_num2[i], articles_read_num3[i],\
          \ articles_read_num4[i], articles_read_num5[i]] \n                     \
          \ for i in range(len(dates))]\n                ]\n            },\n     \
          \       \"xAxis\": [\n                {\"type\": \"category\", \"gridIndex\"\
          : 0}\n                \n            ],\n            \"yAxis\": [\n     \
          \           {\n                    \"gridIndex\": 0,\n                 \
          \   \"name\": \"日阅读量（单位：次）\"\n                }\n                \n    \
          \        ],\n            \"grid\": [\n                {\"bottom\": \"55%\"\
          },\n                {\"top\": \"55%\"}\n            ],\n            \"series\"\
          : [\n                # 折线图系列\n                # {\"type\": \"line\", \"\
          seriesLayoutBy\": \"row\", \"name\": \"Dify安装部署\"},\n                # {\"\
          type\": \"line\", \"seriesLayoutBy\": \"row\", \"name\": \"RAGFlow安装部署\"\
          },\n                # {\"type\": \"line\", \"seriesLayoutBy\": \"row\",\
          \ \"name\": \"Dify联网查询\"},\n                # {\"type\": \"line\", \"seriesLayoutBy\"\
          : \"row\", \"name\": \"Dify翻译小助手\"},\n                # {\"type\": \"line\"\
          , \"seriesLayoutBy\": \"row\", \"name\": \"Dify创建知识库\"}\n        \n    \
          \            # 柱状图系列\n                {\"type\": \"bar\", \"xAxisIndex\"\
          : 0, \"yAxisIndex\": 0, \"name\": \"Dify安装部署\"},\n                {\"type\"\
          : \"bar\", \"xAxisIndex\": 0, \"yAxisIndex\": 0, \"name\": \"RAGFlow安装部署\"\
          },\n                {\"type\": \"bar\", \"xAxisIndex\": 0, \"yAxisIndex\"\
          : 0, \"name\": \"Dify联网查询\"},\n                {\"type\": \"bar\", \"xAxisIndex\"\
          : 0, \"yAxisIndex\": 0, \"name\": \"Dify翻译小助手\"},\n                {\"type\"\
          : \"bar\", \"xAxisIndex\": 0, \"yAxisIndex\": 0, \"name\": \"Dify创建知识库\"\
          }\n            ]\n        }\n        \n        # 生成输出文件\n        output\
          \ = \"```echarts\\n\" + json.dumps(echarts_config, indent=2, ensure_ascii=False)\
          \ + \"\\n```\"\n        \n        # 返回结果\n        return {\n           \
          \ \"result\": output\n        }\n    \n    except Exception as e:\n    \
          \    return {\n            \"result\": f\"Error: {str(e)}\"\n        }"
          code_language: python3
          desc: ''
          outputs:
            result:
              children: null
              type: string
          selected: false
          title: 代码执行
          type: code
          variables:
            - value_selector:
                - llm
                - text
              variable: json_str
        height: 54
        id: '1741965295287'
        position:
          x: 1244.7895556070257
          y: 275
        positionAbsolute:
          x: 1244.7895556070257
          y: 275
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
    viewport:
      x: -231.4144266814012
      y: -33.558973525201964
      zoom: 0.8375732611651916