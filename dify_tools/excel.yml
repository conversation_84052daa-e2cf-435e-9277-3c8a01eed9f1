app:
  description: 'Excel数据分析工具，支持上传Excel文件并根据客户档位进行统计分析，生成可视化图表'
  icon: 📊
  icon_background: '#E6F7FF'
  mode: workflow
  name: Excel数据统计分析
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/code_executor:0.0.1@abc123
kind: app
version: 0.2.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: [.xlsx, .xls, .csv]
      allowed_file_types:
      - document
      allowed_file_upload_methods:
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 50
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      number_limits: 1
    opening_statement: '欢迎使用Excel数据统计分析工具！请上传您的Excel文件，我将为您提取数据并根据客户档位进行统计分析，生成可视化图表。'
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: document-extractor
      id: start-source-document-extractor-target
      source: 'start'
      sourceHandle: source
      target: 'document_extractor'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: document-extractor
        targetType: llm
      id: document-extractor-source-data-processor-target
      source: 'document_extractor'
      sourceHandle: source
      target: 'data_processor'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: data-processor-source-statistics-analyzer-target
      source: 'data_processor'
      sourceHandle: source
      target: 'statistics_analyzer'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: statistics-analyzer-source-end-target
      source: 'statistics_analyzer'
      sourceHandle: source
      target: 'end'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: [.xlsx, .xls, .csv]
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          label: 请上传Excel文件
          max_length: 1
          options: []
          required: true
          type: file-list
          variable: excel_file
      height: 167
      id: 'start'
      position:
        x: 30
        y: 283.5
      positionAbsolute:
        x: 30
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '提取Excel文件中的数据内容'
        is_array_file: true
        selected: false
        title: 文档提取器
        type: document-extractor
        variables:
        - variable: excel_file
      height: 89
      id: 'document_extractor'
      position:
        x: 334
        y: 283.5
      positionAbsolute:
        x: 334
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - 'document_extractor'
          - text
        desc: '将提取的Excel数据转换为JSON格式'
        model:
          completion_params:
            temperature: 0.1
            max_tokens: 4000
          mode: chat
          name: gpt-4o-mini
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: "你是一个Excel数据提取专家，请根据用户上传的Excel文档内容提取表格数据并返回JSON格式。\n\n数据格式包含以下字段：\n操作、地市、区县、客户编码、客户名称、上期库存（条）、期间购进（条）、本期库存（条）、采集日期、采集员、提交时间、状态、数据异常、是否按时填报、采集方式、采集渠道、采集时长（分钟）、采集开始时间、采集结束时间、签到偏差距离、签退偏差距离、订单日期、客户档位、电话\n\n请严格按照以下JSON格式返回数据：\n{\n  \"数据\": [\n    {\n      \"操作\": \"值\",\n      \"地市\": \"值\",\n      \"区县\": \"值\",\n      \"客户编码\": \"值\",\n      \"客户名称\": \"值\",\n      \"上期库存（条）\": 数值,\n      \"期间购进（条）\": 数值,\n      \"本期库存（条）\": 数值,\n      \"采集日期\": \"日期\",\n      \"采集员\": \"值\",\n      \"提交时间\": \"时间\",\n      \"状态\": \"值\",\n      \"数据异常\": \"值\",\n      \"是否按时填报\": \"值\",\n      \"采集方式\": \"值\",\n      \"采集渠道\": \"值\",\n      \"采集时长（分钟）\": 数值,\n      \"采集开始时间\": \"时间\",\n      \"采集结束时间\": \"时间\",\n      \"签到偏差距离\": 数值,\n      \"签退偏差距离\": 数值,\n      \"订单日期\": \"日期\",\n      \"客户档位\": \"值\",\n      \"电话\": \"值\"\n    }\n  ]\n}\n\n注意：\n1. 数值字段请转换为数字类型\n2. 日期和时间字段保持字符串格式\n3. 确保JSON格式正确\n4. 如果某个字段为空，请使用null或空字符串"
        - id: user-prompt
          role: user
          text: "请提取以下Excel数据并转换为JSON格式：\n\n{{#context#}}"
        selected: false
        title: 数据处理器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 97
      id: 'data_processor'
      position:
        x: 638
        y: 283.5
      positionAbsolute:
        x: 638
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: |
          import json
          import re
          from collections import Counter
          
          def main(json_str: str) -> dict:
              try:
                  # 预处理：清理非 JSON 部分
                  json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                  if not json_match:
                      return {"result": "Error: Invalid JSON format"}
                  
                  # 提取合法的 JSON 部分
                  cleaned_json_str = json_match.group(0)
                  
                  # 解析JSON数据
                  data = json.loads(cleaned_json_str)
                  
                  # 获取客户档位数据
                  customer_data = data.get("数据", [])
                  
                  if not customer_data:
                      return {"result": "Error: 未找到有效数据"}
                  
                  # 统计客户档位数量
                  档位_counts = Counter([item.get("客户档位", "未知") for item in customer_data])
                  
                  # 转换为列表格式
                  档位_labels = list(档位_counts.keys())
                  档位_values = list(档位_counts.values())
                  
                  # 构建ECharts饼图配置
                  pie_config = {
                      "title": {
                          "text": "客户档位分布统计",
                          "left": "center"
                      },
                      "tooltip": {
                          "trigger": "item",
                          "formatter": "{a} <br/>{b}: {c} ({d}%)"
                      },
                      "legend": {
                          "orient": "vertical",
                          "left": "left",
                          "data": 档位_labels
                      },
                      "series": [
                          {
                              "name": "客户档位",
                              "type": "pie",
                              "radius": ["50%", "70%"],
                              "avoidLabelOverlap": False,
                              "itemStyle": {
                                  "borderRadius": 10,
                                  "borderColor": "#fff",
                                  "borderWidth": 2
                              },
                              "label": {
                                  "show": True,
                                  "formatter": "{b}: {c} ({d}%)"
                              },
                              "emphasis": {
                                  "label": {
                                      "show": True,
                                      "fontSize": 16,
                                      "fontWeight": "bold"
                                  }
                              },
                              "data": [{"value": 档位_values[i], "name": 档位_labels[i]} for i in range(len(档位_labels))]
                          }
                      ]
                  }
                  
                  # 构建ECharts柱状图配置
                  bar_config = {
                      "title": {
                          "text": "客户档位数量统计",
                          "left": "center"
                      },
                      "tooltip": {
                          "trigger": "axis"
                      },
                      "xAxis": {
                          "type": "category",
                          "data": 档位_labels,
                          "axisLabel": {
                              "interval": 0,
                              "rotate": 45
                          }
                      },
                      "yAxis": {
                          "type": "value",
                          "name": "数量"
                      },
                      "series": [
                          {
                              "name": "客户数量",
                              "type": "bar",
                              "data": 档位_values,
                              "itemStyle": {
                                  "color": "#3f51b5"
                              },
                              "label": {
                                  "show": True,
                                  "position": "top"
                              }
                          }
                      ]
                  }
                  
                  # 生成统计摘要
                  total_customers = sum(档位_values)
                  summary = f"## 数据统计摘要\n\n"
                  summary += f"- **总客户数量**: {total_customers}\n"
                  summary += f"- **档位种类数**: {len(档位_labels)}\n\n"
                  summary += "### 各档位详细统计:\n"
                  for i, label in enumerate(档位_labels):
                      percentage = (档位_values[i] / total_customers) * 100
                      summary += f"- **{label}**: {档位_values[i]} 个客户 ({percentage:.1f}%)\n"
                  
                  # 生成图表输出
                  pie_output = "```echarts\n" + json.dumps(pie_config, indent=2, ensure_ascii=False) + "\n```"
                  bar_output = "```echarts\n" + json.dumps(bar_config, indent=2, ensure_ascii=False) + "\n```"
                  
                  # 返回完整结果
                  result = f"{summary}\n\n## 客户档位分布饼图\n{pie_output}\n\n## 客户档位数量柱状图\n{bar_output}"
                  
                  return {"result": result}
              
              except Exception as e:
                  return {"result": f"Error: {str(e)}"}
        code_language: python3
        desc: '对提取的数据进行客户档位统计分析并生成图表'
        outputs:
          result:
            type: string
        selected: false
        title: 统计分析器
        type: code
        variables:
        - value_selector:
          - 'data_processor'
          - text
          variable: json_str
      height: 97
      id: 'statistics_analyzer'
      position:
        x: 942
        y: 283.5
      positionAbsolute:
        x: 942
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - 'statistics_analyzer'
          - result
          variable: analysis_result
        selected: false
        title: 结束
        type: end
      height: 89
      id: 'end'
      position:
        x: 1246
        y: 283.5
      positionAbsolute:
        x: 1246
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
