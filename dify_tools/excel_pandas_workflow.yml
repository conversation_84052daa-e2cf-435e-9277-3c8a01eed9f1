app:
  description: '增强版Excel数据分析工具，使用pandas进行深度数据分析和可视化'
  icon: 📊
  icon_background: '#E6F7FF'
  mode: workflow
  name: Excel数据分析(Pandas增强版)
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/code_executor:0.0.1@abc123
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: [.xlsx, .xls, .csv]
      allowed_file_types:
      - document
      allowed_file_upload_methods:
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 100
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      number_limits: 3
    opening_statement: '欢迎使用增强版Excel数据分析工具！本工具使用pandas进行深度数据分析，支持多文件上传、高级统计分析和丰富的可视化图表。请上传您的Excel文件开始分析。'
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: 
    - "请分析客户档位分布情况"
    - "对比各地市的库存数据"
    - "分析采集效率和时间分布"
    - "检查数据质量和异常情况"
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: document-extractor
      id: start-source-document-extractor-target
      source: 'start'
      sourceHandle: source
      target: 'document_extractor'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: document-extractor
        targetType: llm
      id: document-extractor-source-data-processor-target
      source: 'document_extractor'
      sourceHandle: source
      target: 'data_processor'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: data-processor-source-pandas-analyzer-target
      source: 'data_processor'
      sourceHandle: source
      target: 'pandas_analyzer'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: pandas-analyzer-source-end-target
      source: 'pandas_analyzer'
      sourceHandle: source
      target: 'end'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: '用户上传Excel文件入口'
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: [.xlsx, .xls, .csv]
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          label: 请上传Excel文件(支持多文件)
          max_length: 3
          options: []
          required: true
          type: file-list
          variable: excel_files
        - label: 分析类型
          max_length: 48
          options: 
          - 基础统计分析
          - 客户档位分析
          - 地区分布分析
          - 库存趋势分析
          - 时间效率分析
          - 综合分析报告
          required: false
          type: select
          variable: analysis_type
      height: 167
      id: 'start'
      position:
        x: 30
        y: 283.5
      positionAbsolute:
        x: 30
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '提取Excel文件中的数据内容，支持多文件处理'
        is_array_file: true
        selected: false
        title: 文档提取器
        type: document-extractor
        variables:
        - variable: excel_files
      height: 89
      id: 'document_extractor'
      position:
        x: 334
        y: 283.5
      positionAbsolute:
        x: 334
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - 'document_extractor'
          - text
        desc: '将提取的Excel数据转换为标准JSON格式，便于pandas处理'
        model:
          completion_params:
            temperature: 0.1
            max_tokens: 8000
          mode: chat
          name: gpt-4o-mini
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个Excel数据提取专家，请根据用户上传的Excel文档内容提取表格数据并返回标准JSON格式。

            数据字段包含但不限于：
            操作、地市、区县、客户编码、客户名称、上期库存（条）、期间购进（条）、本期库存（条）、
            采集日期、采集员、提交时间、状态、数据异常、是否按时填报、采集方式、采集渠道、
            采集时长（分钟）、采集开始时间、采集结束时间、签到偏差距离、签退偏差距离、
            订单日期、客户档位、电话

            请严格按照以下JSON格式返回数据：
            {
              "数据": [
                {
                  "操作": "值",
                  "地市": "值",
                  "区县": "值",
                  "客户编码": "值",
                  "客户名称": "值",
                  "上期库存（条）": 数值,
                  "期间购进（条）": 数值,
                  "本期库存（条）": 数值,
                  "采集日期": "日期",
                  "采集员": "值",
                  "提交时间": "时间",
                  "状态": "值",
                  "数据异常": "值",
                  "是否按时填报": "值",
                  "采集方式": "值",
                  "采集渠道": "值",
                  "采集时长（分钟）": 数值,
                  "采集开始时间": "时间",
                  "采集结束时间": "时间",
                  "签到偏差距离": 数值,
                  "签退偏差距离": 数值,
                  "订单日期": "日期",
                  "客户档位": "值",
                  "电话": "值"
                }
              ],
              "元数据": {
                "文件名": "文件名",
                "处理时间": "时间戳",
                "记录总数": 数值,
                "数据来源": "Excel文件"
              }
            }

            注意事项：
            1. 数值字段请转换为数字类型，如果无法转换则使用null
            2. 日期和时间字段保持字符串格式
            3. 确保JSON格式正确且可解析
            4. 如果某个字段为空，请使用null
            5. 保持数据的原始性和完整性
            6. 如果有多个工作表，请合并所有数据
        - id: user-prompt
          role: user
          text: |
            请提取以下Excel数据并转换为标准JSON格式：

            {{#context#}}

            分析类型要求：{{#analysis_type#}}
        selected: false
        title: 数据处理器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 97
      id: 'data_processor'
      position:
        x: 638
        y: 283.5
      positionAbsolute:
        x: 638
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: |
          import json
          import re
          import pandas as pd
          import numpy as np
          from datetime import datetime
          from collections import Counter

          def main(json_str: str, analysis_type: str = "综合分析报告") -> dict:
              """
              使用pandas进行Excel数据深度分析
              """
              try:
                  # 1. 预处理：清理非JSON部分
                  json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                  if not json_match:
                      return {"result": "❌ Error: Invalid JSON format"}

                  # 提取合法的JSON部分
                  cleaned_json_str = json_match.group(0)

                  # 2. 解析JSON数据
                  data = json.loads(cleaned_json_str)
                  customer_data = data.get("数据", [])
                  metadata = data.get("元数据", {})

                  if not customer_data:
                      return {"result": "❌ Error: 未找到有效数据"}

                  # 3. 将JSON数据转换为pandas DataFrame
                  df = pd.DataFrame(customer_data)

                  # 4. 数据清洗和类型转换
                  df = clean_and_convert_data(df)

                  # 5. 根据分析类型执行相应分析
                  if analysis_type == "基础统计分析":
                      result = basic_statistics_analysis(df, metadata)
                  elif analysis_type == "客户档位分析":
                      result = customer_grade_analysis(df, metadata)
                  elif analysis_type == "地区分布分析":
                      result = regional_analysis(df, metadata)
                  elif analysis_type == "库存趋势分析":
                      result = inventory_analysis(df, metadata)
                  elif analysis_type == "时间效率分析":
                      result = time_efficiency_analysis(df, metadata)
                  else:  # 综合分析报告
                      result = comprehensive_analysis(df, metadata)

                  return {"result": result}

              except Exception as e:
                  return {"result": f"❌ Error: {str(e)}"}


          def clean_and_convert_data(df):
              """数据清洗和类型转换"""
              # 数值字段转换
              numeric_columns = [
                  '上期库存（条）', '期间购进（条）', '本期库存（条）',
                  '采集时长（分钟）', '签到偏差距离', '签退偏差距离'
              ]

              for col in numeric_columns:
                  if col in df.columns:
                      df[col] = pd.to_numeric(df[col], errors='coerce')

              # 日期字段转换
              date_columns = ['采集日期', '订单日期']
              for col in date_columns:
                  if col in df.columns:
                      df[col] = pd.to_datetime(df[col], errors='coerce')

              # 时间字段转换
              time_columns = ['提交时间', '采集开始时间', '采集结束时间']
              for col in time_columns:
                  if col in df.columns:
                      df[col] = pd.to_datetime(df[col], errors='coerce')

              # 填充缺失值
              df = df.fillna({
                  '客户档位': '未知',
                  '地市': '未知',
                  '区县': '未知',
                  '状态': '未知',
                  '采集方式': '未知'
              })

              return df


          def basic_statistics_analysis(df, metadata):
              """基础统计分析"""
              report = "# 📊 基础统计分析报告\n\n"

              # 基本信息
              report += "## 📋 数据概览\n\n"
              report += f"- **数据来源**: {metadata.get('文件名', '未知')}\n"
              report += f"- **处理时间**: {metadata.get('处理时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}\n"
              report += f"- **记录总数**: {len(df)}\n"
              report += f"- **字段数量**: {len(df.columns)}\n\n"

              # 数据类型分布
              report += "## 🔍 数据类型分布\n\n"
              dtype_counts = df.dtypes.value_counts()
              for dtype, count in dtype_counts.items():
                  report += f"- **{dtype}**: {count} 个字段\n"
              report += "\n"

              # 缺失值统计
              missing_data = df.isnull().sum()
              if missing_data.sum() > 0:
                  report += "## ⚠️ 缺失值统计\n\n"
                  for col, missing_count in missing_data.items():
                      if missing_count > 0:
                          percentage = (missing_count / len(df)) * 100
                          report += f"- **{col}**: {missing_count} 个缺失值 ({percentage:.1f}%)\n"
                  report += "\n"
              else:
                  report += "## ✅ 数据完整性\n\n数据完整，无缺失值\n\n"

              return report


          def customer_grade_analysis(df, metadata):
              """客户档位分析"""
              report = "# 🏷️ 客户档位分析报告\n\n"

              if '客户档位' not in df.columns:
                  return report + "❌ 数据中未找到客户档位字段\n"

              # 档位分布统计
              grade_counts = df['客户档位'].value_counts()
              grade_percentages = (grade_counts / len(df) * 100).round(2)

              report += "## 📊 档位分布统计\n\n"
              for grade, count in grade_counts.items():
                  pct = grade_percentages[grade]
                  report += f"- **{grade}**: {count} 个客户 ({pct}%)\n"
              report += "\n"

              # 生成饼图
              pie_config = {
                  "title": {"text": "客户档位分布", "left": "center"},
                  "tooltip": {"trigger": "item", "formatter": "{a} <br/>{b}: {c} ({d}%)"},
                  "legend": {"orient": "vertical", "left": "left", "data": grade_counts.index.tolist()},
                  "series": [{
                      "name": "客户档位",
                      "type": "pie",
                      "radius": ["50%", "70%"],
                      "data": [{"value": int(count), "name": grade} for grade, count in grade_counts.items()]
                  }]
              }

              pie_output = "```echarts\n" + json.dumps(pie_config, indent=2, ensure_ascii=False) + "\n```"
              report += f"## 📈 档位分布饼图\n\n{pie_output}\n\n"

              # 档位库存分析
              if all(col in df.columns for col in ['上期库存（条）', '本期库存（条）']):
                  inventory_by_grade = df.groupby('客户档位')[['上期库存（条）', '本期库存（条）']].sum().round(2)

                  report += "## 📦 各档位库存统计\n\n"
                  for grade in inventory_by_grade.index:
                      prev_stock = inventory_by_grade.loc[grade, '上期库存（条）']
                      curr_stock = inventory_by_grade.loc[grade, '本期库存（条）']
                      change = curr_stock - prev_stock
                      report += f"- **{grade}**: 上期 {prev_stock} 条 → 本期 {curr_stock} 条 (变化: {change:+.2f} 条)\n"
                  report += "\n"

              return report


          def regional_analysis(df, metadata):
              """地区分布分析"""
              report = "# 🗺️ 地区分布分析报告\n\n"

              if '地市' not in df.columns:
                  return report + "❌ 数据中未找到地市字段\n"

              # 地市分布
              city_counts = df['地市'].value_counts()
              report += "## 🏙️ 地市客户分布\n\n"
              for city, count in city_counts.head(10).items():
                  percentage = (count / len(df)) * 100
                  report += f"- **{city}**: {count} 个客户 ({percentage:.1f}%)\n"
              report += "\n"

              # 区县分布
              if '区县' in df.columns:
                  county_counts = df['区县'].value_counts()
                  report += "## 🏘️ 区县客户分布(前10)\n\n"
                  for county, count in county_counts.head(10).items():
                      percentage = (count / len(df)) * 100
                      report += f"- **{county}**: {count} 个客户 ({percentage:.1f}%)\n"
                  report += "\n"

              # 生成地市柱状图
              bar_config = {
                  "title": {"text": "各地市客户数量统计", "left": "center"},
                  "tooltip": {"trigger": "axis"},
                  "xAxis": {
                      "type": "category",
                      "data": city_counts.head(10).index.tolist(),
                      "axisLabel": {"interval": 0, "rotate": 45}
                  },
                  "yAxis": {"type": "value", "name": "客户数量"},
                  "series": [{
                      "name": "客户数量",
                      "type": "bar",
                      "data": city_counts.head(10).values.tolist(),
                      "itemStyle": {"color": "#3f51b5"}
                  }]
              }

              bar_output = "```echarts\n" + json.dumps(bar_config, indent=2, ensure_ascii=False) + "\n```"
              report += f"## 📊 地市分布柱状图\n\n{bar_output}\n\n"

              return report


          def inventory_analysis(df, metadata):
              """库存趋势分析"""
              report = "# 📦 库存趋势分析报告\n\n"

              required_cols = ['上期库存（条）', '期间购进（条）', '本期库存（条）']
              if not all(col in df.columns for col in required_cols):
                  return report + "❌ 数据中缺少必要的库存字段\n"

              # 总体库存统计
              total_prev = df['上期库存（条）'].sum()
              total_purchase = df['期间购进（条）'].sum()
              total_curr = df['本期库存（条）'].sum()
              total_change = total_curr - total_prev

              report += "## 📊 总体库存概况\n\n"
              report += f"- **上期总库存**: {total_prev:.2f} 条\n"
              report += f"- **期间总购进**: {total_purchase:.2f} 条\n"
              report += f"- **本期总库存**: {total_curr:.2f} 条\n"
              report += f"- **库存变化**: {total_change:+.2f} 条\n"
              report += f"- **库存周转率**: {(total_purchase / total_prev * 100):.2f}%\n\n"

              # 按客户档位的库存分析
              if '客户档位' in df.columns:
                  inventory_by_grade = df.groupby('客户档位')[required_cols].agg(['sum', 'mean']).round(2)

                  report += "## 🏷️ 各档位库存详情\n\n"
                  for grade in inventory_by_grade.index:
                      prev_sum = inventory_by_grade.loc[grade, ('上期库存（条）', 'sum')]
                      curr_sum = inventory_by_grade.loc[grade, ('本期库存（条）', 'sum')]
                      change = curr_sum - prev_sum
                      report += f"### {grade}\n"
                      report += f"- 总库存变化: {prev_sum:.2f} → {curr_sum:.2f} ({change:+.2f})\n"
                      report += f"- 平均库存: {inventory_by_grade.loc[grade, ('本期库存（条）', 'mean')]:.2f} 条/客户\n\n"

              return report


          def time_efficiency_analysis(df, metadata):
              """时间效率分析"""
              report = "# ⏱️ 时间效率分析报告\n\n"

              if '采集时长（分钟）' not in df.columns:
                  return report + "❌ 数据中未找到采集时长字段\n"

              # 采集时长统计
              time_stats = df['采集时长（分钟）'].describe()
              report += "## 📊 采集时长统计\n\n"
              report += f"- **平均时长**: {time_stats['mean']:.2f} 分钟\n"
              report += f"- **中位数**: {time_stats['50%']:.2f} 分钟\n"
              report += f"- **最短时长**: {time_stats['min']:.2f} 分钟\n"
              report += f"- **最长时长**: {time_stats['max']:.2f} 分钟\n"
              report += f"- **标准差**: {time_stats['std']:.2f} 分钟\n\n"

              # 按采集方式分析
              if '采集方式' in df.columns:
                  method_efficiency = df.groupby('采集方式')['采集时长（分钟）'].agg(['mean', 'count']).round(2)

                  report += "## 🔧 各采集方式效率对比\n\n"
                  for method in method_efficiency.index:
                      avg_time = method_efficiency.loc[method, 'mean']
                      count = method_efficiency.loc[method, 'count']
                      report += f"- **{method}**: 平均 {avg_time:.2f} 分钟 ({count} 次采集)\n"
                  report += "\n"

              return report


          def comprehensive_analysis(df, metadata):
              """综合分析报告"""
              report = "# 📋 Excel数据综合分析报告\n\n"

              # 基础信息
              report += "## 📊 数据概览\n\n"
              report += f"- **数据来源**: {metadata.get('文件名', '未知')}\n"
              report += f"- **记录总数**: {len(df)}\n"
              report += f"- **字段数量**: {len(df.columns)}\n"
              report += f"- **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

              # 客户档位分析
              if '客户档位' in df.columns:
                  grade_counts = df['客户档位'].value_counts()
                  report += "## 🏷️ 客户档位分布\n\n"
                  for grade, count in grade_counts.head(5).items():
                      pct = (count / len(df)) * 100
                      report += f"- **{grade}**: {count} 个客户 ({pct:.1f}%)\n"
                  report += "\n"

                  # 档位饼图
                  pie_config = {
                      "title": {"text": "客户档位分布", "left": "center"},
                      "tooltip": {"trigger": "item"},
                      "series": [{
                          "name": "客户档位",
                          "type": "pie",
                          "radius": ["50%", "70%"],
                          "data": [{"value": int(count), "name": grade} for grade, count in grade_counts.items()]
                      }]
                  }
                  pie_output = "```echarts\n" + json.dumps(pie_config, indent=2, ensure_ascii=False) + "\n```"
                  report += f"## 📈 档位分布图\n\n{pie_output}\n\n"

              # 地区分析
              if '地市' in df.columns:
                  city_counts = df['地市'].value_counts()
                  report += "## 🗺️ 地区分布(前5)\n\n"
                  for city, count in city_counts.head(5).items():
                      pct = (count / len(df)) * 100
                      report += f"- **{city}**: {count} 个客户 ({pct:.1f}%)\n"
                  report += "\n"

              # 库存概况
              if all(col in df.columns for col in ['上期库存（条）', '本期库存（条）']):
                  total_prev = df['上期库存（条）'].sum()
                  total_curr = df['本期库存（条）'].sum()
                  change = total_curr - total_prev

                  report += "## 📦 库存概况\n\n"
                  report += f"- **上期总库存**: {total_prev:.2f} 条\n"
                  report += f"- **本期总库存**: {total_curr:.2f} 条\n"
                  report += f"- **库存变化**: {change:+.2f} 条\n\n"

              # 数据质量
              missing_data = df.isnull().sum()
              if missing_data.sum() > 0:
                  report += "## ⚠️ 数据质量提醒\n\n"
                  for col, missing_count in missing_data.items():
                      if missing_count > 0:
                          pct = (missing_count / len(df)) * 100
                          report += f"- **{col}**: {missing_count} 个缺失值 ({pct:.1f}%)\n"
                  report += "\n"
              else:
                  report += "## ✅ 数据质量\n\n数据完整，无缺失值\n\n"

              report += "---\n\n*本报告由Pandas数据分析引擎生成*\n"

              return report
        code_language: python3
        desc: '使用pandas进行深度数据分析，支持多种分析类型'
        outputs:
          result:
            type: string
        selected: false
        title: Pandas数据分析器
        type: code
        variables:
        - value_selector:
          - 'data_processor'
          - text
          variable: json_str
        - value_selector:
          - 'start'
          - analysis_type
          variable: analysis_type
      height: 97
      id: 'pandas_analyzer'
      position:
        x: 942
        y: 283.5
      positionAbsolute:
        x: 942
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '输出最终的分析结果'
        outputs:
        - value_selector:
          - 'pandas_analyzer'
          - result
          variable: analysis_result
        selected: false
        title: 结束
        type: end
      height: 89
      id: 'end'
      position:
        x: 1246
        y: 283.5
      positionAbsolute:
        x: 1246
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
