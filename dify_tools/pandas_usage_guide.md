# Dify中使用Pandas处理文档解析器数据完整指南

## 概述

本指南详细介绍如何在Dify工作流中使用Python的pandas库来处理文档解析器返回的Excel数据，实现更强大的数据分析功能。

## 工作流程架构

```
文件上传 → 文档提取器 → LLM数据处理器 → Pandas代码节点 → 结果输出
```

### 1. 文档提取器阶段
- **输入**: Excel文件 (.xlsx, .xls, .csv)
- **输出**: 原始文本数据
- **功能**: 提取Excel文件中的表格内容

### 2. LLM数据处理器阶段
- **输入**: 文档提取器的文本输出
- **输出**: 结构化JSON数据
- **功能**: 将原始文本转换为标准JSON格式

### 3. Pandas代码节点阶段
- **输入**: JSON格式的数据
- **输出**: 完整的数据分析报告
- **功能**: 使用pandas进行深度数据分析

## 核心代码实现

### 数据转换流程

```python
import pandas as pd
import json

def main(json_str: str) -> dict:
    # 1. 解析JSON数据
    data = json.loads(json_str)
    customer_data = data.get("数据", [])
    
    # 2. 转换为DataFrame
    df = pd.DataFrame(customer_data)
    
    # 3. 数据清洗
    df = clean_and_convert_data(df)
    
    # 4. 数据分析
    analysis = perform_analysis(df)
    
    return {"result": generate_report(analysis)}
```

### 数据清洗和类型转换

```python
def clean_and_convert_data(df):
    # 数值字段转换
    numeric_columns = [
        '上期库存（条）', '期间购进（条）', '本期库存（条）',
        '采集时长（分钟）', '签到偏差距离', '签退偏差距离'
    ]
    
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 日期字段转换
    date_columns = ['采集日期', '订单日期']
    for col in date_columns:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')
    
    return df
```

## 主要分析功能

### 1. 基础统计分析

```python
# 记录总数
total_records = len(df)

# 唯一值统计
unique_customers = df['客户编码'].nunique()
unique_cities = df['地市'].nunique()

# 描述性统计
df.describe()
```

### 2. 分组聚合分析

```python
# 按客户档位分组
grade_analysis = df.groupby('客户档位').agg({
    '客户编码': 'count',
    '上期库存（条）': ['sum', 'mean'],
    '本期库存（条）': ['sum', 'mean']
})

# 按地市分组
city_analysis = df.groupby('地市').size()
```

### 3. 时间序列分析

```python
# 按日期分组
if '采集日期' in df.columns:
    daily_stats = df.groupby('采集日期').agg({
        '客户编码': 'count',
        '采集时长（分钟）': 'mean'
    })
```

### 4. 数据质量检查

```python
# 缺失值检查
missing_data = df.isnull().sum()

# 重复值检查
duplicates = df.duplicated().sum()

# 异常值检查
outliers = df.select_dtypes(include=[np.number]).apply(
    lambda x: len(x[(x < x.quantile(0.25) - 1.5 * (x.quantile(0.75) - x.quantile(0.25))) | 
                    (x > x.quantile(0.75) + 1.5 * (x.quantile(0.75) - x.quantile(0.25)))])
)
```

## 可视化图表生成

### 1. 饼图配置

```python
def create_pie_chart(df, column):
    value_counts = df[column].value_counts()
    return {
        "title": {"text": f"{column}分布", "left": "center"},
        "tooltip": {"trigger": "item"},
        "series": [{
            "name": column,
            "type": "pie",
            "radius": ["50%", "70%"],
            "data": [{"value": int(count), "name": name} 
                    for name, count in value_counts.items()]
        }]
    }
```

### 2. 柱状图配置

```python
def create_bar_chart(df, x_column, y_column):
    grouped_data = df.groupby(x_column)[y_column].sum()
    return {
        "title": {"text": f"{x_column}vs{y_column}", "left": "center"},
        "xAxis": {"type": "category", "data": grouped_data.index.tolist()},
        "yAxis": {"type": "value"},
        "series": [{
            "type": "bar",
            "data": grouped_data.values.tolist()
        }]
    }
```

## 实际应用示例

### 客户档位分析

```python
# 档位分布统计
grade_distribution = df['客户档位'].value_counts()
grade_percentages = (grade_distribution / len(df) * 100).round(2)

# 各档位库存分析
inventory_by_grade = df.groupby('客户档位').agg({
    '上期库存（条）': ['sum', 'mean'],
    '本期库存（条）': ['sum', 'mean'],
    '期间购进（条）': ['sum', 'mean']
})
```

### 地区分析

```python
# 地市客户分布
city_customers = df.groupby('地市').size().sort_values(ascending=False)

# 地区库存分析
city_inventory = df.groupby(['地市', '区县']).agg({
    '上期库存（条）': 'sum',
    '本期库存（条）': 'sum'
})
```

### 时间效率分析

```python
# 采集时长分析
collection_time_stats = df['采集时长（分钟）'].describe()

# 按采集方式分组的效率分析
efficiency_by_method = df.groupby('采集方式')['采集时长（分钟）'].agg(['mean', 'std'])
```

## 错误处理和数据验证

### 1. JSON解析错误处理

```python
try:
    data = json.loads(json_str)
except json.JSONDecodeError as e:
    return {"result": f"JSON解析错误: {str(e)}"}
```

### 2. 数据类型转换错误处理

```python
def safe_numeric_conversion(df, columns):
    for col in columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            # 记录转换失败的行数
            failed_conversions = df[col].isna().sum()
            if failed_conversions > 0:
                print(f"警告: {col} 列有 {failed_conversions} 个值无法转换为数字")
    return df
```

### 3. 缺失数据处理策略

```python
def handle_missing_data(df):
    # 字符串字段填充默认值
    string_columns = df.select_dtypes(include=['object']).columns
    for col in string_columns:
        df[col] = df[col].fillna('未知')
    
    # 数值字段填充0或均值
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        df[col] = df[col].fillna(0)  # 或使用 df[col].mean()
    
    return df
```

## 性能优化建议

### 1. 大数据集处理

```python
# 分块处理大数据集
def process_large_dataset(df, chunk_size=1000):
    results = []
    for chunk in pd.read_json(json_str, chunksize=chunk_size):
        chunk_result = process_chunk(chunk)
        results.append(chunk_result)
    return combine_results(results)
```

### 2. 内存优化

```python
# 优化数据类型以节省内存
def optimize_dtypes(df):
    for col in df.columns:
        if df[col].dtype == 'object':
            try:
                df[col] = pd.to_numeric(df[col], downcast='integer')
            except:
                pass
    return df
```

## 部署配置

### 1. 依赖包配置

在Dify代码节点中，pandas通常已经预装。如果需要额外的包，可以在代码开头导入：

```python
import pandas as pd
import numpy as np
import json
import re
from datetime import datetime
```

### 2. 内存和超时设置

- 建议设置适当的超时时间（如60秒）
- 对于大数据集，考虑分批处理
- 监控内存使用情况

## 最佳实践

1. **数据验证**: 始终验证输入数据的格式和完整性
2. **错误处理**: 实现全面的异常处理机制
3. **性能监控**: 记录处理时间和内存使用
4. **结果格式化**: 确保输出格式符合Dify的要求
5. **文档记录**: 详细记录数据处理逻辑和业务规则

通过以上方法，您可以在Dify中充分利用pandas的强大功能来处理和分析Excel数据，实现更深入的数据洞察和可视化展示。
