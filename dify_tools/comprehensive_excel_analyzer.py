import pandas as pd
import numpy as np
import json
import re
from collections import Counter
file_path = '{{file_path}}'

def generate_basic_info(df):
    """生成数据基本信息"""
    info = f"- 行数: {df.shape[0]}\n"
    info += f"- 列数: {df.shape[1]}\n"
    info += f"- 数据点总数: {df.size}\n"
    info += f"- 内存使用: {df.memory_usage().sum() / 1024:.2f} KB\n"
    return info

def generate_data_preview(df):
    """生成数据预览"""
    preview = "### 数据前5行\n\n"
    
    # 创建表头
    headers = "|" + "|".join(str(col) for col in df.columns) + "|"
    separator = "|" + "|".join(["---" for _ in df.columns]) + "|"
    
    preview += headers + "\n" + separator + "\n"
    
    # 添加数据行
    for _, row in df.head().iterrows():
        preview += "|" + "|".join(str(val) for val in row.values) + "|\n"
    
    return preview



def analyze_numeric_columns(df):
    """分析数值型列"""
    # 排除客户编码列
    numeric_cols = df.select_dtypes(include=['number']).columns
    numeric_cols = [col for col in numeric_cols if col != '客户编码']
    
    if len(numeric_cols) == 0:
        return "### 数值型数据分析\n\n无数值型数据列"
    
    # 手动创建描述性统计表格
    stats_md = "### 数值型数据统计\n\n"
    
    # 创建表头
    stats_md += "| 列名 | 计数 | 均值 | 标准差 | 最小值 | 25% | 50% | 75% | 最大值 |\n"
    stats_md += "|------|------|------|--------|--------|-----|-----|-----|--------|\n"
    
    # 添加每列的统计数据
    for col in numeric_cols:
        desc = df[col].describe()
        stats_md += f"| {col} | {desc['count']:.0f} | {desc['mean']:.2f} | {desc['std']:.2f} | {desc['min']:.2f} | {desc['25%']:.2f} | {desc['50%']:.2f} | {desc['75%']:.2f} | {desc['max']:.2f} |\n"
    
    return stats_md

def analyze_categorical_columns(df):
    """分析分类数据列"""
    # 排除客户编码列
    cat_cols = df.select_dtypes(include=['object', 'category']).columns
    cat_cols = [col for col in cat_cols if col != '客户编码']
    
    if len(cat_cols) == 0:
        return "### 分类数据分析\n\n无分类数据列"
    
    cat_analysis = "### 分类数据分析\n\n"
    
    for col in cat_cols[:5]:  # 限制分析前5个分类列，避免报告过长
        unique_values = df[col].nunique()
        cat_analysis += f"#### {col}\n"
        cat_analysis += f"- 唯一值数量: {unique_values}\n"
        
        if unique_values <= 10:  # 只显示值数量较少的分类
            value_counts = df[col].value_counts().head(10)
            cat_analysis += "- 值分布:\n"
            for val, count in value_counts.items():
                cat_analysis += f"  - {val}: {count} ({count/len(df)*100:.1f}%)\n"
        else:
            cat_analysis += f"- 前5个最常见值:\n"
            for val, count in df[col].value_counts().head(5).items():
                cat_analysis += f"  - {val}: {count} ({count/len(df)*100:.1f}%)\n"
        
        cat_analysis += "\n"
    
    return cat_analysis

def analyze_data_quality(df):
    """分析数据质量"""
    quality = "### 数据质量分析\n\n"
    
    # 缺失值分析
    missing = df.isnull().sum()
    missing_cols = missing[missing > 0]
    
    if len(missing_cols) > 0:
        quality += "#### 缺失值分析\n\n"
        for col, count in missing_cols.items():
            quality += f"- {col}: {count}个缺失值 ({count/len(df)*100:.1f}%)\n"
    else:
        quality += "#### 缺失值分析\n\n- 无缺失值\n"
    
    # 重复行分析
    duplicates = df.duplicated().sum()
    quality += f"\n#### 重复行分析\n\n- 重复行数: {duplicates} ({duplicates/len(df)*100:.1f}%)\n"
    
    return quality

def analyze_customer_grades(df):
    """分析客户档位分布"""
    if '客户档位' not in df.columns:
        return "### 客户档位分析\n\n数据中未找到'客户档位'列"
    
    grade_analysis = "### 客户档位分析\n\n"
    
    # 档位分布统计
    grade_counts = df['客户档位'].value_counts()
    grade_percentages = (grade_counts / len(df) * 100).round(2)
    
    grade_analysis += "#### 档位分布统计\n\n"
    grade_analysis += "| 客户档位 | 数量 | 百分比 |\n"
    grade_analysis += "|----------|------|--------|\n"
    
    for grade, count in grade_counts.items():
        pct = grade_percentages[grade]
        grade_analysis += f"| {grade} | {count} | {pct}% |\n"
    
    # 按档位分组统计其他数值列
    numeric_cols = df.select_dtypes(include=['number']).columns
    # 排除客户编码列
    numeric_cols = [col for col in numeric_cols if col != '客户编码']
    
    if len(numeric_cols) > 0:
        grade_analysis += "\n#### 各档位数值统计\n\n"
        
        for col in numeric_cols[:3]:  # 限制分析前3个数值列
            grade_analysis += f"\n##### {col} 按客户档位统计\n\n"
            grade_analysis += "| 客户档位 | 数量 | 平均值 | 总和 | 最小值 | 最大值 |\n"
            grade_analysis += "|----------|------|--------|------|--------|--------|\n"
            
            grouped = df.groupby('客户档位')[col].agg(['count', 'mean', 'sum', 'min', 'max'])
            for grade in grouped.index:
                stats = grouped.loc[grade]
                grade_analysis += f"| {grade} | {stats['count']} | {stats['mean']:.2f} | {stats['sum']:.2f} | {stats['min']:.2f} | {stats['max']:.2f} |\n"
    
    # 如果存在库存相关列，进行特殊分析
    inventory_cols = [col for col in df.columns if ('库存' in col or '购进' in col) and col != '客户编码']
    if inventory_cols:
        grade_analysis += "\n#### 各档位库存分析\n\n"
        
        for col in inventory_cols:
            grade_analysis += f"\n##### {col} 按客户档位统计\n\n"
            grade_analysis += "| 客户档位 | 平均值 | 总和 |\n"
            grade_analysis += "|----------|--------|------|\n"
            
            grouped = df.groupby('客户档位')[col].agg(['mean', 'sum'])
            for grade in grouped.index:
                stats = grouped.loc[grade]
                grade_analysis += f"| {grade} | {stats['mean']:.2f} | {stats['sum']:.2f} |\n"
    
    return grade_analysis

def main(file_path):
    """
    全面解析Excel文件并生成详细分析报告
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        dict: 包含分析结果的字典
    """
    try:
        # 1. 读取Excel文件 - 自动检测文件类型
        if file_path.endswith('.csv'):
            # 尝试不同编码读取CSV
            try:
                df = pd.read_csv(file_path)
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(file_path, encoding='gbk')
                except UnicodeDecodeError:
                    try:
                        df = pd.read_csv(file_path, encoding='gb18030')
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='ISO-8859-1')
        else:
            # 读取Excel文件
            try:
                # 尝试读取所有sheet
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                
                # 如果有多个sheet，读取第一个sheet
                df = excel_file.parse(sheet_names[0])
                
                # 保存sheet信息
                sheets_info = f"- 工作表数量: {len(sheet_names)}\n"
                sheets_info += f"- 工作表名称: {', '.join(sheet_names)}\n"
                sheets_info += f"- 当前分析工作表: {sheet_names[0]}\n"
            except Exception as e:
                return {"result": f"Excel读取错误: {str(e)}"}
        
        # 2. 数据清洗和预处理
        # 删除全为空的行和列
        df = df.dropna(how='all')
        df = df.dropna(axis=1, how='all')
        
        # 3. 数据基本信息
        basic_info = generate_basic_info(df)
        
        # 4. 数据预览
        preview = generate_data_preview(df)
        
        # 5. 数值型数据统计分析
        numeric_analysis = analyze_numeric_columns(df)
        
        # 6. 分类数据分析
        categorical_analysis = analyze_categorical_columns(df)
        
        # 7. 数据质量分析
        quality_analysis = analyze_data_quality(df)
        
        # 8. 客户档位分析
        customer_grade_analysis = analyze_customer_grades(df)
        
        # 9. 组合所有分析结果
        report = "# Excel数据全面分析报告\n\n"
        report += f"## 📊 基本信息\n\n"
        report += basic_info + "\n\n"
        report += f"## 📋 数据预览\n\n"
        report += preview + "\n\n"
        report += f"## 📈 数值型数据分析\n\n"
        report += numeric_analysis + "\n\n"
        report += f"## 📊 分类数据分析\n\n"
        report += categorical_analysis + "\n\n"
        report += f"## ⚠️ 数据质量分析\n\n"
        report += quality_analysis + "\n\n"
        report += f"## 🏷️ 客户档位分析\n\n"
        report += customer_grade_analysis + "\n\n"
        
        return {"result": report}
        
    except Exception as e:
        return {"result": f"错误: {str(e)}"}