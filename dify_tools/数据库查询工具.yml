app:
  description: 数据库查询工具
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 数据库查询工具
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: junjiem/db_query:0.0.9@1060683d13870d0cb77d47664e5100a16a2029dada24cf1193bc2ce312295764
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: tool
      id: 1742996495464-source-1748570581345-target
      source: '1742996495464'
      sourceHandle: source
      target: '1748570581345'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: end
      id: 1748570581345-source-1748570634054-target
      source: '1748570581345'
      sourceHandle: source
      target: '1748570634054'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: SQL语句
          max_length: 2000
          options: []
          required: true
          type: paragraph
          variable: sql
      height: 90
      id: '1742996495464'
      position:
        x: 170.7575707933832
        y: 282
      positionAbsolute:
        x: 170.7575707933832
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: mysql
          form: llm
          human_description:
            en_US: Used for selecting the database type, mysql, oracle, oracle11g,
              postgresql or mssql.
            ja_JP: Used for selecting the database type, mysql, oracle, oracle11g,
              postgresql or mssql.
            pt_BR: Used for selecting the database type, mysql, oracle, oracle11g,
              postgresql or mssql.
            zh_Hans: 用于选择数据库类型，mysql、oracle、oracle11g、postgresql或mssql。
          label:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          llm_description: ''
          max: null
          min: null
          name: db_type
          options:
          - label:
              en_US: MySQL
              ja_JP: MySQL
              pt_BR: MySQL
              zh_Hans: MySQL
            value: mysql
          - label:
              en_US: Oracle
              ja_JP: Oracle
              pt_BR: Oracle
              zh_Hans: Oracle
            value: oracle
          - label:
              en_US: Oracle11g
              ja_JP: Oracle11g
              pt_BR: Oracle11g
              zh_Hans: Oracle11g
            value: oracle11g
          - label:
              en_US: PostgreSQL
              ja_JP: PostgreSQL
              pt_BR: PostgreSQL
              zh_Hans: PostgreSQL
            value: postgresql
          - label:
              en_US: Microsoft SQL Server
              ja_JP: Microsoft SQL Server
              pt_BR: Microsoft SQL Server
              zh_Hans: Microsoft SQL Server
            value: mssql
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: localhost
          form: llm
          human_description:
            en_US: Database hostname or IP address (Original string, not URL-encoded
              string).
            ja_JP: Database hostname or IP address (Original string, not URL-encoded
              string).
            pt_BR: Database hostname or IP address (Original string, not URL-encoded
              string).
            zh_Hans: 数据库的主机名或IP地址（原始字符串，非URL编码字符串）。
          label:
            en_US: Database Host
            ja_JP: Database Host
            pt_BR: Database Host
            zh_Hans: 数据库地址
          llm_description: ''
          max: null
          min: null
          name: db_host
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Database port.
            ja_JP: Database port.
            pt_BR: Database port.
            zh_Hans: 数据库的端口。
          label:
            en_US: Port
            ja_JP: Port
            pt_BR: Port
            zh_Hans: 端口
          llm_description: ''
          max: null
          min: null
          name: db_port
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Database username (Original string, not URL-encoded string).
            ja_JP: Database username (Original string, not URL-encoded string).
            pt_BR: Database username (Original string, not URL-encoded string).
            zh_Hans: 数据库的用户名（原始字符串，非URL编码字符串）。
          label:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          llm_description: ''
          max: null
          min: null
          name: db_username
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Database password (Original string, not URL-encoded string).
            ja_JP: Database password (Original string, not URL-encoded string).
            pt_BR: Database password (Original string, not URL-encoded string).
            zh_Hans: 数据库的密码（原始字符串，非URL编码字符串）。
          label:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          llm_description: ''
          max: null
          min: null
          name: db_password
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: secret-input
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Database name (Original string, not URL-encoded string).
            ja_JP: Database name (Original string, not URL-encoded string).
            pt_BR: Database name (Original string, not URL-encoded string).
            zh_Hans: 数据库的名称（原始字符串，非URL编码字符串）。
          label:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 库名
          llm_description: ''
          max: null
          min: null
          name: db_name
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 'Database properties, for example: alt_host=host1&alt_host=host2&ssl_cipher=%2Fpath%2Fto%2Fcrt'
            ja_JP: 'Database properties, for example: alt_host=host1&alt_host=host2&ssl_cipher=%2Fpath%2Fto%2Fcrt'
            pt_BR: 'Database properties, for example: alt_host=host1&alt_host=host2&ssl_cipher=%2Fpath%2Fto%2Fcrt'
            zh_Hans: 数据库属性，例如：alt_host=host1&alt_host=host2&ssl_cipher=%2Fpath%2Fto%2Fcrt
          label:
            en_US: Database properties
            ja_JP: Database properties
            pt_BR: Database properties
            zh_Hans: 数据库属性
          llm_description: ''
          max: null
          min: null
          name: db_properties
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 'SQL query statement, for example: select * from tbl_name'
            ja_JP: 'SQL query statement, for example: select * from tbl_name'
            pt_BR: 'SQL query statement, for example: select * from tbl_name'
            zh_Hans: SQL查询语句，例如：select * from tbl_name
          label:
            en_US: Query SQL
            ja_JP: Query SQL
            pt_BR: Query SQL
            zh_Hans: SQL查询语句
          llm_description: 'SQL query statement, for example: select * from tbl_name'
          max: null
          min: null
          name: query_sql
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: markdown
          form: form
          human_description:
            en_US: Used for selecting the output format, markdown or json.
            ja_JP: Used for selecting the output format, markdown or json.
            pt_BR: Used for selecting the output format, markdown or json.
            zh_Hans: 用于选择输出格式，markdown或json。
          label:
            en_US: Output format
            ja_JP: Output format
            pt_BR: Output format
            zh_Hans: 输出格式
          llm_description: ''
          max: null
          min: null
          name: output_format
          options:
          - label:
              en_US: MARKDOWN
              ja_JP: MARKDOWN
              pt_BR: MARKDOWN
              zh_Hans: MARKDOWN
            value: markdown
          - label:
              en_US: JSON
              ja_JP: JSON
              pt_BR: JSON
              zh_Hans: JSON
            value: json
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: select
        params:
          db_host: ''
          db_name: ''
          db_password: ''
          db_port: ''
          db_properties: ''
          db_type: ''
          db_username: ''
          output_format: ''
          query_sql: ''
        provider_id: junjiem/db_query/db_query
        provider_name: junjiem/db_query/db_query
        provider_type: builtin
        selected: false
        title: SQL查询
        tool_configurations:
          output_format: markdown
        tool_description: 数据库SQL查询工具。
        tool_label: SQL查询
        tool_name: sql_query
        tool_parameters:
          db_host:
            type: mixed
            value: host.docker.internal
          db_name:
            type: mixed
            value: dify_test
          db_password:
            type: mixed
            value: '123456'
          db_port:
            type: constant
            value: 3306
          db_type:
            type: constant
            value: mysql
          db_username:
            type: mixed
            value: root
          query_sql:
            type: mixed
            value: '{{#1742996495464.sql#}}'
        type: tool
      height: 90
      id: '1748570581345'
      position:
        x: 474.7575707933832
        y: 282
      positionAbsolute:
        x: 474.7575707933832
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1748570581345'
          - text
          variable: result
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1748570634054'
      position:
        x: 778.7575707933831
        y: 282
      positionAbsolute:
        x: 778.7575707933831
        y: 282
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -91.5001118423595
      y: -9.776347907329864
      zoom: 1.1755053996769085
