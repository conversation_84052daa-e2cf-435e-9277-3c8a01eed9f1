"""
Dify代码节点中使用pandas的简化示例
适用于直接在Dify工作流的代码节点中使用
"""

import json
import re
import pandas as pd
import numpy as np
from datetime import datetime

def main(json_str: str) -> dict:
    """
    简化的pandas数据分析示例
    
    Args:
        json_str: 从文档解析器或LLM处理器获取的JSON字符串
        
    Returns:
        dict: 包含分析结果的字典
    """
    try:
        # 步骤1: 解析JSON数据
        # 清理可能的非JSON内容
        json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
        if not json_match:
            return {"result": "❌ 错误: 无效的JSON格式"}
        
        # 解析JSON
        data = json.loads(json_match.group(0))
        customer_data = data.get("数据", [])
        
        if not customer_data:
            return {"result": "❌ 错误: 未找到有效数据"}
        
        # 步骤2: 转换为pandas DataFrame
        df = pd.DataFrame(customer_data)
        
        # 步骤3: 数据清洗
        # 转换数值字段
        numeric_cols = ['上期库存（条）', '期间购进（条）', '本期库存（条）', '采集时长（分钟）']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 转换日期字段
        date_cols = ['采集日期', '订单日期']
        for col in date_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # 填充缺失值
        df = df.fillna({
            '客户档位': '未知',
            '地市': '未知',
            '状态': '未知'
        })
        
        # 步骤4: 数据分析
        analysis_result = perform_analysis(df)
        
        return {"result": analysis_result}
        
    except Exception as e:
        return {"result": f"❌ 处理错误: {str(e)}"}


def perform_analysis(df):
    """执行数据分析并生成报告"""
    
    report = "# 📊 Excel数据分析报告\n\n"
    
    # 基础统计
    report += "## 📋 基础信息\n\n"
    report += f"- **总记录数**: {len(df)}\n"
    report += f"- **字段数量**: {len(df.columns)}\n"
    report += f"- **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    # 客户档位分析
    if '客户档位' in df.columns:
        grade_analysis = analyze_customer_grades(df)
        report += grade_analysis
    
    # 地区分析
    if '地市' in df.columns:
        region_analysis = analyze_regions(df)
        report += region_analysis
    
    # 库存分析
    if all(col in df.columns for col in ['上期库存（条）', '本期库存（条）']):
        inventory_analysis = analyze_inventory(df)
        report += inventory_analysis
    
    # 数据质量检查
    quality_check = check_data_quality(df)
    report += quality_check
    
    return report


def analyze_customer_grades(df):
    """分析客户档位分布"""
    report = "## 🏷️ 客户档位分析\n\n"
    
    # 档位分布统计
    grade_counts = df['客户档位'].value_counts()
    grade_percentages = (grade_counts / len(df) * 100).round(2)
    
    report += "### 档位分布:\n"
    for grade, count in grade_counts.items():
        pct = grade_percentages[grade]
        report += f"- **{grade}**: {count} 个客户 ({pct}%)\n"
    report += "\n"
    
    # 生成ECharts饼图
    pie_data = [{"value": int(count), "name": grade} for grade, count in grade_counts.items()]
    pie_config = {
        "title": {"text": "客户档位分布", "left": "center"},
        "tooltip": {"trigger": "item", "formatter": "{a} <br/>{b}: {c} ({d}%)"},
        "legend": {"orient": "vertical", "left": "left"},
        "series": [{
            "name": "客户档位",
            "type": "pie",
            "radius": ["50%", "70%"],
            "data": pie_data,
            "emphasis": {"itemStyle": {"shadowBlur": 10, "shadowOffsetX": 0, "shadowColor": "rgba(0, 0, 0, 0.5)"}}
        }]
    }
    
    pie_json = json.dumps(pie_config, indent=2, ensure_ascii=False)
    report += f"### 档位分布图:\n```echarts\n{pie_json}\n```\n\n"
    
    return report


def analyze_regions(df):
    """分析地区分布"""
    report = "## 🗺️ 地区分析\n\n"
    
    # 地市分布
    city_counts = df['地市'].value_counts()
    report += "### 地市分布(前10):\n"
    for city, count in city_counts.head(10).items():
        pct = (count / len(df)) * 100
        report += f"- **{city}**: {count} 个客户 ({pct:.1f}%)\n"
    report += "\n"
    
    # 生成柱状图
    top_cities = city_counts.head(8)  # 取前8个城市避免图表过于拥挤
    bar_config = {
        "title": {"text": "各地市客户数量", "left": "center"},
        "tooltip": {"trigger": "axis"},
        "xAxis": {
            "type": "category",
            "data": top_cities.index.tolist(),
            "axisLabel": {"interval": 0, "rotate": 45}
        },
        "yAxis": {"type": "value", "name": "客户数量"},
        "series": [{
            "name": "客户数量",
            "type": "bar",
            "data": top_cities.values.tolist(),
            "itemStyle": {"color": "#3f51b5"},
            "label": {"show": True, "position": "top"}
        }]
    }
    
    bar_json = json.dumps(bar_config, indent=2, ensure_ascii=False)
    report += f"### 地市分布图:\n```echarts\n{bar_json}\n```\n\n"
    
    return report


def analyze_inventory(df):
    """分析库存情况"""
    report = "## 📦 库存分析\n\n"
    
    # 总体库存统计
    total_prev = df['上期库存（条）'].sum()
    total_curr = df['本期库存（条）'].sum()
    total_change = total_curr - total_prev
    
    report += "### 总体库存:\n"
    report += f"- **上期总库存**: {total_prev:.2f} 条\n"
    report += f"- **本期总库存**: {total_curr:.2f} 条\n"
    report += f"- **库存变化**: {total_change:+.2f} 条\n"
    
    if total_prev > 0:
        change_rate = (total_change / total_prev) * 100
        report += f"- **变化率**: {change_rate:+.2f}%\n"
    report += "\n"
    
    # 按档位分析库存
    if '客户档位' in df.columns:
        inventory_by_grade = df.groupby('客户档位')[['上期库存（条）', '本期库存（条）']].sum().round(2)
        
        report += "### 各档位库存:\n"
        for grade in inventory_by_grade.index:
            prev = inventory_by_grade.loc[grade, '上期库存（条）']
            curr = inventory_by_grade.loc[grade, '本期库存（条）']
            change = curr - prev
            report += f"- **{grade}**: {prev:.2f} → {curr:.2f} ({change:+.2f})\n"
        report += "\n"
        
        # 生成库存对比图
        comparison_config = {
            "title": {"text": "各档位库存对比", "left": "center"},
            "tooltip": {"trigger": "axis"},
            "legend": {"data": ["上期库存", "本期库存"]},
            "xAxis": {"type": "category", "data": inventory_by_grade.index.tolist()},
            "yAxis": {"type": "value", "name": "库存数量(条)"},
            "series": [
                {
                    "name": "上期库存",
                    "type": "bar",
                    "data": inventory_by_grade['上期库存（条）'].tolist()
                },
                {
                    "name": "本期库存",
                    "type": "bar",
                    "data": inventory_by_grade['本期库存（条）'].tolist()
                }
            ]
        }
        
        comparison_json = json.dumps(comparison_config, indent=2, ensure_ascii=False)
        report += f"### 库存对比图:\n```echarts\n{comparison_json}\n```\n\n"
    
    return report


def check_data_quality(df):
    """检查数据质量"""
    report = "## 🔍 数据质量检查\n\n"
    
    # 缺失值检查
    missing_data = df.isnull().sum()
    total_missing = missing_data.sum()
    
    if total_missing > 0:
        report += "### ⚠️ 缺失值统计:\n"
        for col, missing_count in missing_data.items():
            if missing_count > 0:
                pct = (missing_count / len(df)) * 100
                report += f"- **{col}**: {missing_count} 个缺失值 ({pct:.1f}%)\n"
        report += "\n"
    else:
        report += "### ✅ 数据完整性: 无缺失值\n\n"
    
    # 重复值检查
    duplicates = df.duplicated().sum()
    if duplicates > 0:
        report += f"### ⚠️ 重复记录: {duplicates} 条\n\n"
    else:
        report += "### ✅ 无重复记录\n\n"
    
    # 数据类型统计
    report += "### 📊 数据类型分布:\n"
    dtype_counts = df.dtypes.value_counts()
    for dtype, count in dtype_counts.items():
        report += f"- **{dtype}**: {count} 个字段\n"
    report += "\n"
    
    return report


# 使用示例:
# 在Dify代码节点中，只需要调用 main(json_str) 函数
# 其中 json_str 是从上一个节点(文档解析器或LLM)传递过来的JSON字符串
