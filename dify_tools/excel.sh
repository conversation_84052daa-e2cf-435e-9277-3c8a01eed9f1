你是一个文档提取专家，请根据用户上传文档内容{文档提取器输出text}提取该表格里面的数据返回json格式数据。
其中内容格式 "操作	地市	区县	客户编码	客户名称	上期库存（条）	期间购进（条）	本期库存（条）	采集日期	采集员	提交时间	状态	数据异常	是否按时填报	采集方式	采集渠道	采集时长（分钟）	采集开始时间	采集结束时间	签到偏差距离	签退偏差距离	订单日期	客户档位	电话" 字段返回信息，返回的结果信息以json格式返回

返回数据格式如下：
{
  "data": [
    {
      "操作": "查看驳回",
      "地市": "商洛市烟草专卖局(公司)",
      "区县": "山阳县烟草专卖局",
      "客户编码": "611024130175",
      "客户名称": "鑫源批发部",
      "上期库存（条）": 310.6,
      "期间购进（条）": 0.0,
      "本期库存（条）": 291.6,
      "采集日期": "2025-05-09",
      "采集员": "洪瑞",
      "提交时间": "2025-05-09 09:41:55",
      "状态": "已采集",
      "数据异常": "是",
      "是否按时填报": "是",
      "采集方式": "手工采集",
      "采集渠道": "现场采集",
      "采集时长（分钟）": 22,
      "采集开始时间": "2025-05-09 09:20:00",
      "采集结束时间": "2025-05-09 09:41:54",
      "签到偏差距离": 19,
      "签退偏差距离": 13,
      "订单日期": "2025-05-09",
      "客户档位": "十四档",
      "电话": "18729698195"
    }
          ...多条数据
  ]
}