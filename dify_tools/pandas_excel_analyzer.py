import pandas as pd
file_path = '{{file_path}}'

print(file_path)
def main(file_path):
    try:
        # 读取CSV文件，尝试不同的编码方式
        try:
            df = pd.read_csv(file_path)
        except UnicodeDecodeError:
            try:
                df = pd.read_csv(file_path, encoding='gbk')
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(file_path, encoding='gb18030')
                except UnicodeDecodeError:
                    df = pd.read_csv(file_path, encoding='ISO-8859-1')

        # 获取前5行数据作为样本
        sample_df = df.head()

        # 生成markdown表格
        markdown = "### 数据样本预览\n\n"

        # 添加表头
        headers = "|" + "|".join(str(col) for col in sample_df.columns) + "|"
        separator = "|" + "|".join(["---" for _ in sample_df.columns]) + "|"

        markdown += headers + "\n" + separator + "\n"

        # 添加数据行
        for _, row in sample_df.iterrows():
            markdown += "|" + "|".join(str(val) for val in row.values) + "|\n"

        # 添加数据集信息
        markdown += f"\n### 数据集信息\n"
        markdown += f"- 总行数: {len(df)}\n"
        markdown += f"- 总列数: {len(df.columns)}\n"
        markdown += f"- 列名: {', '.join(df.columns.tolist())}\n"

        return {"result": markdown}

    except Exception as e:
        return {"result": f"错误: {str(e)}"}