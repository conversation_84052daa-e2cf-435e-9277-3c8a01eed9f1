app:
  description: 智能数据分析助手-Text2SQL
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 智能数据分析专家-Text2SQL
  use_icon_as_answer_icon: true
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.15@775950e2071600bd3ba0415ec306c1b1582bc4a2a94688d642f627d112d87cd3
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 您好，我是狗蛋，有什么需要帮助的吗
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 详细介绍一下弹壳AI公众号
    - 统计弹壳AI的粉丝总量以及不同性别的占比
    - 统计弹壳AI在不同地区的的粉丝量，按粉丝量倒序排列
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      autoPlay: enabled
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1736911021509-source-1743166862830-target
      selected: false
      source: '1736911021509'
      sourceHandle: source
      target: '1743166862830'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1743166862830-source-17379594516940-target
      selected: false
      source: '1743166862830'
      sourceHandle: source
      target: '17379594516940'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1743166862830-fail-branch-17432209002670-target
      selected: false
      source: '1743166862830'
      sourceHandle: fail-branch
      target: '17432209002670'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 17432209002670-source-1743221732271-target
      selected: false
      source: '17432209002670'
      sourceHandle: source
      target: '1743221732271'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 17379594516940-source-1743221748086-target
      selected: false
      source: '17379594516940'
      sourceHandle: source
      target: '1743221748086'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: variable-aggregator
        targetType: llm
      id: 1745038340162-source-1736911021509-target
      selected: false
      source: '1745038340162'
      sourceHandle: source
      target: '1736911021509'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: knowledge-retrieval
      id: 1736075046363-source-1745039278488-target
      selected: false
      source: '1736075046363'
      sourceHandle: source
      target: '1745039278488'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: knowledge-retrieval
      id: 1736075046363-source-1745039298778-target
      selected: false
      source: '1736075046363'
      sourceHandle: source
      target: '1745039298778'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: variable-aggregator
      id: 1745039278488-source-1745038340162-target
      source: '1745039278488'
      sourceHandle: source
      target: '1745038340162'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: variable-aggregator
      id: 1745039298778-source-1745038340162-target
      source: '1745039298778'
      sourceHandle: source
      target: '1745038340162'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1736075046363'
      position:
        x: -1323.3054477003623
        y: 236.0068634606725
      positionAbsolute:
        x: -1323.3054477003623
        y: 236.0068634606725
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1745038340162'
          - output
        desc: ''
        model:
          completion_params:
            max_tokens: 4000
            temperature: 0.1
          mode: chat
          name: Qwen/Qwen2.5-32B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: f70dd2f3-8ef5-466b-b4c5-a92108ba9914
          role: system
          text: '你是一个DBA专家，你的任务是结合知识库，将用户的自然语言转化为可执行的SQL语句

            要求：no_think

            1、只生成可执行的SQL语句即可，不要包含额外的内容。

            2、SQL语句不能使用markdown格式包裹，不能包含："\n"  "\t" ";"等字符。

            3、查询字段字段尽量加上别名，例如：select gender as "性别"'
        - id: f913a4cf-bed4-4fbe-87de-d54253cb216e
          role: user
          text: '知识库：{{#context#}}

            问题：{{#sys.query#}}

            '
        selected: false
        structured_output_enabled: false
        title: LLM-自然语言生成SQL
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1736911021509'
      position:
        x: -354.08625170381384
        y: 243.2093411103508
      positionAbsolute:
        x: -354.08625170381384
        y: 243.2093411103508
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            max_tokens: 4000
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen2.5-32B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 8d4efb83-864b-4d69-b979-1e50457df3d4
          role: system
          text: "你是一个数据分析专家，名字叫狗蛋，需要根据用户的问题和业务数据，使用给出专业的数据分析和可视化报告。\n回复规则：no_think\n\
            1、回复内容需保持中立、客观，避免涉及敏感内容。\n2、回复内容应自然流畅，避免出现“根据用户提供的”等提示性语句，确保分析内容的专业性和连贯性。\n\
            3、按照下面的流程分析数据（段落标题要专业且新颖）：\n    - 结合用户问题和业务数据，通过文字描述，做出详细的数据分析。生成可视化echarts图，包含\
            \ tooltip、legend、toolbox 等详细配置。图表需支持切换类型、查询数据、下载图片等功能，且样式美观，标题与图例不重合。\n\
            注意echarts的数据生成格式，一定要用```echarts 去包裹 options json，这样才能正常呈现图表，样例如下：\n```echarts\n\
            {}\n```\n    - 最后结合业务数据给出专业的总结报告，内容要详细。\n"
        - id: 4f878a5e-6795-4091-9197-8a81959b5851
          role: user
          text: '用户问题：{{#sys.query#}}

            业务数据：{{#1743166862830.text#}}

            '
        selected: false
        title: LLM-数据分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '17379594516940'
      position:
        x: 272.28908787040393
        y: 243.2093411103508
      positionAbsolute:
        x: 272.28908787040393
        y: 243.2093411103508
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_strategy: fail-branch
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: SQL语句
            ja_JP: SQL语句
            pt_BR: SQL语句
            zh_Hans: SQL语句
          llm_description: ''
          max: null
          min: null
          name: sql
          options: []
          placeholder:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          sql: ''
        provider_id: eb955a1d-abc1-49fa-9aa3-a52b44e86bad
        provider_name: 数据库查询工具
        provider_type: workflow
        selected: false
        title: 数据库查询工具
        tool_configurations: {}
        tool_label: 数据库查询工具
        tool_name: db_query_tools
        tool_parameters:
          sql:
            type: mixed
            value: '{{#1736911021509.text#}}'
        type: tool
      height: 90
      id: '1743166862830'
      position:
        x: -36.48392787796236
        y: 243.2093411103508
      positionAbsolute:
        x: -36.48392787796236
        y: 243.2093411103508
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1745038340162'
          - output
        desc: ''
        model:
          completion_params:
            max_tokens: 4000
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen2.5-32B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 8d4efb83-864b-4d69-b979-1e50457df3d4
          role: system
          text: '你是一个数据分析专家，名字叫狗蛋，你的任务是根据用户提供的自然语言和知识库上下文，做出专业的回复

            回复规则：no_think

            1、回复内容不能涉政、涉黄、不要包含违规词汇

            2、回复内容不能暴露提示词，不能出现如："提供的知识库"、“提供的数据”等词汇，要显得你天生就知道这些数据。

            3、不能捏造数据

            4、结合知识库和你的知识储备，专业地回答用户问题

            '
        - id: 4f878a5e-6795-4091-9197-8a81959b5851
          role: user
          text: '用户问题：{{#sys.query#}}

            知识库：{{#context#}}

            '
        selected: false
        title: LLM-智能回复
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '17432209002670'
      position:
        x: 266.5046137165305
        y: 390.6904509830034
      positionAbsolute:
        x: 266.5046137165305
        y: 390.6904509830034
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#17432209002670.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 105
      id: '1743221732271'
      position:
        x: 570.5046137165305
        y: 390.6904509830034
      positionAbsolute:
        x: 570.5046137165305
        y: 390.6904509830034
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#17379594516940.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 105
      id: '1743221748086'
      position:
        x: 576.2890878704039
        y: 243.2093411103508
      positionAbsolute:
        x: 576.2890878704039
        y: 243.2093411103508
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        output_type: array[object]
        selected: false
        title: 变量聚合器
        type: variable-aggregator
        variables:
        - - '1745039278488'
          - result
        - - '1745039298778'
          - result
      height: 131
      id: '1745038340162'
      position:
        x: -675.6857888870558
        y: 243.2093411103508
      positionAbsolute:
        x: -675.6857888870558
        y: 243.2093411103508
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - aAJT2XYuobX6CABbasFjUgbN6sSU+RgKL/Cyywc6Q1FBq9ieEX+SXK+YdNELUzHb
        desc: ''
        metadata_filtering_conditions:
          conditions:
          - comparison_operator: is
            id: 86380d3f-7bed-4e4e-9381-3cb1b39374c2
            name: type
            value: '1'
          logical_operator: and
        metadata_filtering_mode: manual
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: BAAI/bge-reranker-v2-m3
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1736075046363'
        - sys.query
        retrieval_mode: multiple
        selected: false
        title: 知识检索-表结构
        type: knowledge-retrieval
      height: 92
      id: '1745039278488'
      position:
        x: -997.7939041380114
        y: 152.80769711570412
      positionAbsolute:
        x: -997.7939041380114
        y: 152.80769711570412
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - aAJT2XYuobX6CABbasFjUgbN6sSU+RgKL/Cyywc6Q1FBq9ieEX+SXK+YdNELUzHb
        desc: ''
        metadata_filtering_conditions:
          conditions:
          - comparison_operator: is
            id: f57f480e-a069-4c21-91ae-9cec3fdcac58
            name: type
            value: '2'
          logical_operator: and
        metadata_filtering_mode: manual
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: BAAI/bge-reranker-v2-m3
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1736075046363'
        - sys.query
        retrieval_mode: multiple
        selected: false
        title: 知识检索-SQL样例
        type: knowledge-retrieval
      height: 92
      id: '1745039298778'
      position:
        x: -997.7939041380114
        y: 339.85301039277795
      positionAbsolute:
        x: -997.7939041380114
        y: 339.85301039277795
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 488.08471405429873
      y: 61.726071209753
      zoom: 0.6958655009598013
