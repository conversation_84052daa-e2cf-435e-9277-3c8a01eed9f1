import json
import re
from collections import Counter

def main(json_str: str) -> dict:
    try:
        # 预处理：清理非 JSON 部分
        json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
        if not json_match:
            return {"result": "Error: Invalid JSON format"}
        
        # 提取合法的 JSON 部分
        cleaned_json_str = json_match.group(0)
        
        # 解析JSON数据
        data = json.loads(cleaned_json_str)
        
        # 获取客户档位数据
        customer_data = data.get("数据", [])
        
        # 统计客户档位数量
        档位_counts = Counter([item.get("客户档位", "未知") for item in customer_data])
        
        # 转换为列表格式
        档位_labels = list(档位_counts.keys())
        档位_values = list(档位_counts.values())
        
        # 构建ECharts配置
        echarts_config = {
            "title": {
                "text": "客户档位统计"
            },
            "tooltip": {
                "trigger": "item",
                "formatter": "{a} <br/>{b}: {c} ({d}%)"
            },
            "legend": {
                "orient": "vertical",
                "left": "left",
                "data": 档位_labels
            },
            "series": [
                {
                    "name": "客户档位",
                    "type": "pie",
                    "radius": ["50%", "70%"],
                    "avoidLabelOverlap": False,
                    "itemStyle": {
                        "borderRadius": 10,
                        "borderColor": "#fff",
                        "borderWidth": 2
                    },
                    "label": {
                        "show": True,
                        "formatter": "{b}: {c} ({d}%)"
                    },
                    "emphasis": {
                        "label": {
                            "show": True,
                            "fontSize": 16,
                            "fontWeight": "bold"
                        }
                    },
                    "data": [{"value": 档位_values[i], "name": 档位_labels[i]} for i in range(len(档位_labels))]
                }
            ]
        }
        
        # 添加柱状图展示
        bar_config = {
            "title": {
                "text": "客户档位数量统计"
            },
            "tooltip": {
                "trigger": "axis"
            },
            "xAxis": {
                "type": "category",
                "data": 档位_labels,
                "axisLabel": {
                    "interval": 0,
                    "rotate": 45
                }
            },
            "yAxis": {
                "type": "value",
                "name": "数量"
            },
            "series": [
                {
                    "name": "客户数量",
                    "type": "bar",
                    "data": 档位_values,
                    "itemStyle": {
                        "color": "#3f51b5"
                    },
                    "label": {
                        "show": True,
                        "position": "top"
                    }
                }
            ]
        }
        
        # 生成输出文件
        pie_output = "```echarts\n" + json.dumps(echarts_config, indent=2, ensure_ascii=False) + "\n```"
        bar_output = "```echarts\n" + json.dumps(bar_config, indent=2, ensure_ascii=False) + "\n```"
        
        # 返回结果
        return {
            "result": f"## 客户档位饼图\n{pie_output}\n\n## 客户档位柱状图\n{bar_output}"
        }
    
    except Exception as e:
        return {
            "result": f"Error: {str(e)}"
        }