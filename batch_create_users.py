import http.client
import json
import time

# 读取用户数据
with open('user_data.json', 'r', encoding='utf-8') as f:
    users_data = json.load(f)

# 配置
HOST = "localhost"
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiNjFlZWRjNmItZDUyNy00ZjUxLWIyYTgtMTNjMTgxNzJjZDUyIiwiZXhwIjoxNzUyNzYxNjc4LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.EBvfxkixwBEY2pu9-441t0IhiCPKRcHICTYdcivSGP8"
TENANT_ID = "e29ac578-df0c-4049-bb86-87abb979bd43"
DEFAULT_PASSWORD = "a12345678"

# 统计变量
success_count = 0
failed_count = 0
failed_users = []

def create_user(user_data):
    global success_count, failed_count, failed_users
    
    conn = http.client.HTTPConnection(HOST)
    
    payload = json.dumps({
        "email": user_data["email"],
        "name": user_data["name"],
        "password": DEFAULT_PASSWORD,
        "tenant_id": TENANT_ID,
        "department": user_data["department"],
        "unit": user_data["unit"]
    })
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {TOKEN}',
        'Accept': '*/*'
    }
    
    try:
        conn.request("POST", "/admin/api/add_account.json", payload, headers)
        res = conn.getresponse()
        data = res.read()
        
        # 解析返回的JSON数据
        try:
            response_data = json.loads(data.decode('utf-8'))
        except:
            print(f"✗ 响应解析失败: {user_data['name']} ({user_data['email']})")
            failed_count += 1
            failed_users.append({
                'name': user_data['name'],
                'email': user_data['email'],
                'error_code': 'RESPONSE_PARSE_ERROR',
                'error_message': data.decode('utf-8')
            })
            return
        
        # 检查是否成功
        if response_data.get('result') == 'success':
            print(f"✓ 成功创建用户: {user_data['name']} ({user_data['email']})")
            success_count += 1
        else:
            # 失败情况
            error_code = response_data.get('code', 'UNKNOWN')
            error_msg = response_data.get('message', '未知错误')
            
            print(f"✗ 创建用户失败: {user_data['name']} ({user_data['email']})")
            print(f"  错误代码: {error_code}")
            print(f"  错误信息: {error_msg}")
            
            failed_count += 1
            failed_users.append({
                'name': user_data['name'],
                'email': user_data['email'],
                'error_code': error_code,
                'error_message': error_msg
            })
            
    except Exception as e:
        print(f"✗ 请求异常: {user_data['name']} ({user_data['email']}) - {str(e)}")
        failed_count += 1
        failed_users.append({
            'name': user_data['name'],
            'email': user_data['email'],
            'error_code': 'REQUEST_ERROR',
            'error_message': str(e)
        })
    finally:
        conn.close()

# 批量创建用户
print(f"开始批量创建 {len(users_data)} 个用户...")
print("=" * 50)

for i, user in enumerate(users_data, 1):
    print(f"[{i}/{len(users_data)}] 正在创建: {user['name']}")
    create_user(user)
    time.sleep(0.2)  # 避免请求过快

print("=" * 50)
print("批量创建完成!")
print(f"总计: {len(users_data)} 个用户")
print(f"成功: {success_count} 个")
print(f"失败: {failed_count} 个")

# 输出失败详情
if failed_users:
    print("\n失败用户详情:")
    print("-" * 50)
    for failed_user in failed_users:
        print(f"姓名: {failed_user['name']}")
        print(f"邮箱: {failed_user['email']}")
        print(f"错误: {failed_user['error_code']} - {failed_user['error_message']}")
        print("-" * 30)
    
    # 保存失败记录到文件
    with open('failed_users.json', 'w', encoding='utf-8') as f:
        json.dump(failed_users, f, ensure_ascii=False, indent=2)
    print(f"\n失败记录已保存到 failed_users.json")

