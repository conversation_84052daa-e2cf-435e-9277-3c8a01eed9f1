@echo off
setlocal

:: 设置变量
set IMAGE_NAME=difyserver
set IMAGE_TAG=latest

:: 确保 Docker Buildx 可用
echo 正在设置 Docker Buildx...
docker buildx create --name difybuilder --use 2>nul || echo 构建器已存在
docker buildx inspect --bootstrap

:: 构建多架构镜像
echo 开始构建多架构 Docker 镜像...
docker buildx build --platform linux/amd64,linux/arm64 ^
  -t %IMAGE_NAME%:%IMAGE_TAG% ^
  --push ^
  .

echo 多架构 Docker 镜像构建完成！
echo 镜像名称: %IMAGE_NAME%:%IMAGE_TAG%

endlocal
