# DifyServer Docker 部署指南

本文档提供了使用 Docker 和 Docker Compose 部署 DifyServer 的详细说明。

## 目录结构

```
DifyServer/
├── Dockerfile          # 多阶段构建的 Dockerfile
├── docker-compose.yml  # Docker Compose 配置文件
├── config.yaml         # 应用配置文件
└── ...                 # 其他项目文件
```

## 快速开始

### 1. 构建 Docker 镜像

```bash
# 构建支持 arm64 和 amd64 双架构的镜像
docker build -t difyserver:v1 .
```

### 2. 使用 Docker Compose 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 3. 访问应用

服务启动后，可以通过浏览器访问 http://localhost:8080 使用应用。

## 配置说明

### 数据库配置

默认的数据库配置如下：

```yaml
database:
  host: "docker-db-1"  # PostgreSQL 容器名称
  port: 5432           # PostgreSQL 默认端口
  user: "postgres"     # 数据库用户名
  password: "difyai123456"  # 数据库密码
  dbname: "dify"       # 数据库名称
```

如需修改数据库配置，请同时更新 `config.yaml` 和 `docker-compose.yml` 文件。

## 常用命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f
```

## 数据持久化

数据库数据存储在名为 `postgres_data` 的 Docker 卷中，确保数据不会在容器重启后丢失。

## 多架构支持

DifyServer Docker 镜像支持 arm64 和 amd64 双架构，可以在不同硬件平台上运行。
