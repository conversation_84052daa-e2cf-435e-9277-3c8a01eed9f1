# Please do not directly edit this file. Instead, modify the .env variables related to NGINX configuration.

server {
    listen 80;
    server_name _;

    location /console/api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /v1 {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /files {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /explore {
      proxy_pass http://web:3000;
      include proxy.conf;
    }

    location /admin {
      proxy_pass http://difyserver:8080;
      include proxy.conf;
    }

location /admin/static/ {
    proxy_pass http://difyserver:8080/static/;
    include proxy.conf;
}
# 特定静态文件
location = /admin/favicon.ico {
    proxy_pass http://difyserver:8080/favicon.ico;
    include proxy.conf;
}

location = /admin/logo192.png {
    proxy_pass http://difyserver:8080/logo192.png;
    include proxy.conf;
}

location = /admin/manifest.json {
    proxy_pass http://difyserver:8080/manifest.json;
    include proxy.conf;
}
location /admin/api/ {
    proxy_pass http://difyserver:8080/api/;
    include proxy.conf;
}
    location /e/ {
      proxy_pass http://plugin_daemon:5002;
      proxy_set_header Dify-Hook-Url $scheme://$host$request_uri;
      include proxy.conf;
    }

    location / {
      proxy_pass http://web:3000;
      include proxy.conf;
    }

    # placeholder for acme challenge location


    # placeholder for https config defined in https.conf.template

}
