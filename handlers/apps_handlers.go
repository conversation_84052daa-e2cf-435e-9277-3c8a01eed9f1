package handlers

import (
	"errors"
	"log/slog"

	"difyserver/database"
	"difyserver/models"
	"difyserver/utils/define"
	"difyserver/view"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// ListAppStatus godoc
// @Summary 获取应用状态
// @Description 获取应用状态
// @Tags 应用管理
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=[]view.AppsSimpleView} "成功获取应用状态"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /apps/status.json [get]
// @Security ApiKeyAuth
func ListAppStatus(c *gin.Context) {
	tenant := CurrentTenant(c)
	var apps []models.Apps
	if result := database.DB.Model(&models.Apps{}).Where("tenant_id=?", tenant.Id).Find(&apps); result.Error != nil {
		slog.ErrorContext(c, "DB_ERROR", "获取应用状态失败", result.Error)
		ErrorResponse(c, "DB_ERROR", "获取应用状态失败", 500)
		return
	}
	appsView := lo.Map(apps, func(item models.Apps, index int) view.AppsSimpleView {
		return view.AppsSimpleView{
			Name:       item.Name,
			APPID:      item.Id,
			EnableSite: item.EnableSite,
			Status:     item.Status,
			OnlyMe:     item.OnlyMe,
		}
	})
	SuccessResponse(c, appsView)
}

// UpdateOnlyMeStatus godoc
// @Summary 更新应用OnlyMe状态
// @Description 更新指定应用的OnlyMe状态
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param app body view.UpdateAppOnlyMeStatusParam true "更新应用OnlyMe状态参数"
// @Success 200 {object} view.Result{data=models.Apps} "成功更新的应用信息"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /apps/update_only_me_status.json [post]
// @Security ApiKeyAuth
func UpdateOnlyMeStatus(c *gin.Context) {
	req := new(view.UpdateAppOnlyMeStatusParam)
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	if req.AppID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "应用ID不能为空", 400)
		return
	}

	// 获取租户信息，如果外部API失败则从数据库获取
	var tenantID string
	tenant := CurrentTenant(c)
	if tenant != nil && tenant.Id != "" {
		tenantID = tenant.Id
	} else {
		// 备用方案：从数据库中获取用户的租户信息
		userID := c.GetString(define.UserIDKey)
		if userID == "" {
			ErrorResponse(c, "MISSING_USER", "获取用户信息失败", 500)
			return
		}

		var tenantJoin models.TenantAccountJoin
		if result := database.DB.Where("account_id = ?", userID).First(&tenantJoin); result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				ErrorResponse(c, "NO_TENANT", "用户未关联任何租户", 404)
				return
			}
			ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
			return
		}
		tenantID = tenantJoin.TenantID
	}

	// 检查应用是否存在且属于当前租户
	app := new(models.Apps)
	if result := database.DB.Where("id = ? AND tenant_id = ?", req.AppID, tenantID).First(app); result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			ErrorResponse(c, "NOT_FOUND", "应用不存在", 404)
			return
		}
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 只更新OnlyMe字段，避免更新其他可能有问题的字段
	if result := database.DB.Model(app).Update("only_me", req.OnlyMe); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 重新查询更新后的应用信息
	if result := database.DB.Where("id = ?", req.AppID).First(app); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", "获取更新后的应用信息失败", 500)
		return
	}

	SuccessResponse(c, app)
}
