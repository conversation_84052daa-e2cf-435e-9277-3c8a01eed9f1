package handlers

import (
	"difyserver/config"
	"difyserver/database"
	"difyserver/models"
	"difyserver/utils/define"
	"difyserver/view"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm/utils"
)

// ListNemu godoc
// @Summary 获取菜单树
// @Description 获取系统中的菜单树，支持分页
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=[]models.SystemMenu} "成功获取菜单树"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /menus.json [get]
// @Security ApiKeyAuth
func ListNemu(c *gin.Context) {
	menus := make([]*models.SystemMenu, 0)
	all, err := database.GetMenuDao().GetAll()
	if err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}
	adminEmail := c.GetString(define.UserEmailKey)
	if adminEmail == "" {
		SuccessResponse(c, menus)
		return
	}
	value, exists := c.Get(define.CurrentTenantKey)
	if !exists {
		ErrorResponse(c, "MISSING_TENANT", "missing tenant", 500)
		return
	}

	tenant := value.(*view.CurrentTenant)
	role := tenant.Role
	if lo.Contains(config.GlobalConfig.Admins, adminEmail) {
		role = view.TenantAccountRoleOwner
	} else if tenant.Role == view.TenantAccountRoleOwner {
		role = view.TenantAccountRoleAdmin
	}

	removeNemus := make([]string, 0)
	if role == view.TenantAccountRoleAdmin {
		removeNemus = []string{view.TenantAccountRoleOwner}
	} else if role == view.TenantAccountRoleEditor {
		removeNemus = []string{view.TenantAccountRoleAdmin, view.TenantAccountRoleOwner}
	} else if role == view.TenantAccountRoleNormal {
		removeNemus = []string{view.TenantAccountRoleAdmin, view.TenantAccountRoleEditor, view.TenantAccountRoleOwner}
	}

	for _, menu := range all {
		if menu.Permiss != nil && utils.Contains(removeNemus, *menu.Permiss) {
			continue
		}
		menus = append(menus, menu)
	}
	tree := view.BuildMenuTree(menus)
	SuccessResponse(c, tree)
}
