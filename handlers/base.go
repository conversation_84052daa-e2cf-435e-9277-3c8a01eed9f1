package handlers

import (
	"difyserver/database"
	"difyserver/middleware"
	"difyserver/models"
	"difyserver/utils/define"
	"difyserver/view"
	"github.com/gin-contrib/cors"
	swaggerFiles "github.com/swaggo/files"
	swagger "github.com/swaggo/gin-swagger"

	"github.com/gin-gonic/gin"
	// Swagger 文档
	_ "difyserver/docs" // 导入生成的 docs 包
)

func InitHandler(r *gin.Engine) {

	// CORS 配置...
	r.Use(cors.New(cors.Config{
		AllowAllOrigins: true,
		// 		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:3001","http://localhost"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "Access-Control-Allow-Origin"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))
	// 静态文件服务
	r.Static("/static", "./frontend/build/static")
	r.Static("/images", "./frontend/build/images")
	r.StaticFile("/favicon.ico", "./frontend/build/favicon.ico")
	r.StaticFile("/logo192.png", "./frontend/build/logo192.png")
	// 答题系统页面路由（无需后端认证，前端检查登录状态）
	r.GET("/api/quiz/statistics/page", ShowStatisticsPage) // 统计页面
	r.GET("/api/quiz/dashboard/page", ShowDashboardPage)   // 仪表板页面
	r.GET("/api/quiz/:uuid", ShowQuizPage)                 // 答题页面

	// 答题系统API路由（需要登录验证）
	quizAPIGroup := r.Group("/api")
	quizAPIGroup.Use(QuizAuthRequired())
	{
		quizAPIGroup.GET("/verify-login", VerifyLoginStatus)                           // 验证登录状态
		quizAPIGroup.POST("/quiz/:uuid/submit", SubmitQuizAnswers)                     // 提交答题
		quizAPIGroup.GET("/quiz/:uuid/check", CheckUserAnswered)                       // 检查答题状态
		quizAPIGroup.GET("/quiz/:uuid/statistics", GetQuizStatistics)                  // 获取题目统计
		quizAPIGroup.GET("/quiz/:uuid/questions", GetQuizQuestions)                    // 获取题目问题数据
		quizAPIGroup.GET("/quiz/:uuid/results", GetQuizResults)                        // 获取题目结果
		quizAPIGroup.GET("/quiz/batch/:title/statistics", GetBatchStatistics)          // 获取批次统计
		quizAPIGroup.GET("/quiz/batch/:title/user-statistics", GetBatchUserStatistics) // 获取批次用户统计
		quizAPIGroup.GET("/quiz/dashboard", GetQuizDashboard)                          // 获取仪表板
	}

	// 超管专用的答题系统API路由
	quizAdminAPIGroup := r.Group("/api")
	quizAdminAPIGroup.Use(QuizAuthRequired())
	quizAdminAPIGroup.Use(QuizAdminRequired())
	{
		quizAdminAPIGroup.DELETE("/quiz/batch/:title", DeleteBatch) // 删除批次（仅超管）
	}

	// 处理前端路由
	r.NoRoute(func(c *gin.Context) {
		c.File("./frontend/build/index.html")
	})
	// API 路由...
	r.POST("/api/login.json", Login)
	r.POST("/api/refresh_token.json", RefreshToken)
	r.GET("/api/file-url.json", GetFileURL)
	r.GET("/api/file/:fileID", GetFile)
	r.GET("/api/file", GetFileFromLocal)
	auth := r.Group("/api")
	// 普通用户权限验证
	auth.Use(middleware.AuthMiddleware())
	{
		auth.GET("/menus.json", ListNemu)

		// 用户反馈功能路由
		auth.GET("/user/feedbacks.json", GetUserFeedbacks) // 获取用户自己的反馈
		auth.POST("/feedbacks.json", CreateFeedback)       // 用户提交反馈

		// 锚点统计功能路由
		auth.POST("/anchor-statistics/record.json", RecordAnchorStatistics)                      // 记录锚点统计
		auth.GET("/anchor-statistics/resource-summary.json", GetAnchorStatisticsResourceSummary) // 获取资源汇总统计数据

		// 用户文档功能路由（普通用户可查看）
		auth.GET("/documents.json", GetUserDocuments)           // 获取用户文档列表
		auth.GET("/documents/:doc_type", GetUserDocumentByType) // 根据文档类型获取用户文档
		auth.GET("/documents/id/:id", GetUserDocumentByID)      // 根据ID获取用户文档详情

		auth.GET("/logout.json", Logout)
		auth.GET("/current_tenant.json", GetCurrentTenant)
		auth.POST("/switch_tenant.json", SwitchTenant)
		auth.GET("/apps/status.json", ListAppStatus)
		auth.POST("/apps/update_only_me_status.json", UpdateOnlyMeStatus)
	}
	// 管理员用户权限验证
	auth.Use(middleware.AuthAdminMiddleware())
	{

		auth.GET("/datasets.json", GetDatasets)
		auth.GET("/list_can_join_tenant.json", ListCanJoinTenant)
		auth.GET("/list_tenant_can_join_account.json", ListCanJoinAccount)
		auth.GET("/list_dataset_tenant.json", ListDatasetTenant)
		auth.POST("/add_dataset_tenant.json", AddDatasetTenant)
		auth.POST("/del_dataset_tenant.json", DelDatasetTenant)
		auth.GET("/list_tenant_account.json", ListTenantAccount)
		auth.GET("/list_tenant_account_by_account.json", ListTenantAccountByAccount)
		auth.GET("/list_tenant_account_by_tenant.json", ListTenantAccountByTenant)
		auth.POST("/add_tenant_account.json", AddTenantAccount)
		auth.POST("/del_tenant_account.json", DelTenantAccount)
		auth.POST("/update_tenant_account_role.json", UpdateTenantAccountRole)
		//auth.POST("/api/login.json", Login)

		auth.GET("/accounts.json", GetAccounts)

		// 管理员反馈功能路由
		auth.GET("/feedbacks.json", GetAllFeedbacks)                // 获取所有反馈（管理员）
		auth.POST("/feedbacks/:id/respond.json", RespondToFeedback) // 管理员回复反馈
		auth.GET("/feedbacks/:id/info.json", GetFeedback)           // 查看单个反馈

		// 管理员锚点统计功能路由
		auth.GET("/anchor-statistics.json", GetAnchorStatistics)                    // 获取锚点统计列表
		auth.GET("/anchor-statistics/aggregate.json", GetAnchorStatisticsAggregate) // 获取聚合统计数据
		auth.GET("/anchor-statistics/analysis.json", GetAnchorStatisticsAnalysis)   // 获取分析统计数据
		auth.GET("/anchor-statistics/:id/info.json", GetAnchorStatisticsDetail)     // 获取统计详情

		// 用户文档管理功能路由（只有配置文件中的超管可以操作）
		auth.POST("/documents.json", CreateUserDocument)              // 创建用户文档
		auth.PUT("/documents/id/:id", UpdateUserDocumentByID)         // 根据ID更新用户文档
		auth.DELETE("/documents/:doc_type", DeleteUserDocumentByType) // 根据类型删除用户文档
		auth.DELETE("/documents/id/:id", DeleteUserDocument)          // 根据ID删除用户文档

		// Dashboard API（测试用，不需要认证）
		r.GET("/api/dashboard/summary", GetDashboardSummary)

		// 节假日检查API（无需认证）
		r.GET("/api/check-working-day", CheckWorkingDay)

		// 临时数据管理功能路由（无需认证）
		r.POST("/api/temp-data", CreateTempData)         // 创建临时数据
		r.GET("/api/temp-data/:uuid", GetTempDataByUUID) // 根据UUID获取临时数据
	}
	// 超管用户权限验证
	auth.Use(middleware.AuthSuperAdminMiddleware())
	{
		// Swagger 文档路由
		r.GET("/swagger/*any", swagger.WrapHandler(
			swaggerFiles.Handler,
			swagger.URL("http://localhost:8080/swagger/doc.json"), // 指定 Swagger JSON 端点的 URL
		))
		auth.POST("/add_account.json", AddAccount)
		auth.POST("/del_account.json", DelAccount)
		auth.POST("/set_account_password.json", SetAccountPassword)
		auth.POST("/update_account_info.json", UpdateAccountInfo)
		auth.GET("/tenants.json", GetTenants)
		auth.POST("/add_tenant.json", AddTenant)
		auth.POST("/del_tenant.json", DelTenant)
		auth.POST("/update_tenant_name.json", UpdateTenantName)
		auth.POST("/update_tenant_status.json", UpdateTenantStatus)
	}

	// 配置文件超管权限验证（用于用户文档管理）
	auth.Use(middleware.AuthConfigAdminMiddleware())
	{
		// 模拟数据生成API（仅超管可用）
		auth.POST("/mock-data/trigger", TriggerMockDataGeneration)
		auth.POST("/mock-data/generate-for-date", TriggerMockDataGenerationForDate)
	}
}

func GetFirstAccount(c *gin.Context) *models.Account {
	firstAccount := new(models.Account)
	result := database.DB.Where("status=?", models.ACTIVE).Order("created_at ASC").First(firstAccount)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return nil
	}
	return firstAccount
}

func GetFireTenant(c *gin.Context) *models.Tenant {
	firstTenant := new(models.Tenant)
	// get first Tenant
	result := database.DB.Where("status=?", "normal").Order("created_at ASC").First(firstTenant)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return nil
	}
	return firstTenant
}

func CurrentTenant(c *gin.Context) *view.CurrentTenant {
	value, exists := c.Get(define.CurrentTenantKey)
	if !exists {
		ErrorResponse(c, "MISSING_TENANT", "missing tenant", 500)
		return nil
	}
	tenant := value.(*view.CurrentTenant)
	return tenant
}
