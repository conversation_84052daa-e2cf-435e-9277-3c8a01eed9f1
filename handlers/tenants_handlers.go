package handlers

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"math"
	"os"
	"strconv"
	"time"

	"difyserver/config"
	"difyserver/database"
	"difyserver/models"
	"difyserver/utils"
	"difyserver/view"
	"github.com/samber/lo"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GetTenants godoc
// @Summary 获取团队列表
// @Description 获取系统中的团队列表，支持分页
// @Tags 团队管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1"
// @Success 200 {object} view.Result{data=models.PageResponse{data=[]models.Tenant}} "成功获取团队列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /tenants.json [get]
// @Security ApiKeyAuth
func GetTenants(c *gin.Context) {
	var tenants []models.Tenant
	var total int64
	pageStr := c.DefaultQuery("page", "1")
	page, _ := strconv.Atoi(pageStr)
	sizeStr := c.DefaultQuery("size", "10")
	limit, _ := strconv.Atoi(sizeStr)
	offset := (page - 1) * limit
	firstTenant := GetFireTenant(c)
	// 先获取总记录数
	database.DB.Model(&models.Tenant{}).Count(&total)

	// 获取分页数据
	result := database.DB.Limit(limit).Offset(offset).Find(&tenants)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	accounts := make([]models.Account, 0)
	if result := database.DB.Model(&models.Account{}).Where("status=?", models.ACTIVE).Find(&accounts); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	accountMap := lo.SliceToMap(accounts, func(item models.Account) (string, models.Account) {
		return item.ID, item
	})
	views := lo.Map(tenants, func(tenant models.Tenant, index int) view.TenantView {
		tenantJoins := make([]models.TenantAccountJoin, 0)
		if result := database.DB.Model(&models.TenantAccountJoin{}).Where("tenant_id=?", tenant.ID).Find(&tenantJoins); result.Error != nil {
			slog.ErrorContext(c, "DB_ERROR", "获取团队成员失败", 500)
		}
		owner := ""
		for _, join := range tenantJoins {
			if join.Role == view.TenantAccountRoleOwner {
				owner = join.AccountID
				break
			}
		}
		ownerName := accountMap[owner].Name
		ownerEmail := accountMap[owner].Email
		return view.TenantView{
			Tenant:      tenant,
			OwnerName:   ownerName,
			OwnerEmail:  ownerEmail,
			DefaultTeam: firstTenant.ID == tenant.ID,
		}
	})

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := models.PageResponse{
		Data:       views,
		Total:      total,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
	}

	SuccessResponse(c, response)
}

// AddTenant godoc
// @Summary 添加新团队
// @Description 创建一个新的团队
// @Tags 团队管理
// @Accept json
// @Produce json
// @Param tenant body view.AddTenantParam true "团队信息"
// @Success 200 {object} view.Result{data=models.Tenant} "成功创建的团队信息"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /add_tenant.json [post]
// @Security ApiKeyAuth
func AddTenant(c *gin.Context) {
	req := new(view.AddTenantParam)
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}
	if req.UserID == "" || req.Name == "" {
		ErrorResponse(c, "INVALID_PARAMS", "参数错误", 400)
		return
	}
	var result *gorm.DB
	addAccount := new(models.Account)
	if req.UserID != "" {
		result = database.DB.Where("status=?", models.ACTIVE).Where("id=?", req.UserID).First(addAccount)
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				ErrorResponse(c, "INVALID_PARAMS", "所选用户不存在", 400)
				return
			}
			ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
			return
		}
		if addAccount.ID == "" {
			ErrorResponse(c, "INVALID_PARAMS", "所选用户不存在", 400)
		}
	}

	firstTenant := new(models.Tenant)
	// get first Tenant
	result = database.DB.Where("status=?", "normal").Order("created_at ASC").First(firstTenant)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	firstTenantProviders := make([]models.Providers, 0)
	firstTenantProviderModel := make([]models.ProviderModel, 0)
	firstTenantProviderModelSettings := make([]models.ProviderModelSettings, 0)
	firstTenantPluginInstallation := make([]models.PluginInstallation, 0)
	firstTenantAiModelInstallation := make([]models.AiModelInstallation, 0)

	firstPemByte, err := os.ReadFile(fmt.Sprintf("%s/privkeys/%s/private.pem", config.GlobalConfig.DifyStoragePath, firstTenant.ID))
	if err != nil {
		ErrorResponse(c, "RSA_ERROR", err.Error(), 500)
		return
	}
	firstPem := string(firstPemByte)

	if result := database.DB.Where("tenant_id=?", firstTenant.ID).Where("provider_name in (?)", config.GlobalConfig.SyncPlugin).Find(&firstTenantProviders); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	if result := database.DB.Where("tenant_id=?", firstTenant.ID).Where("provider_name in (?)", config.GlobalConfig.SyncPlugin).Find(&firstTenantProviderModel); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	if result := database.DB.Where("tenant_id=?", firstTenant.ID).Where("provider_name in (?)", config.GlobalConfig.SyncPlugin).Find(&firstTenantProviderModelSettings); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	if result := database.PluginDB.Where("tenant_id=?", firstTenant.ID).Where("plugin_id in (?)", config.GlobalConfig.GetPluginIDs()).Find(&firstTenantPluginInstallation); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	if result := database.PluginDB.Where("tenant_id=?", firstTenant.ID).Where("plugin_id in (?)", config.GlobalConfig.GetPluginIDs()).Find(&firstTenantAiModelInstallation); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	privateKey, publicKey, err := utils.GenerateKeyPair()
	if err != nil {
		ErrorResponse(c, "RSA_ERROR", err.Error(), 500)
		return
	}
	tenant := models.Tenant{
		ID:               uuid.New().String(),
		Name:             req.Name,
		EncryptPublicKey: publicKey,
		Plan:             models.TenantPlanEnterprise,
		Status:           "normal",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}
	tenant.ID = uuid.New().String()
	tenant.CreatedAt = time.Now()
	tenant.UpdatedAt = time.Now()
	tenant.EncryptPublicKey = publicKey
	err = database.DB.Transaction(func(tx *gorm.DB) error {
		if result := tx.Create(&tenant); result.Error != nil {
			return result.Error
		}
		//同步 dify provider,providerModel,providerModelSettings 信息
		for _, provider := range firstTenantProviders {
			provider.TenantId = tenant.ID
			provider.Id = uuid.New().String()
			provider.CreatedAt = time.Now()
			provider.UpdatedAt = time.Now()
			provider.LastUsed = nil
			provider.QuotaLimit = nil
			encryptedConfig, err := updateEncryptedConfig(c, provider.EncryptedConfig, firstPem, publicKey)
			if err != nil {
				slog.ErrorContext(c, "update encrypted config error", "error", err)
				continue
			}
			provider.EncryptedConfig = encryptedConfig
			if result := tx.Create(&provider); result.Error != nil {
				slog.ErrorContext(c, "create provider error")
				return result.Error
			}
		}
		for _, provider := range firstTenantProviderModel {
			provider.ID = uuid.New().String()
			provider.TenantID = tenant.ID
			provider.CreatedAt = time.Now()
			provider.UpdatedAt = time.Now()
			encryptedConfig, err := updateEncryptedConfig(c, provider.EncryptedConfig, firstPem, publicKey)
			if err != nil {
				slog.ErrorContext(c, "update encrypted config error", "error", err)
				continue
			}
			provider.EncryptedConfig = encryptedConfig
			if result := tx.Create(&provider); result.Error != nil {
				slog.ErrorContext(c, "create provider error")
				return result.Error
			}
		}
		for _, modelSetting := range firstTenantProviderModelSettings {
			modelSetting.TenantId = tenant.ID
			modelSetting.Id = uuid.New().String()
			modelSetting.CreatedAt = time.Now()
			modelSetting.UpdatedAt = time.Now()
			if result := tx.Create(&modelSetting); result.Error != nil {
				slog.ErrorContext(c, "create model setting error", "error", result.Error)
				return result.Error
			}
		}
		//同步 dify_plugin ai_model_installations,plugin_installations 信息
		for _, plugin := range firstTenantPluginInstallation {
			plugin.Id = uuid.New().String()
			plugin.TenantId = tenant.ID
			plugin.CreatedAt = time.Now()
			plugin.UpdatedAt = time.Now()
			if result := database.PluginDB.Create(&plugin); result.Error != nil {
				slog.ErrorContext(c, "create plugin error", "error", result.Error)
				return result.Error
			}
		}
		for _, plugin := range firstTenantAiModelInstallation {
			plugin.Id = uuid.New().String()
			plugin.TenantId = tenant.ID
			plugin.CreatedAt = time.Now()
			plugin.UpdatedAt = time.Now()
			if result := database.PluginDB.Create(&plugin); result.Error != nil {
				slog.ErrorContext(c, "create plugin error", "error", result.Error)
				return result.Error
			}
		}

		join := models.TenantAccountJoin{
			ID:        uuid.New().String(),
			TenantID:  tenant.ID,
			AccountID: addAccount.ID,
			Role:      view.TenantAccountRoleOwner,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if result := tx.Create(&join); result.Error != nil {
			slog.ErrorContext(c, "create join error", "error", result.Error)
			tx.Rollback()
			return result.Error
		}
		dir := fmt.Sprintf("%s/privkeys/%s", config.GlobalConfig.DifyStoragePath, tenant.ID)
		if err := os.Mkdir(dir, 0777); err != nil {
			slog.ErrorContext(c, "create tenant directory error", "error", err)
			return err
		}
		dest := fmt.Sprintf("%s/privkeys/%s/private.pem", config.GlobalConfig.DifyStoragePath, tenant.ID)
		destFile, err := os.Create(dest)
		if err != nil {
			slog.ErrorContext(c, "create tenant file error", "error", err, "dest:", dest)
			return err
		}
		defer destFile.Close()
		_, err = io.WriteString(destFile, privateKey)
		if err != nil {
			slog.ErrorContext(c, "write tenant file error", "error", err, "dest:", dest)
			return err
		}
		return nil
	})
	if err != nil {
		ErrorResponse(c, "API_ERROR", err.Error(), 500)
		return
	}
	tenant.EncryptPublicKey = ""
	SuccessResponse(c, tenant)
}

// DelTenant godoc
// @Summary 删除团队
// @Description 删除指定的团队及其所有相关数据
// @Tags 团队管理
// @Accept json
// @Produce json
// @Param tenant body view.DelTenantParam true "删除团队参数"
// @Success 200 {object} view.Result{data=map[string]string} "成功删除团队"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /del_tenant.json [post]
// @Security ApiKeyAuth
func DelTenant(c *gin.Context) {
	req := new(view.DelTenantParam)
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	if req.TenantID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "团队ID不能为空", 400)
		return
	}

	// 检查租户是否存在
	tenant := new(models.Tenant)
	if result := database.DB.Where("id = ?", req.TenantID).First(tenant); result.Error != nil {
		ErrorResponse(c, "NOT_FOUND", "团队不存在", 404)
		return
	}

	// 检查是否为第一个租户（不允许删除第一个租户）
	firstTenant := GetFireTenant(c)
	if tenant.ID == firstTenant.ID {
		ErrorResponse(c, "FORBIDDEN", "不能删除默认团队", 403)
		return
	}

	// 开始事务删除所有相关数据
	err := database.DB.Transaction(func(tx *gorm.DB) error {
		// 1. 删除租户账户关联
		if result := tx.Where("tenant_id = ?", req.TenantID).Delete(&models.TenantAccountJoin{}); result.Error != nil {
			slog.ErrorContext(c, "delete tenant account joins error", "error", result.Error)
			return result.Error
		}

		// 2. 删除 providers
		if result := tx.Where("tenant_id = ?", req.TenantID).Delete(&models.Providers{}); result.Error != nil {
			slog.ErrorContext(c, "delete providers error", "error", result.Error)
			return result.Error
		}

		// 3. 删除 provider_models
		if result := tx.Where("tenant_id = ?", req.TenantID).Delete(&models.ProviderModel{}); result.Error != nil {
			slog.ErrorContext(c, "delete provider models error", "error", result.Error)
			return result.Error
		}

		// 4. 删除 provider_model_settings
		if result := tx.Where("tenant_id = ?", req.TenantID).Delete(&models.ProviderModelSettings{}); result.Error != nil {
			slog.ErrorContext(c, "delete provider model settings error", "error", result.Error)
			return result.Error
		}

		// 5. 删除插件相关数据（使用 PluginDB）
		if result := database.PluginDB.Where("tenant_id = ?", req.TenantID).Delete(&models.PluginInstallation{}); result.Error != nil {
			slog.ErrorContext(c, "delete plugin installations error", "error", result.Error)
			return result.Error
		}

		if result := database.PluginDB.Where("tenant_id = ?", req.TenantID).Delete(&models.AiModelInstallation{}); result.Error != nil {
			slog.ErrorContext(c, "delete ai model installations error", "error", result.Error)
			return result.Error
		}

		// 6. 最后删除租户本身
		if result := tx.Where("id = ?", req.TenantID).Delete(&models.Tenant{}); result.Error != nil {
			slog.ErrorContext(c, "delete tenant error", "error", result.Error)
			return result.Error
		}

		// 7. 删除租户的私钥目录和文件
		tenantDir := fmt.Sprintf("%s/privkeys/%s", config.GlobalConfig.DifyStoragePath, req.TenantID)
		if err := os.RemoveAll(tenantDir); err != nil {
			slog.ErrorContext(c, "delete tenant directory error", "error", err, "dir", tenantDir)
			return err
		}

		return nil
	})

	if err != nil {
		ErrorResponse(c, "API_ERROR", err.Error(), 500)
		return
	}

	SuccessResponse(c, gin.H{"message": "删除团队成功"})
}

func updateEncryptedConfig(c *gin.Context, encryptedConfigStr, firstPem, publicKey string) (string, error) {
	encryptedConfig := new(view.EncryptedConfig)
	if err := json.Unmarshal([]byte(encryptedConfigStr), encryptedConfig); err != nil {
		slog.ErrorContext(c, "unmarshal provider error", "providerID", encryptedConfigStr)
		return "", err
	}
	if encryptedConfig.ApiKey == "" {
		return encryptedConfigStr, nil
	}
	apiKeyByte, err := base64.StdEncoding.DecodeString(encryptedConfig.ApiKey)
	if err != nil {
		slog.ErrorContext(c, "base64 decode provider error", "providerID", encryptedConfigStr)
		return "", err
	}
	apiKey, err := utils.Decrypt(apiKeyByte, firstPem)
	if err != nil {
		slog.ErrorContext(c, "decrypt provider error", "providerID", encryptedConfigStr)
		return "", err
	}
	encryptApiKey, err := utils.Encrypt(apiKey, publicKey)
	if err != nil {
		slog.ErrorContext(c, "encrypt provider error", "providerID", encryptedConfigStr)
		return "", err
	}
	encryptedConfig.ApiKey = base64.StdEncoding.EncodeToString(encryptApiKey)
	marshal, _ := json.Marshal(encryptedConfig)
	return string(marshal), nil
}

// UpdateTenantName godoc
// @Summary 更新团队名称
// @Description 更新指定团队的名称
// @Tags 团队管理
// @Accept json
// @Produce json
// @Param tenant body view.UpdateTenantNameParam true "更新团队名称参数"
// @Success 200 {object} view.Result{data=models.Tenant} "成功更新的团队信息"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /update_tenant_name.json [post]
// @Security ApiKeyAuth
func UpdateTenantName(c *gin.Context) {
	req := new(view.UpdateTenantNameParam)
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	if req.TenantID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "团队ID不能为空", 400)
		return
	}

	if req.Name == "" {
		ErrorResponse(c, "INVALID_PARAMS", "团队名称不能为空", 400)
		return
	}

	// 检查租户是否存在
	tenant := new(models.Tenant)
	if result := database.DB.Where("id = ?", req.TenantID).First(tenant); result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			ErrorResponse(c, "NOT_FOUND", "团队不存在", 404)
			return
		}
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 更新团队名称
	tenant.Name = req.Name
	tenant.UpdatedAt = time.Now()

	if result := database.DB.Save(tenant); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 清除敏感信息
	tenant.EncryptPublicKey = ""
	SuccessResponse(c, tenant)
}

// UpdateTenantStatus godoc
// @Summary 更新团队状态
// @Description 更新指定团队的状态，支持normal和archive两种状态
// @Tags 团队管理
// @Accept json
// @Produce json
// @Param tenant body view.UpdateTenantStatusParam true "更新团队状态参数"
// @Success 200 {object} view.Result{data=models.Tenant} "成功更新的团队信息"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /update_tenant_status.json [post]
// @Security ApiKeyAuth
func UpdateTenantStatus(c *gin.Context) {
	req := new(view.UpdateTenantStatusParam)
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	if req.TenantID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "团队ID不能为空", 400)
		return
	}

	if req.Status == "" {
		ErrorResponse(c, "INVALID_PARAMS", "团队状态不能为空", 400)
		return
	}

	// 验证状态值是否有效
	if !view.ValidTenantStatuses[req.Status] {
		ErrorResponse(c, "INVALID_PARAMS", "团队状态只支持normal和archive", 400)
		return
	}

	// 检查租户是否存在
	tenant := new(models.Tenant)
	if result := database.DB.Where("id = ?", req.TenantID).First(tenant); result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			ErrorResponse(c, "NOT_FOUND", "团队不存在", 404)
			return
		}
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 检查是否为第一个租户（不允许修改第一个租户的状态）
	firstTenant := GetFireTenant(c)
	if tenant.ID == firstTenant.ID {
		ErrorResponse(c, "FORBIDDEN", "不能修改默认团队的状态", 403)
		return
	}

	// 更新团队状态
	tenant.Status = req.Status
	tenant.UpdatedAt = time.Now()

	if result := database.DB.Save(tenant); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 清除敏感信息
	tenant.EncryptPublicKey = ""
	SuccessResponse(c, tenant)
}
