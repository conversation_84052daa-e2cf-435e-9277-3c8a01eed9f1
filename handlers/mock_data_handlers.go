package handlers

import (
	"net/http"
	"time"

	"difyserver/config"
	"difyserver/scheduler"
	"difyserver/services"

	"github.com/gin-gonic/gin"
)

// TriggerMockDataGeneration godoc
// @Summary 手动触发模拟数据生成
// @Description 手动触发一次模拟数据生成（用于测试）
// @Tags 模拟数据
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功触发模拟数据生成"
// @Failure 500 {object} map[string]string "服务器错误"
// @Router /mock-data/trigger [post]
// @Security ApiKeyAuth
func TriggerMockDataGeneration(c *gin.Context) {
	if !config.GlobalConfig.MockData.Enabled {
		ErrorResponse(c, "MOCK_DATA_DISABLED", "模拟数据生成已禁用", 400)
		return
	}

	generator := scheduler.NewMockDataGenerator(&config.GlobalConfig)
	err := generator.GenerateDailyMockData()
	if err != nil {
		ErrorResponse(c, "GENERATION_ERROR", err.Error(), 500)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"success": true,
			"message": "模拟数据生成完成",
		},
	})
}

// TriggerMockDataGenerationForDate godoc
// @Summary 为指定日期手动触发模拟数据生成
// @Description 为指定日期手动触发一次模拟数据生成（用于测试和补充历史数据）
// @Tags 模拟数据
// @Accept json
// @Produce json
// @Param date query string true "目标日期 YYYY-MM-DD" example(2025-06-29)
// @Success 200 {object} map[string]interface{} "成功触发模拟数据生成"
// @Failure 400 {object} map[string]string "参数错误"
// @Failure 500 {object} map[string]string "服务器错误"
// @Router /mock-data/generate-for-date [post]
// @Security ApiKeyAuth
func TriggerMockDataGenerationForDate(c *gin.Context) {
	if !config.GlobalConfig.MockData.Enabled {
		ErrorResponse(c, "MOCK_DATA_DISABLED", "模拟数据生成已禁用", 400)
		return
	}

	// 获取日期参数
	dateStr := c.Query("date")
	if dateStr == "" {
		ErrorResponse(c, "INVALID_PARAMS", "缺少日期参数，格式：YYYY-MM-DD", 400)
		return
	}

	// 解析日期
	targetDate, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "日期格式错误，请使用 YYYY-MM-DD 格式", 400)
		return
	}

	// 检查日期是否在合理范围内（不能是未来日期，不能超过1年前）
	now := time.Now()
	if targetDate.After(now) {
		ErrorResponse(c, "INVALID_PARAMS", "不能为未来日期生成数据", 400)
		return
	}

	oneYearAgo := now.AddDate(-1, 0, 0)
	if targetDate.Before(oneYearAgo) {
		ErrorResponse(c, "INVALID_PARAMS", "不能为超过一年前的日期生成数据", 400)
		return
	}

	generator := scheduler.NewMockDataGenerator(&config.GlobalConfig)
	err = generator.GenerateMockDataForDate(targetDate)
	if err != nil {
		ErrorResponse(c, "GENERATION_ERROR", err.Error(), 500)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data": gin.H{
			"success": true,
			"message": "指定日期模拟数据生成完成",
			"date":    dateStr,
		},
	})
}

// CheckWorkingDay godoc
// @Summary 检查指定日期是否为工作日
// @Description 检查指定日期是否为工作日（排除周末和节假日）
// @Tags 模拟数据
// @Accept json
// @Produce json
// @Param date query string true "目标日期 YYYY-MM-DD" example(2025-06-29)
// @Success 200 {object} map[string]interface{} "日期检查结果"
// @Failure 400 {object} map[string]string "参数错误"
// @Router /mock-data/check-working-day [get]
func CheckWorkingDay(c *gin.Context) {
	// 获取日期参数
	dateStr := c.Query("date")
	if dateStr == "" {
		ErrorResponse(c, "INVALID_PARAMS", "缺少日期参数，格式：YYYY-MM-DD", 400)
		return
	}

	// 解析日期
	targetDate, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "日期格式错误，请使用 YYYY-MM-DD 格式", 400)
		return
	}

	holidayService := services.NewHolidayService()
	isWorkingDay := holidayService.IsWorkingDay(targetDate)
	holidayInfo := holidayService.GetHolidayInfo(targetDate)

	// 获取星期几
	weekday := targetDate.Weekday()
	weekdayStr := map[time.Weekday]string{
		time.Sunday:    "星期日",
		time.Monday:    "星期一",
		time.Tuesday:   "星期二",
		time.Wednesday: "星期三",
		time.Thursday:  "星期四",
		time.Friday:    "星期五",
		time.Saturday:  "星期六",
	}[weekday]

	result := gin.H{
		"date":          dateStr,
		"weekday":       weekdayStr,
		"is_working_day": isWorkingDay,
		"is_weekend":    weekday == time.Saturday || weekday == time.Sunday,
	}

	if holidayInfo != nil {
		result["is_holiday"] = holidayInfo.Holiday
		result["holiday_name"] = holidayInfo.Name
	} else {
		result["is_holiday"] = false
		result["holiday_name"] = ""
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "success",
		"data":    result,
	})
}
