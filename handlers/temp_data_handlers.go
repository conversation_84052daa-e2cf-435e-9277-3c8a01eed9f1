package handlers

import (
	"net/http"

	"difyserver/database"
	"difyserver/view"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CreateTempData godoc
// @Summary 创建临时数据
// @Description 创建一条新的临时数据记录
// @Tags 临时数据
// @Accept json
// @Produce json
// @Param temp_data body view.TempDataCreateRequest true "临时数据内容"
// @Success 200 {object} view.Result{data=view.TempDataCreateResponse} "成功创建临时数据"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /temp-data [post]
func CreateTempData(c *gin.Context) {
	var req view.TempDataCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "参数错误: "+err.Error(), 400)
		return
	}

	// 创建临时数据
	dao := database.GetTempDataDao()
	tempData, err := dao.CreateTempData(req.Content, req.Title)
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "创建失败: "+err.Error(), 500)
		return
	}

	// 构建响应数据
	response := view.TempDataCreateResponse{
		UUID:    tempData.UUID,
		Message: "临时数据创建成功",
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// GetTempDataByUUID godoc
// @Summary 根据UUID获取临时数据
// @Description 根据UUID获取单条临时数据详情
// @Tags 临时数据
// @Accept json
// @Produce json
// @Param uuid path string true "临时数据UUID"
// @Success 200 {object} view.Result{data=view.TempDataResponse} "成功获取临时数据详情"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /temp-data/{uuid} [get]
func GetTempDataByUUID(c *gin.Context) {
	// 获取URL参数中的UUID
	uuidParam := c.Param("uuid")
	if uuidParam == "" {
		ErrorResponse(c, "INVALID_PARAMS", "UUID参数不能为空", 400)
		return
	}

	// 解析UUID
	dataUUID, err := uuid.Parse(uuidParam)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的UUID格式", 400)
		return
	}

	// 查询数据
	dao := database.GetTempDataDao()
	tempData, err := dao.GetByUUID(dataUUID)
	if err != nil {
		if err.Error() == "record not found" {
			ErrorResponse(c, "NOT_FOUND", "临时数据不存在", 404)
			return
		}
		ErrorResponse(c, "DATABASE_ERROR", "查询失败: "+err.Error(), 500)
		return
	}

	// 构建响应数据
	response := view.TempDataResponse{
		UUID:      tempData.UUID,
		Title:     tempData.Title,
		Content:   tempData.Content,
		CreatedAt: tempData.CreatedAt,
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}
