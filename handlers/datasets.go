package handlers

import (
	"math"
	"strconv"

	"difyserver/database"
	"difyserver/models"
	"github.com/gin-gonic/gin"
)

// GetDatasets godoc
// @Summary 获取数据集列表
// @Description 获取系统中的数据集列表，支持分页
// @Tags 数据集管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1"
// @Success 200 {object} view.Result{data=models.PageResponse{data=[]models.Dataset}} "成功获取数据集列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /datasets.json [get]
// @Security ApiKeyAuth
func GetDatasets(c *gin.Context) {
	var datasets []models.Dataset
	var total int64
	pageStr := c.Default<PERSON>uery("page", "1")
	page, _ := strconv.Atoi(pageStr)
	limit := 10
	offset := (page - 1) * limit

	// 先获取总记录数
	database.DB.Model(&models.Dataset{}).Count(&total)

	// 获取分页数据
	result := database.DB.Limit(limit).Offset(offset).Find(&datasets)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := models.PageResponse{
		Data:       datasets,
		Total:      total,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
	}

	SuccessResponse(c, response)
}

// ListDatasetTenant godoc
// @Summary 获取数据集关联的工作空间列表
// @Description 获取特定数据集关联的工作空间列表，支持分页
// @Tags 数据集管理
// @Accept json
// @Produce json
// @Param dataset_id query string true "数据集ID"
// @Param page query int false "页码，默认为1"
// @Success 200 {object} view.Result{data=models.PageResponse} "成功获取数据集关联的工作空间列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /list_dataset_tenant.json [get]
// @Security ApiKeyAuth
func ListDatasetTenant(c *gin.Context) {
	var datasets []models.Dataset
	var total int64
	tenantID := c.Query("tenant_id")
	pageStr := c.DefaultQuery("page", "1")
	page, _ := strconv.Atoi(pageStr)
	limit := 10
	offset := (page - 1) * limit

	// 先获取总记录数
	database.DB.Model(&models.Dataset{}).Count(&total)

	// 获取分页数据
	query := database.DB.Model(&models.Dataset{})
	if tenantID != "" {
		query = query.Where("tenant_id = ?", tenantID)
	}

	result := query.Limit(limit).Offset(offset).Find(&datasets)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := models.PageResponse{
		Data:       datasets,
		Total:      total,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
	}

	SuccessResponse(c, response)
}

// AddDatasetTenant godoc
// @Summary 添加数据集与工作空间的关联
// @Description 创建数据集与工作空间的关联关系
// @Tags 数据集管理
// @Accept json
// @Produce json
// @Param datasetTenant body models.DatasetTenantRequest true "数据集与工作空间关联信息"
// @Success 200 {object} view.Result{data=map[string]string} "成功创建关联"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /add_dataset_tenant.json [post]
// @Security ApiKeyAuth
func AddDatasetTenant(c *gin.Context) {
	var dataset models.Dataset
	if err := c.ShouldBindJSON(&dataset); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	result := database.DB.Model(&models.Dataset{}).Where("id = ?", dataset.ID).Update("tenant_id", dataset.TenantID)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	SuccessResponse(c, gin.H{"message": "关联成功"})
}

// DelDatasetTenant godoc
// @Summary 删除数据集与工作空间的关联
// @Description 删除数据集与工作空间的关联关系
// @Tags 数据集管理
// @Accept json
// @Produce json
// @Param datasetTenant body models.DatasetTenantRequest true "数据集与工作空间关联信息"
// @Success 200 {object} view.Result{data=map[string]string} "成功删除关联"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /del_dataset_tenant.json [post]
// @Security ApiKeyAuth
func DelDatasetTenant(c *gin.Context) {
	var dataset models.Dataset
	if err := c.ShouldBindJSON(&dataset); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	result := database.DB.Model(&models.Dataset{}).Where("id = ? AND tenant_id = ?", dataset.ID, dataset.TenantID).Update("tenant_id", nil)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	SuccessResponse(c, gin.H{"message": "删除关联成功"})
}
