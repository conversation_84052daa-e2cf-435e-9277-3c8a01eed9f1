package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"difyserver/view"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestDelTenantParameterValidation(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	router.POST("/del_tenant.json", DelTenant)

	tests := []struct {
		name           string
		requestBody    view.DelTenantParam
		expectedStatus int
		expectedCode   string
	}{
		{
			name: "空租户ID",
			requestBody: view.DelTenantParam{
				TenantID: "",
			},
			expectedStatus: 200,
			expectedCode:   "INVALID_PARAMS",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 序列化请求体
			jsonData, err := json.Marshal(tt.requestBody)
			assert.NoError(t, err)

			// 创建请求
			req, err := http.NewRequest("POST", "/del_tenant.json", bytes.NewBuffer(jsonData))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			// 创建响应记录器
			w := httptest.NewRecorder()

			// 执行请求
			router.ServeHTTP(w, req)

			// 验证状态码
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 解析响应
			var response view.DifyError
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			// 验证错误代码
			assert.Equal(t, tt.expectedCode, response.Code)
		})
	}
}

func TestDelTenantValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/del_tenant.json", DelTenant)

	// 测试无效的 JSON
	req, err := http.NewRequest("POST", "/del_tenant.json", bytes.NewBuffer([]byte("invalid json")))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response view.DifyError
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "INVALID_PARAMS", response.Code)
}

func TestUpdateTenantNameParameterValidation(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	router.POST("/update_tenant_name.json", UpdateTenantName)

	tests := []struct {
		name           string
		requestBody    view.UpdateTenantNameParam
		expectedStatus int
		expectedCode   string
	}{
		{
			name: "空租户ID",
			requestBody: view.UpdateTenantNameParam{
				TenantID: "",
				Name:     "测试团队",
			},
			expectedStatus: 200,
			expectedCode:   "INVALID_PARAMS",
		},
		{
			name: "空团队名称",
			requestBody: view.UpdateTenantNameParam{
				TenantID: "test-tenant-id",
				Name:     "",
			},
			expectedStatus: 200,
			expectedCode:   "INVALID_PARAMS",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, err := json.Marshal(tt.requestBody)
			assert.NoError(t, err)

			req, err := http.NewRequest("POST", "/update_tenant_name.json", bytes.NewBuffer(jsonData))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response view.DifyError
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedCode, response.Code)
		})
	}
}

func TestUpdateTenantStatusParameterValidation(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	router.POST("/update_tenant_status.json", UpdateTenantStatus)

	tests := []struct {
		name           string
		requestBody    view.UpdateTenantStatusParam
		expectedStatus int
		expectedCode   string
	}{
		{
			name: "空租户ID",
			requestBody: view.UpdateTenantStatusParam{
				TenantID: "",
				Status:   "normal",
			},
			expectedStatus: 200,
			expectedCode:   "INVALID_PARAMS",
		},
		{
			name: "空状态",
			requestBody: view.UpdateTenantStatusParam{
				TenantID: "test-tenant-id",
				Status:   "",
			},
			expectedStatus: 200,
			expectedCode:   "INVALID_PARAMS",
		},
		{
			name: "无效状态",
			requestBody: view.UpdateTenantStatusParam{
				TenantID: "test-tenant-id",
				Status:   "invalid_status",
			},
			expectedStatus: 200,
			expectedCode:   "INVALID_PARAMS",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonData, err := json.Marshal(tt.requestBody)
			assert.NoError(t, err)

			req, err := http.NewRequest("POST", "/update_tenant_status.json", bytes.NewBuffer(jsonData))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response view.DifyError
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedCode, response.Code)
		})
	}
}

func TestListTenantAccountParameterValidation(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	router.GET("/list_tenant_account.json", ListTenantAccount)

	// 测试缺少 tenant_id 参数的情况
	req, err := http.NewRequest("GET", "/list_tenant_account.json", nil)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 由于缺少权限验证，这里主要测试参数处理逻辑
	// 实际的数据库测试需要更复杂的设置
	assert.Equal(t, 200, w.Code)
}
