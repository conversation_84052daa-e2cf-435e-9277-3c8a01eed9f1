package handlers

import (
	"strconv"

	"difyserver/database"
	"difyserver/models"
	"difyserver/utils/define"
	"difyserver/view"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RecordAnchorStatistics godoc
// @Summary 记录锚点统计
// @Description 记录用户对某个资源的操作统计
// @Tags 锚点统计
// @Accept json
// @Produce json
// @Param anchor body view.RecordAnchorRequest true "锚点统计记录"
// @Success 200 {object} view.Result{data=view.RecordAnchorResponse} "成功记录锚点统计"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /anchor-statistics/record.json [post]
// @Security ApiKeyAuth
func RecordAnchorStatistics(c *gin.Context) {
	// 获取当前用户ID和租户ID
	userID := c.GetString(define.UserIDKey)
	tenantID := c.GetString(define.CurrentTenantIDKey)

	if userID == "" || tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 绑定请求参数
	var req view.RecordAnchorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证必填字段
	if req.ResourceType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "资源类型不能为空", 400)
		return
	}
	if req.ResourceID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "资源ID不能为空", 400)
		return
	}
	if req.ActionType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "操作类型不能为空", 400)
		return
	}

	// 解析用户ID和租户ID为UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的用户ID", 400)
		return
	}

	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 记录锚点统计
	if err := database.RecordAnchorStatistics(tenantUUID, userUUID, req.ResourceType, req.ResourceID, req.ActionType); err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	response := view.RecordAnchorResponse{
		Success: true,
		Message: "锚点统计记录成功",
	}

	SuccessResponse(c, response)
}

// GetAnchorStatistics godoc
// @Summary 获取锚点统计列表
// @Description 获取锚点统计数据列表（分页）
// @Tags 锚点统计
// @Accept json
// @Produce json
// @Param resource_type query string false "资源类型"
// @Param resource_id query string false "资源ID"
// @Param action_type query string false "操作类型"
// @Param user_id query string false "用户ID"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} view.Result{data=view.AnchorStatisticsListResponse} "成功获取锚点统计列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /anchor-statistics.json [get]
// @Security ApiKeyAuth
func GetAnchorStatistics(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 绑定查询参数
	var req view.QueryAnchorStatisticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 转换为models层的请求结构
	modelReq := &models.AnchorStatisticsQueryRequest{
		ResourceType: req.ResourceType,
		ResourceID:   req.ResourceID,
		ActionType:   req.ActionType,
		UserID:       req.UserID,
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
		Page:         req.Page,
		PageSize:     req.PageSize,
	}

	// 查询锚点统计数据
	result, err := database.GetAnchorStatistics(modelReq, tenantUUID)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	// 转换为view层的响应结构
	viewResult := &view.AnchorStatisticsListResponse{
		Data:       make([]view.AnchorStatisticsResponse, len(result.Data)),
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}

	for i, item := range result.Data {
		viewResult.Data[i] = view.AnchorStatisticsResponse{
			ID:           item.ID,
			TenantID:     item.TenantID,
			UserID:       item.UserID,
			UserName:     item.UserName,
			ResourceType: item.ResourceType,
			ResourceID:   item.ResourceID,
			ResourceName: item.ResourceName,
			ActionType:   item.ActionType,
			ActionCount:  item.ActionCount,
			RecordDate:   item.RecordDate,
			CreatedAt:    item.CreatedAt,
			UpdatedAt:    item.UpdatedAt,
		}
	}

	SuccessResponse(c, viewResult)
}

// GetAnchorStatisticsAggregate godoc
// @Summary 获取锚点统计聚合数据
// @Description 获取按时间周期聚合的锚点统计数据
// @Tags 锚点统计
// @Accept json
// @Produce json
// @Param resource_type query string false "资源类型"
// @Param resource_id query string false "资源ID"
// @Param action_type query string false "操作类型"
// @Param user_id query string false "用户ID"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Param group_by query string false "聚合维度 (day/month/year)" default(day)
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} view.Result{data=view.AnchorStatisticsAggregateListResponse} "成功获取聚合统计数据"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /anchor-statistics/aggregate.json [get]
// @Security ApiKeyAuth
func GetAnchorStatisticsAggregate(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 绑定查询参数
	var req view.QueryAnchorStatisticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 转换为models层的请求结构
	modelReq := &models.AnchorStatisticsQueryRequest{
		ResourceType: req.ResourceType,
		ResourceID:   req.ResourceID,
		ActionType:   req.ActionType,
		UserID:       req.UserID,
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
		GroupBy:      req.GroupBy,
		Page:         req.Page,
		PageSize:     req.PageSize,
	}

	// 查询聚合统计数据
	result, err := database.GetAnchorStatisticsAggregate(modelReq, tenantUUID)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	// 转换为view层的响应结构
	viewResult := &view.AnchorStatisticsAggregateListResponse{
		Data:       make([]view.AnchorStatisticsAggregateResponse, len(result.Data)),
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}

	for i, item := range result.Data {
		viewResult.Data[i] = view.AnchorStatisticsAggregateResponse{
			Period:       item.Period,
			ResourceType: item.ResourceType,
			ResourceID:   item.ResourceID,
			ResourceName: item.ResourceName,
			ActionType:   item.ActionType,
			TotalCount:   item.TotalCount,
			UserCount:    item.UserCount,
		}
	}

	SuccessResponse(c, viewResult)
}

// GetAnchorStatisticsDetail godoc
// @Summary 获取锚点统计详情
// @Description 根据ID获取单条锚点统计记录详情
// @Tags 锚点统计
// @Accept json
// @Produce json
// @Param id path int true "统计记录ID"
// @Success 200 {object} view.Result{data=view.AnchorStatisticsResponse} "成功获取统计详情"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /anchor-statistics/{id}/info.json [get]
// @Security ApiKeyAuth
func GetAnchorStatisticsDetail(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 获取统计记录ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的统计记录ID", 400)
		return
	}

	// 查询统计记录详情
	result, err := database.GetAnchorStatisticsByID(id, tenantUUID)
	if err != nil {
		ErrorResponse(c, "NOT_FOUND", "统计记录不存在", 404)
		return
	}

	// 转换为view层的响应结构
	viewResult := view.AnchorStatisticsResponse{
		ID:           result.ID,
		TenantID:     result.TenantID,
		UserID:       result.UserID,
		UserName:     result.UserName,
		ResourceType: result.ResourceType,
		ResourceID:   result.ResourceID,
		ResourceName: result.ResourceName,
		ActionType:   result.ActionType,
		ActionCount:  result.ActionCount,
		RecordDate:   result.RecordDate,
		CreatedAt:    result.CreatedAt,
		UpdatedAt:    result.UpdatedAt,
	}

	SuccessResponse(c, viewResult)
}

// GetAnchorStatisticsAnalysis godoc
// @Summary 获取锚点统计分析数据
// @Description 获取过去N天某个资源类型和操作类型的操作次数，按指定步长汇总
// @Tags 锚点统计
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型"
// @Param resource_id query string false "资源ID（可选，不指定则统计该类型下所有资源）"
// @Param action_type query string true "操作类型"
// @Param days query int true "过去天数 (1-365)"
// @Param step query int true "汇总步长天数 (1-30)"
// @Success 200 {object} view.Result{data=view.AnalysisAnchorStatisticsListResponse} "成功获取分析数据"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /anchor-statistics/analysis.json [get]
// @Security ApiKeyAuth
func GetAnchorStatisticsAnalysis(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 绑定查询参数
	var req view.AnalysisAnchorStatisticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证参数
	if req.ResourceType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "资源类型不能为空", 400)
		return
	}
	if req.ActionType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "操作类型不能为空", 400)
		return
	}
	if req.Days < 1 || req.Days > 365 {
		ErrorResponse(c, "INVALID_PARAMS", "天数必须在1-365之间", 400)
		return
	}
	if req.Step < 1 || req.Step > 30 {
		ErrorResponse(c, "INVALID_PARAMS", "步长必须在1-30之间", 400)
		return
	}

	// 转换为models层的请求结构
	modelReq := &models.AnchorStatisticsAnalysisRequest{
		ResourceType: req.ResourceType,
		ResourceID:   req.ResourceID,
		ActionType:   req.ActionType,
		Days:         req.Days,
		Step:         req.Step,
	}

	// 查询分析统计数据
	result, err := database.GetAnchorStatisticsAnalysis(modelReq, tenantUUID)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	// 转换为view层的响应结构
	viewResult := &view.AnalysisAnchorStatisticsListResponse{
		Data:         make([]view.AnalysisAnchorStatisticsResponse, len(result.Data)),
		ResourceType: result.ResourceType,
		ActionType:   result.ActionType,
		TotalDays:    result.TotalDays,
		StepDays:     result.StepDays,
		Summary: view.AnalysisAnchorStatisticsSummary{
			TotalCount:       result.Summary.TotalCount,
			TotalUserCount:   result.Summary.TotalUserCount,
			AveragePerPeriod: result.Summary.AveragePerPeriod,
			MaxPeriodCount:   result.Summary.MaxPeriodCount,
			MinPeriodCount:   result.Summary.MinPeriodCount,
		},
	}

	for i, item := range result.Data {
		viewResult.Data[i] = view.AnalysisAnchorStatisticsResponse{
			StartDate:    item.StartDate,
			EndDate:      item.EndDate,
			TotalCount:   item.TotalCount,
			UserCount:    item.UserCount,
			ResourceType: item.ResourceType,
			ActionType:   item.ActionType,
		}
	}

	SuccessResponse(c, viewResult)
}

// GetAnchorStatisticsResourceSummary godoc
// @Summary 获取资源汇总统计数据
// @Description 获取指定资源类型和操作类型下所有资源的统计汇总，以resource_id为key返回
// @Tags 锚点统计
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型"
// @Param action_type query string true "操作类型"
// @Success 200 {object} view.Result{data=view.ResourceSummaryAnchorStatisticsResponse} "成功获取资源汇总数据"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /anchor-statistics/resource-summary.json [get]
// @Security ApiKeyAuth
func GetAnchorStatisticsResourceSummary(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 绑定查询参数
	var req view.ResourceSummaryAnchorStatisticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证参数
	if req.ResourceType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "资源类型不能为空", 400)
		return
	}
	if req.ActionType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "操作类型不能为空", 400)
		return
	}

	// 转换为models层的请求结构
	modelReq := &models.AnchorStatisticsResourceSummaryRequest{
		ResourceType: req.ResourceType,
		ActionType:   req.ActionType,
	}

	// 查询资源汇总统计数据
	result, err := database.GetAnchorStatisticsResourceSummary(modelReq, tenantUUID)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	// 转换为view层的响应结构
	viewResult := make(view.ResourceSummaryAnchorStatisticsResponse)
	for resourceID, item := range result {
		viewResult[resourceID] = view.ResourceSummaryAnchorStatisticsItem{
			ResourceType: item.ResourceType,
			ResourceID:   item.ResourceID,
			ResourceName: item.ResourceName,
			ActionType:   item.ActionType,
			TotalCount:   item.TotalCount,
			UserCount:    item.UserCount,
		}
	}

	SuccessResponse(c, viewResult)
}
