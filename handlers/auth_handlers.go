package handlers

import (
	"bytes"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"

	"difyserver/database"
	"difyserver/models"
	"difyserver/utils"
	"difyserver/utils/define"
	"difyserver/view"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/pbkdf2"
)

// Login godoc
// @Summary 用户登录
// @Description 管理员用户登录系统
// @Tags 认证
// @Accept json
// @Produce json
// @Param credentials body models.LoginRequest true "登录凭证"
// @Success 200 {object} view.Result "登录成功，返回用户信息和令牌"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /login.json [post]
// Deprecated
func Login(c *gin.Context) {
	var req struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证参数
	if req.Email == "" || req.Password == "" {
		ErrorResponse(c, "INVALID_PARAMS", "邮箱和密码不能为空", 400)
		return
	}

	// 查找用户
	var account models.Account
	if err := database.DB.Where("email = ?", req.Email).First(&account).Error; err != nil {
		ErrorResponse(c, "UNAUTHORIZED", "用户不存在或密码错误", 401)
		return
	}

	// 验证密码
	if account.Password == "" || account.PasswordSalt == "" {
		ErrorResponse(c, "UNAUTHORIZED", "用户未设置密码", 401)
		return
	}

	// 解码存储的密码和盐值
	storedPasswordBytes, err := base64.StdEncoding.DecodeString(account.Password)
	if err != nil {
		ErrorResponse(c, "INTERNAL_ERROR", "密码解码失败", 500)
		return
	}

	saltBytes, err := base64.StdEncoding.DecodeString(account.PasswordSalt)
	if err != nil {
		ErrorResponse(c, "INTERNAL_ERROR", "密码盐解码失败", 500)
		return
	}

	// 使用相同的盐值加密输入的密码
	inputPasswordHashed := hashPassword(req.Password, saltBytes)

	// 比较密码
	if !bytes.Equal([]byte(inputPasswordHashed), storedPasswordBytes) {
		ErrorResponse(c, "UNAUTHORIZED", "用户不存在或密码错误", 401)
		return
	}

	client := utils.GetDifyClient()
	requestBody := view.AuthLoginParam{
		Email:      req.Email,
		Password:   req.Password,
		Language:   "zh-Hans",
		RememberMe: true,
	}
	responseData := new(view.DifyResult[view.AuthLoginResult])
	err = client.PostJSON(c, "/console/api/login", requestBody, nil, nil, responseData)
	if err != nil {
		slog.ErrorContext(c, "Failed to login", "error", err)
		difyErr := view.UnWrapError(err)
		ErrorResponse(c, difyErr.Code, difyErr.Message, difyErr.Status)
		return
	}

	// 返回用户信息和 token
	account.Password = ""     // 清除敏感信息
	account.PasswordSalt = "" // 清除敏感信息
	SuccessResponse(c, gin.H{
		"message":       "登录成功",
		"data":          account,
		"token":         responseData.Data.AccessToken,
		"refresh_token": responseData.Data.RefreshToken,
	})
}

// RefreshToken godoc
// @Summary 刷新用户令牌
// @Description 使用 refresh_token 获取新的 access_token
// @Tags 认证
// @Accept json
// @Produce json
// @Param refresh_token body view.AuthRefreshTokenParam true "刷新令牌"
// @Success 200 {object} view.Result "返回新的访问令牌和刷新令牌"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /refresh_token.json [post]
// @Security ApiKeyAuth
// Deprecated
func RefreshToken(c *gin.Context) {
	client := utils.GetDifyClient()
	var req view.AuthRefreshTokenParam
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}
	responseData := new(view.DifyResult[view.AuthLoginResult])
	if err := client.PostJSON(c, "/console/api/refresh-token", req, nil, nil, responseData); err != nil {
		difyErr := view.UnWrapError(err)
		ErrorResponse(c, difyErr.Code, difyErr.Message, difyErr.Status)
		return
	}
	SuccessResponse(c, responseData.Data)
}

// Logout godoc
// @Summary 用户登出
// @Description 用户退出登录状态
// @Tags 认证
// @Accept json
// @Produce json
// @Success 200 {object} view.Result[string] "登出成功"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /logout.json [post]
// @Security ApiKeyAuth
// Deprecated
func Logout(c *gin.Context) {
	token, _ := c.Get(define.CurrentToken)
	client := utils.GetDifyClientWithToken(token.(string))
	if _, err := client.Get(c, "/console/api/logout", nil, nil); err != nil {
		difyErr := view.UnWrapError(err)
		ErrorResponse(c, difyErr.Code, difyErr.Message, difyErr.Status)
		return
	}
	SuccessResponse(c, "logout success")
}

// hashPassword 使用 PBKDF2 算法和盐值加密密码
func hashPassword(password string, salt []byte) string {
	// 生成 PBKDF2 密钥
	dk := pbkdf2.Key([]byte(password), salt, 10000, 32, sha256.New)
	// 转换为十六进制字符串
	return hex.EncodeToString(dk)
}

// GetFileURL godoc
// @Summary 获取文件 URL
// @Description 获取文件 URL
// @Tags 文件
// @Accept json
// @Produce json
// @Param fileID query string true "文件 ID"
// @Success 200 {object} view.Result{data=string} "成功获取文件 URL"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /file-url.json [get]
func GetFileURL(c *gin.Context) {
	fileID := c.Query("fileID")
	if fileID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "fileID 参数必填", 400)
		return
	}
	url, err := utils.GetSignedFileURL(fileID)
	if err != nil {
		ErrorResponse(c, "INTERNAL_ERROR", "获取文件 URL 失败", 500)
		return
	}
	SuccessResponse(c, url)
}

// GetFile godoc
// @Summary 获取文件
// @Description 获取文件
// @Tags 文件
// @Accept json
// @Produce json
// @Param fileID path string true "文件 ID"
// @Success 200 {object} view.Result{data=string} "成功获取文件 URL"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /file/{fileID} [get]
func GetFile(c *gin.Context) {
	fileID := c.Param("fileID")
	if fileID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "fileID 参数必填", 400)
		return
	}
	uploadFile := new(models.UploadFiles)
	if result := database.DB.Where("id=?", fileID).First(uploadFile); result.Error != nil {
		ErrorResponse(c, "INVALID_PARAMS", "未找到文件", 500)
		return
	}
	filePath := fmt.Sprintf("/app/api/storage/" + uploadFile.Key)
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		slog.ErrorContext(c, "File not found", "filePath", filePath)
		ErrorResponse(c, "FILE_NOT_FOUND", "本地文件不存在", 404)
		return
	}

	// 设置文件名（可选，使用原始文件名）
	fileName := uploadFile.Name
	if fileName == "" {
		fileName = filepath.Base(filePath)
	}
	ext := strings.ToLower(filepath.Ext(fileName))
	var contentType string

	switch ext {
	case ".pdf":
		contentType = "application/pdf"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".png":
		contentType = "image/png"
	case ".gif":
		contentType = "image/gif"
	case ".txt":
		contentType = "text/plain"
	case ".html", ".htm":
		contentType = "text/html"
	case ".doc", ".docx":
		contentType = "application/msword"
	case ".xls", ".xlsx":
		contentType = "application/vnd.ms-excel"
	case ".mp4":
		contentType = "video/mp4"
	default:
		// 对于未知类型，使用通用二进制类型，这会触发下载
		contentType = "application/octet-stream"
	}
	// 设置下载头信息
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", contentType)

	// 发送文件
	c.File(filePath)
}

// GetFileFromLocal godoc
// @Summary 获取服务器上传文件
// @Description 获取服务器上传文件
// @Tags 文件
// @Accept json
// @Produce json
// @Param file query string true "文件地址"
// @Success 200 {object} view.Result{data=string} "成功获取文件 URL"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /file [get]
func GetFileFromLocal(c *gin.Context) {
	file := c.Query("file")
	file = strings.TrimPrefix(file, "/tmp")
	filePath := fmt.Sprintf("/app/api/storage/" + file)
	fileName := c.Query("fileName")

	// 检查文件是否存在
	if fileINfo, err := os.Stat(filePath); os.IsNotExist(err) {
		slog.ErrorContext(c, "File not found", "filePath", filePath)
		c.JSON(404, view.DifyError{
			Code:    "FILE_NOT_FOUND",
			Message: "本地文件不存在",
			Status:  404,
		})
		return
	} else {
		if fileName == "" {
			fileName = fileINfo.Name()
		}
	}

	// 设置文件名（可选，使用原始文件名）
	if fileName == "" {
		fileName = filepath.Base(filePath)
	}
	ext := strings.ToLower(filepath.Ext(fileName))
	var contentType string

	switch ext {
	case ".pdf":
		contentType = "application/pdf"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".png":
		contentType = "image/png"
	case ".gif":
		contentType = "image/gif"
	case ".txt":
		contentType = "text/plain"
	case ".html", ".htm":
		contentType = "text/html"
	case ".doc", ".docx":
		contentType = "application/msword"
	case ".xls", ".xlsx":
		contentType = "application/vnd.ms-excel"
	case ".mp4":
		contentType = "video/mp4"
	default:
		// 对于未知类型，使用通用二进制类型，这会触发下载
		contentType = "application/octet-stream"
	}
	// 设置下载头信息
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", contentType)

	// 发送文件
	c.File(filePath)
}

// GetCurrentTenant godoc
// @Summary 获取当前团队信息
// @Description 获取当前团队信息
// @Tags 团队管理,认证
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=view.CurrentTenant} "成功获取当前团队信息"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /current_tenant.json [get]
// @Security ApiKeyAuth
func GetCurrentTenant(c *gin.Context) {
	value, exists := c.Get(define.CurrentTenantKey)
	if !exists {
		ErrorResponse(c, "MISSING_TENANT", "missing tenant", 500)
		return
	}
	tenant := value.(*view.CurrentTenant)
	SuccessResponse(c, tenant)
}

// SwitchTenant godoc
// @Summary 切换团队
// @Description 切换团队
// @Tags 团队管理,认证
// @Accept json
// @Produce json
// @Param tenantID body view.TenantAccountParam true "团队ID"
// @Success 200 {object} view.Result{data=view.CurrentTenant} "成功切换团队"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /switch_tenant.json [post]
// @Security ApiKeyAuth
func SwitchTenant(c *gin.Context) {
	var req view.TenantAccountParam
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}
	if req.TenantID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "tenant_id不能为空", 400)
		return
	}
	client := utils.GetDifyClientWithToken(c.GetString(define.CurrentToken))
	result := new(view.SwitchTenantResult)
	if err := client.PostJSON(c, "/console/api/workspaces/switch", req, nil, nil, result); err != nil {
		difyErr := view.UnWrapError(err)
		ErrorResponse(c, difyErr.Code, difyErr.Message, difyErr.Status)
		return
	}
	SuccessResponse(c, result.NewTenant)
}

// SuccessResponse 返回统一格式的成功响应
// data 为返回的数据内容
func SuccessResponse(c *gin.Context, data any) {
	c.JSON(200, view.Result{
		Result: "success",
		Data:   data,
	})
}

// ErrorResponse 返回统一格式的错误响应
// code 为错误代码，message 为错误信息，status 为内部状态码（非 HTTP 状态码）
func ErrorResponse(c *gin.Context, code string, message string, status int) {
	c.JSON(200, view.DifyError{
		Code:    code,
		Message: message,
		Status:  status,
	})
}
