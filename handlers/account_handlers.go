package handlers

import (
	"encoding/base64"
	"fmt"
	"log/slog"
	"math"
	"strconv"
	"time"

	"difyserver/database"
	"difyserver/models"
	"difyserver/view"
	"github.com/samber/lo"

	"crypto/rand"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetAccounts godoc
// @Summary 获取账户列表
// @Description 获取系统中的账户列表，支持分页和按姓名、邮箱过滤
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1"
// @Param size query int false "每页长度"
// @Param name query string false "按姓名过滤（模糊匹配）"
// @Param email query string false "按邮箱过滤（模糊匹配）"
// @Success 200 {object} view.Result{data=models.PageResponse{data=[]view.AccountView}} "成功获取账户列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /accounts.json [get]
// @Security ApiKeyAuth
func GetAccounts(c *gin.Context) {
	var accounts []models.Account
	var total int64
	pageStr := c.DefaultQuery("page", "1")
	page, _ := strconv.Atoi(pageStr)
	sizeStr := c.DefaultQuery("size", "10")
	limit, _ := strconv.Atoi(sizeStr)
	offset := (page - 1) * limit

	// 获取过滤参数
	nameFilter := c.Query("name")
	emailFilter := c.Query("email")

	// 构建查询条件
	query := database.DB.Model(&models.Account{})
	if nameFilter != "" {
		query = query.Where("name LIKE ?", "%"+nameFilter+"%")
	}
	if emailFilter != "" {
		query = query.Where("email LIKE ?", "%"+emailFilter+"%")
	}

	// 先获取总记录数
	query.Count(&total)

	// 获取分页数据
	result := query.Limit(limit).Offset(offset).Find(&accounts)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	tenant := CurrentTenant(c)
	ajs := make([]models.TenantAccountJoin, 0)
	if result := database.DB.Model(&models.TenantAccountJoin{}).Where("tenant_id=?", tenant.Id).Scan(&ajs); result.Error != nil {
		slog.ErrorContext(c, "DB_ERROR", "获取团队成员失败", 500)
		ErrorResponse(c, "DB_ERROR", "获取团队成员失败", 500)
		return
	}
	tenantAccountMap := lo.SliceToMap(ajs, func(item models.TenantAccountJoin) (string, models.TenantAccountJoin) {
		return item.AccountID, item
	})

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	views := lo.Map(accounts, func(item models.Account, index int) view.AccountView {
		// 处理每个账户的逻辑
		return view.AccountView{
			Account:              item,
			FirstTenantID:        tenant.Id,
			FirstTenantRole:      tenantAccountMap[item.ID].Role,
			FirstTenantAccountID: tenantAccountMap[item.ID].ID,
		}
	})

	response := models.PageResponse{
		Data:       views,
		Total:      total,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
	}

	SuccessResponse(c, response)
}

// AddAccount godoc
// @Summary 添加新账户
// @Description 创建一个新的系统账户
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param account body view.CreateAccountParam true "账户信息"
// @Success 200 {object} view.Result{data=models.Account} "成功创建的账户信息"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /add_account.json [post]
// @Security ApiKeyAuth
func AddAccount(c *gin.Context) {
	req := new(view.CreateAccountParam)
	if err := c.ShouldBindJSON(req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	accounts := []models.Account{}
	if result := database.DB.Where("email = ?", req.Email).Find(&accounts); result.Error != nil || len(accounts) > 0 {
		ErrorResponse(c, "DB_ERROR", "用户邮箱已存在", 500)
		return
	}

	if req.TenantID != "" {
		tenant, err := database.GetTenantDao().GetByID(req.TenantID)
		if err != nil || tenant == nil {
			ErrorResponse(c, "DB_ERROR", "未找到指定团队", 500)
			return
		}
	}

	account := models.Account{
		ID:    uuid.New().String(),
		Name:  req.Name,
		Email: req.Email,
		//Avatar:            "********-eb21-49b2-a83c-26303f7c11b2",
		InterfaceLanguage: "zh-Hans",
		InterfaceTheme:    "light",
		Timezone:          "Asia/Shanghai",
		Status:            models.ACTIVE,
		Unit:              req.Unit,
		Department:        req.Department,
	}

	if req.Password != "" {
		// 验证新密码长度
		if len(req.Password) < 6 {
			ErrorResponse(c, "INVALID_PARAMS", "密码长度至少6位", 400)
			return
		}

		// 生成新的盐值
		salt := make([]byte, 16)
		if _, err := rand.Read(salt); err != nil {
			ErrorResponse(c, "INTERNAL_ERROR", "生成密码盐失败", 500)
			return
		}

		// 使用新盐值加密新密码
		byteArray := hashPassword(req.Password, salt)
		// 将密码和盐值转换为 base64 编码
		hashedPassword := []byte(byteArray)

		base64Password := base64.StdEncoding.EncodeToString(hashedPassword)
		base64Salt := base64.StdEncoding.EncodeToString(salt)
		account.Password = base64Password
		account.PasswordSalt = base64Salt
	}
	tx := database.DB.Begin()
	if result := tx.Create(&account); result.Error != nil {
		tx.Rollback()
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	join := models.TenantAccountJoin{
		ID:        uuid.New().String(),
		TenantID:  req.TenantID,
		AccountID: account.ID,
		Role:      view.TenantAccountRoleNormal,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if result := tx.Create(&join); result.Error != nil {
		tx.Rollback()
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		ErrorResponse(c, "DB_ERROR", "删除用户失败", 500)
		return
	}
	SuccessResponse(c, account)
}

// DelAccount godoc
// @Summary 删除账户
// @Description 删除指定账户及其相关关联
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param accountInfo body models.AccountIDRequest true "账户ID信息"
// @Success 200 {object} view.Result{data=map[string]string} "删除成功"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /del_account.json [post]
// @Security ApiKeyAuth
func DelAccount(c *gin.Context) {
	var req struct {
		ID string `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	if req.ID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "用户ID不能为空", 400)
		return
	}
	ac, err := database.GetAccountDao().GetByID(req.ID)
	if err != nil || ac == nil {
		ErrorResponse(c, "NOT_FOUND", "未找到指定用户", 404)
		return
	}

	// 开启事务
	tx := database.DB.Begin()

	// 先删除关联关系
	if err := tx.Where("account_id = ?", req.ID).Delete(&models.TenantAccountJoin{}).Error; err != nil {
		tx.Rollback()
		ErrorResponse(c, "DB_ERROR", "删除用户关联关系失败", 500)
		return
	}

	// 再删除用户
	if err := tx.Where("id = ?", req.ID).Delete(&models.Account{}).Error; err != nil {
		tx.Rollback()
		ErrorResponse(c, "DB_ERROR", "删除用户失败", 500)
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		ErrorResponse(c, "DB_ERROR", "删除用户失败", 500)
		return
	}

	SuccessResponse(c, gin.H{"message": "删除用户成功"})
}

// SetAccountPassword godoc
// @Summary 设置账户密码
// @Description 为指定账户设置新密码
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param request body models.SetPasswordRequest true "设置密码请求"
// @Success 200 {object} view.Result{data=map[string]string} "设置密码成功"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /set_account_password.json [post]
// @Security ApiKeyAuth
func SetAccountPassword(c *gin.Context) {
	var req struct {
		ID          string `json:"id"`
		NewPassword string `json:"password"` // 新密码
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证参数
	if req.ID == "" || req.NewPassword == "" {
		ErrorResponse(c, "INVALID_PARAMS", "用户ID和新密码不能为空", 400)
		return
	}

	// 验证新密码长度
	if len(req.NewPassword) < 6 {
		ErrorResponse(c, "INVALID_PARAMS", "密码长度至少6位", 400)
		return
	}

	// 查找用户
	var account models.Account
	if err := database.DB.Where("id = ?", req.ID).First(&account).Error; err != nil {
		ErrorResponse(c, "NOT_FOUND", "未找到指定用户", 404)
		return
	}

	// 生成新的盐值
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		ErrorResponse(c, "INTERNAL_ERROR", "生成密码盐失败", 500)
		return
	}

	// 使用新盐值加密新密码
	byteArray := hashPassword(req.NewPassword, salt)

	// 将密码和盐值转换为 base64 编码
	hashedPassword := []byte(byteArray)
	fmt.Println(byteArray) // 输出: [104 101 108 108 111]

	base64Password := base64.StdEncoding.EncodeToString(hashedPassword)
	base64Salt := base64.StdEncoding.EncodeToString(salt)

	// 更新用户密码和盐值
	result := database.DB.Model(&account).Updates(map[string]interface{}{
		"password":      base64Password,
		"password_salt": base64Salt,
	})

	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", "设置密码失败", 500)
		return
	}

	SuccessResponse(c, gin.H{"message": "设置密码成功"})
}

// UpdateAccountInfo godoc
// @Summary 修改账户信息
// @Description 管理员修改指定账户的姓名、部门和单位信息
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param request body view.UpdateAccountInfoParam true "修改账户信息请求"
// @Success 200 {object} view.Result{data=models.Account} "修改成功"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /update_account_info.json [post]
// @Security ApiKeyAuth
func UpdateAccountInfo(c *gin.Context) {
	var req view.UpdateAccountInfoParam
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证参数
	if req.ID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "用户ID不能为空", 400)
		return
	}

	if req.Name == "" {
		ErrorResponse(c, "INVALID_PARAMS", "用户姓名不能为空", 400)
		return
	}

	// 查找用户
	var account models.Account
	if err := database.DB.Where("id = ?", req.ID).First(&account).Error; err != nil {
		ErrorResponse(c, "NOT_FOUND", "未找到指定用户", 404)
		return
	}

	// 更新用户信息
	updateData := map[string]interface{}{
		"name":       req.Name,
		"unit":       req.Unit,
		"department": req.Department,
	}

	result := database.DB.Model(&account).Updates(updateData)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", "修改用户信息失败", 500)
		return
	}

	// 重新查询更新后的用户信息
	if err := database.DB.Where("id = ?", req.ID).First(&account).Error; err != nil {
		ErrorResponse(c, "DB_ERROR", "获取更新后的用户信息失败", 500)
		return
	}

	SuccessResponse(c, account)
}
