package handlers

import (
	"log/slog"

	"difyserver/database"
	"difyserver/models"

	"github.com/gin-gonic/gin"
)

// GetDashboardSummary godoc
// @Summary 获取Dashboard总览数据
// @Description 获取Dashboard总览数据，包括总量统计、按日期统计的数据和每个app的点击数及对话消息数统计（全局统计，不按租户隔离）
// @Tags 首页Dashboard
// @Accept json
// @Produce json
// @Param stat_type query string false "统计类型：daily(按日) 或 monthly(按月)" default(daily)
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Success 200 {object} models.DashboardSummary "成功获取Dashboard数据"
// @Failure 400 {object} map[string]string "参数错误"
// @Failure 500 {object} map[string]string "服务器错误"
// @Router /dashboard/summary [get]
// @Security ApiKeyAuth
func GetDashboardSummary(c *gin.Context) {
	// 解析查询参数
	req := &models.DashboardQueryRequest{}
	if err := c.ShouldBindQuery(req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 设置默认值
	if req.StatType == "" {
		req.StatType = "daily"
	}

	// 验证统计类型
	if req.StatType != "daily" && req.StatType != "monthly" {
		ErrorResponse(c, "INVALID_PARAMS", "stat_type必须是daily或monthly", 400)
		return
	}

	// 获取Dashboard数据（全局统计）
	dashboardDao := database.GetDashboardDao()
	summary, err := dashboardDao.GetDashboardSummary(req)
	if err != nil {
		slog.ErrorContext(c, "DB_ERROR", "获取Dashboard数据失败", err)
		ErrorResponse(c, "DB_ERROR", "获取Dashboard数据失败", 500)
		return
	}

	SuccessResponse(c, summary)
}
