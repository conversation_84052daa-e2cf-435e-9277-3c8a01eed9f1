package handlers

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"difyserver/config"
	"difyserver/database"
	"difyserver/models"
	"difyserver/utils"
	"difyserver/utils/define"
	"difyserver/view"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// QuizAuthRequired 答题系统认证中间件
func QuizAuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(401, gin.H{
				"result":  "error",
				"message": "未提供认证token",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.<PERSON>(401, gin.H{
				"result":  "error",
				"message": "认证token格式错误",
			})
			c.Abort()
			return
		}

		token := parts[1]

		// 使用现有的token解析逻辑
		claims, err := utils.ParseToken(token)
		if err != nil {
			c.JSON(401, gin.H{
				"result":  "error",
				"message": "认证token无效",
			})
			c.Abort()
			return
		}

		// 验证用户是否存在且状态正常
		account := new(models.Account)
		if result := database.DB.Where("id = ?", claims.ID).First(&account); result.Error != nil || account.Status != models.ACTIVE {
			c.JSON(401, gin.H{
				"result":  "error",
				"message": "用户不存在或已被禁用",
			})
			c.Abort()
			return
		}

		// 设置用户信息到上下文
		c.Set(define.UserIDKey, claims.ID)
		c.Set(define.UserEmailKey, account.Email)
		c.Set(define.UserAttrKey, *account) // 设置完整的用户信息

		// 检查是否为超管（从配置文件中的管理员列表判断）
		isAdmin := false
		for _, adminEmail := range config.GlobalConfig.Admins {
			if adminEmail == account.Email {
				isAdmin = true
				break
			}
		}
		c.Set(define.IsAdminKey, isAdmin)

		c.Next()
	}
}

// QuizAdminRequired 答题系统超管权限中间件
func QuizAdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否为超管
		isAdmin, exists := c.Get(define.IsAdminKey)
		if !exists || !isAdmin.(bool) {
			c.JSON(403, gin.H{
				"result":  "error",
				"message": "需要超级管理员权限",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// Question 题目数据结构
type Question struct {
	QuestionID    string   `json:"question_id"`
	QuestionType  string   `json:"question_type"`
	Question      string   `json:"question"`
	Options       []string `json:"options,omitempty"`
	CorrectAnswer string   `json:"correct_answer"`
	Explanation   string   `json:"explanation"`
}

// ShowQuizPage 显示答题系统页面
// @Summary 显示答题系统页面
// @Description 渲染答题系统的HTML模板页面
// @Tags Quiz
// @Param uuid path string true "临时数据UUID"
// @Accept html
// @Produce html
// @Success 200 {string} string "HTML页面"
// @Router /quiz/{uuid} [get]
func ShowQuizPage(c *gin.Context) {

	uuidParam := c.Param("uuid")
	if uuidParam == "" {
		ErrorResponse(c, "INVALID_PARAMS", "UUID参数不能为空", 400)
		return
	}

	// 解析UUID
	dataUUID, err := uuid.Parse(uuidParam)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的UUID格式", 400)
		return
	}

	// 查询数据
	dao := database.GetTempDataDao()
	tempData, err := dao.GetByUUID(dataUUID)
	if err != nil {
		if err.Error() == "record not found" {
			ErrorResponse(c, "NOT_FOUND", "临时数据不存在", 404)
			return
		}
		ErrorResponse(c, "DATABASE_ERROR", "查询失败: "+err.Error(), 500)
		return
	}
	// 获取题目数据
	questionsJson := tempData.Content
	if strings.Contains(questionsJson, "```json") {
		startIndex := strings.Index(questionsJson, "```json")
		endIndex := strings.LastIndex(questionsJson, "```")

		if startIndex != -1 && endIndex != -1 && endIndex > startIndex {
			// 提取代码块内容
			questionsJson = questionsJson[startIndex:endIndex]
			// 去掉```json标记
			questionsJson = strings.ReplaceAll(questionsJson, "```json", "")
		}
	}
	title := tempData.Title
	if strings.Contains(title, ".") {
		title = title[:strings.LastIndex(title, ".")]
		parts := strings.Split(title, ".")
		if len(parts) > 1 {
			title = parts[1]
		} else {
			title = parts[0]
		}
	}
	m := new([]map[string]any)
	if err := json.Unmarshal([]byte(questionsJson), m); err != nil {
		slog.Error("json解析失败", "error", err)
		questionsJson = strings.ReplaceAll(questionsJson, "\\n", "")
		questionsJson = strings.ReplaceAll(questionsJson, "\\\"", "\"")
		questionsJson = " [ " + questionsJson + " ] "
	}
	if err := json.Unmarshal([]byte(questionsJson), m); err != nil {
		slog.Error("json解析失败", "error", err)
		ErrorResponse(c, "JSON_PARSE_ERROR", "题目数据解析失败: "+err.Error(), 500)
		return
	}

	// 渲染模板，传递动态数据
	c.HTML(http.StatusOK, "index.tmpl", gin.H{
		"title":         title,
		"questionsJSON": string(questionsJson),
	})
}

// SubmitQuizAnswers 提交答题答案
// @Summary 提交答题答案
// @Description 提交用户的答题答案并返回评分结果
// @Tags Quiz
// @Param uuid path string true "题目UUID"
// @Param submission body view.QuizSubmissionRequest true "答题提交数据"
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=view.QuizSubmissionResponse} "答题结果"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 404 {object} view.DifyError "题目不存在"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/{uuid}/submit [post]
func SubmitQuizAnswers(c *gin.Context) {
	uuidParam := c.Param("uuid")
	if uuidParam == "" {
		ErrorResponse(c, "INVALID_PARAMS", "UUID参数不能为空", 400)
		return
	}

	// 解析UUID
	quizUUID, err := uuid.Parse(uuidParam)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的UUID格式", 400)
		return
	}

	// 解析请求体
	var req view.QuizSubmissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "参数错误: "+err.Error(), 400)
		return
	}

	// 获取题目数据
	tempDataDao := database.GetTempDataDao()
	tempData, err := tempDataDao.GetByUUID(quizUUID)
	if err != nil {
		if err.Error() == "record not found" {
			ErrorResponse(c, "NOT_FOUND", "题目不存在", 404)
			return
		}
		ErrorResponse(c, "DATABASE_ERROR", "查询题目失败: "+err.Error(), 500)
		return
	}

	// 解析题目数据
	questions, err := parseQuizQuestions(tempData.Content)
	if err != nil {
		ErrorResponse(c, "JSON_PARSE_ERROR", "题目数据解析失败: "+err.Error(), 500)
		return
	}

	// 计算得分和详细结果
	correctAnswers, detailedResults := calculateScore(questions, req.Answers)
	totalQuestions := len(questions)
	// 按100分制计算得分
	score := int(float64(correctAnswers) / float64(totalQuestions) * 100)
	percentage := float64(correctAnswers) / float64(totalQuestions) * 100

	// 获取用户信息（使用现有的key定义）
	userID := c.GetString(define.UserIDKey)
	userAttr, _ := c.Get(define.UserAttrKey)

	var userUUID *uuid.UUID
	if userID != "" {
		if parsedUUID, err := uuid.Parse(userID); err == nil {
			userUUID = &parsedUUID
		}
	}

	// 生成会话ID
	sessionID := generateSessionIDFromUser(c, req.SessionInfo, userAttr)

	// 创建提交记录
	submission := &models.QuizSubmission{
		QuizUUID:       quizUUID,
		QuizTitle:      tempData.Title,
		UserID:         userUUID,
		SessionID:      sessionID,
		UserAnswers:    models.JSON(convertAnswersToInterface(req.Answers)),
		Score:          score,
		TotalQuestions: totalQuestions,
		CorrectAnswers: correctAnswers,
		Percentage:     math.Round(percentage*100) / 100,
		UserInfo:       models.JSON(mergeUserInfo(req.SessionInfo, userAttr)),
		SubmittedAt:    time.Now().UTC(), // 存储UTC时间到数据库
	}

	// 保存到数据库
	quizDao := database.GetQuizDao()
	if err := quizDao.SubmitQuiz(submission); err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "保存答题记录失败: "+err.Error(), 500)
		return
	}

	// 生成分析报告
	analysis := generateAnalysis(score, totalQuestions, percentage)

	// 构建响应（包含详细结果用于显示解析）
	response := view.QuizSubmissionResponse{
		SubmissionID:    submission.ID,
		Score:           score,
		TotalQuestions:  totalQuestions,
		CorrectAnswers:  correctAnswers,
		Percentage:      submission.Percentage,
		DetailedResults: detailedResults,
		Analysis:        analysis,
		SubmittedAt:     submission.SubmittedAt,
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// parseQuizQuestions 解析题目数据
func parseQuizQuestions(content string) ([]Question, error) {
	// 处理可能包含markdown代码块的内容
	questionsJson := content
	if strings.Contains(questionsJson, "```json") {
		startIndex := strings.Index(questionsJson, "```json")
		endIndex := strings.LastIndex(questionsJson, "```")

		if startIndex != -1 && endIndex != -1 && endIndex > startIndex {
			questionsJson = questionsJson[startIndex:endIndex]
			questionsJson = strings.ReplaceAll(questionsJson, "```json", "")
		}
	}

	// 尝试解析JSON
	var questions []Question
	if err := json.Unmarshal([]byte(questionsJson), &questions); err != nil {
		// 如果直接解析失败，尝试清理数据
		questionsJson = strings.ReplaceAll(questionsJson, "\\n", "")
		questionsJson = strings.ReplaceAll(questionsJson, "\\\"", "\"")

		// 如果不是数组格式，尝试包装成数组
		if !strings.HasPrefix(strings.TrimSpace(questionsJson), "[") {
			questionsJson = "[" + questionsJson + "]"
		}

		if err := json.Unmarshal([]byte(questionsJson), &questions); err != nil {
			return nil, err
		}
	}

	return questions, nil
}

// calculateScore 计算得分和详细结果
func calculateScore(questions []Question, userAnswers map[string]string) (int, []view.QuestionResult) {
	var correctAnswers int
	var detailedResults []view.QuestionResult

	for _, question := range questions {
		userAnswer, exists := userAnswers[question.QuestionID]
		if !exists {
			userAnswer = "" // 未回答
		}

		// 标准化答案比较
		isCorrect := normalizeAnswer(userAnswer) == normalizeAnswer(question.CorrectAnswer)
		if isCorrect {
			correctAnswers++
		}

		detailedResults = append(detailedResults, view.QuestionResult{
			QuestionID:    question.QuestionID,
			Question:      question.Question,
			UserAnswer:    userAnswer,
			CorrectAnswer: question.CorrectAnswer,
			IsCorrect:     isCorrect,
			Explanation:   question.Explanation,
		})
	}

	return correctAnswers, detailedResults
}

// normalizeAnswer 标准化答案格式
func normalizeAnswer(answer string) string {
	normalized := strings.TrimSpace(strings.ToLower(answer))

	// 处理判断题的各种格式
	switch normalized {
	case "true", "t", "正确", "对", "是", "1":
		return "true"
	case "false", "f", "错误", "错", "否", "0":
		return "false"
	default:
		// 对于选择题，直接返回标准化后的答案
		return normalized
	}
}

// generateSessionIDFromUser 从用户信息生成会话ID
func generateSessionIDFromUser(c *gin.Context, sessionInfo map[string]interface{}, userInfo interface{}) string {
	// 优先使用用户ID生成会话ID
	userID := c.GetString(define.UserIDKey)
	if userID != "" {
		return fmt.Sprintf("user_%s_%d", userID, time.Now().Unix())
	}

	// 回退到原有逻辑
	if sessionInfo != nil {
		if sessionID, exists := sessionInfo["sessionId"]; exists {
			if sid, ok := sessionID.(string); ok && sid != "" {
				return sid
			}
		}
	}

	// 使用IP和时间戳生成默认session ID
	clientIP := c.ClientIP()
	return fmt.Sprintf("anonymous_%s_%d", strings.ReplaceAll(clientIP, ".", "_"), time.Now().Unix())
}

// mergeUserInfo 合并用户信息
func mergeUserInfo(sessionInfo map[string]interface{}, userInfo interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	// 复制session info
	if sessionInfo != nil {
		for k, v := range sessionInfo {
			result[k] = v
		}
	}

	// 添加用户信息
	if userInfo != nil {
		if account, ok := userInfo.(models.Account); ok {
			result["user_id"] = account.ID
			result["user_email"] = account.Email
			result["user_name"] = account.Name
			result["authenticated"] = true
		}
	}

	return result
}

// convertAnswersToInterface 转换答案格式
func convertAnswersToInterface(answers map[string]string) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range answers {
		result[k] = v
	}
	return result
}

// generateAnalysis 生成分析报告
func generateAnalysis(score, totalQuestions int, percentage float64) string {
	var analysis strings.Builder

	analysis.WriteString(fmt.Sprintf("您本次答题得分：%d/%d (%.1f%%)\n\n", score, 100, percentage))

	if percentage >= 90 {
		analysis.WriteString("🎉 优秀！您的表现非常出色，对知识点掌握得很好。")
	} else if percentage >= 80 {
		analysis.WriteString("👍 良好！您的表现不错，继续保持。")
	} else if percentage >= 60 {
		analysis.WriteString("📚 及格！您已经掌握了基本知识点，建议继续学习提高。")
	} else {
		analysis.WriteString("💪 需要加强！建议您重新学习相关知识点，多做练习。")
	}

	analysis.WriteString("\n\n请查看详细结果了解每道题的正确答案和解释。")

	return analysis.String()
}

// GetQuizStatistics 获取指定题目的统计信息
// @Summary 获取题目统计信息
// @Description 获取指定UUID题目的答题统计信息
// @Tags Quiz
// @Param uuid path string true "题目UUID"
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=view.QuizStatisticsResponse} "统计信息"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 404 {object} view.DifyError "题目不存在"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/{uuid}/statistics [get]
func GetQuizStatistics(c *gin.Context) {
	uuidParam := c.Param("uuid")
	if uuidParam == "" {
		ErrorResponse(c, "INVALID_PARAMS", "UUID参数不能为空", 400)
		return
	}

	// 解析UUID
	quizUUID, err := uuid.Parse(uuidParam)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的UUID格式", 400)
		return
	}

	// 获取题目信息
	tempDataDao := database.GetTempDataDao()
	tempData, err := tempDataDao.GetByUUID(quizUUID)
	if err != nil {
		if err.Error() == "record not found" {
			ErrorResponse(c, "NOT_FOUND", "题目不存在", 404)
			return
		}
		ErrorResponse(c, "DATABASE_ERROR", "查询题目失败: "+err.Error(), 500)
		return
	}

	// 获取统计信息
	quizDao := database.GetQuizDao()
	stats, err := quizDao.GetQuizStatistics(quizUUID)
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "获取统计信息失败: "+err.Error(), 500)
		return
	}

	// 如果没有统计数据，返回空统计
	if stats == nil {
		response := view.QuizStatisticsResponse{
			QuizUUID:          quizUUID,
			QuizTitle:         tempData.Title,
			TotalSubmissions:  0,
			AverageScore:      0,
			HighestScore:      0,
			LowestScore:       0,
			PassRate:          0,
			ScoreDistribution: make(map[string]int),
			RecentSubmissions: []view.SubmissionSummary{},
		}

		c.JSON(http.StatusOK, view.Result{
			Result: "success",
			Data:   response,
		})
		return
	}

	// 获取分数分布
	scoreDistribution, err := quizDao.GetScoreDistribution(quizUUID)
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "获取分数分布失败: "+err.Error(), 500)
		return
	}

	// 获取最近的提交记录
	submissions, _, err := quizDao.GetQuizSubmissions(quizUUID, 1, 10) // 获取最近10条
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "获取提交记录失败: "+err.Error(), 500)
		return
	}

	// 转换提交记录格式
	var recentSubmissions []view.SubmissionSummary
	for _, submission := range submissions {
		recentSubmissions = append(recentSubmissions, view.SubmissionSummary{
			SubmissionID: submission.ID,
			UserInfo:     submission.UserInfo,
			Score:        submission.Score,
			Percentage:   submission.Percentage,
			SubmittedAt:  submission.SubmittedAt,
		})
	}

	// 构建响应
	response := view.QuizStatisticsResponse{
		QuizUUID:          stats.QuizUUID,
		QuizTitle:         stats.QuizTitle,
		TotalSubmissions:  stats.TotalSubmissions,
		AverageScore:      stats.AverageScore,
		HighestScore:      stats.HighestScore,
		LowestScore:       stats.LowestScore,
		PassRate:          stats.PassRate,
		ScoreDistribution: scoreDistribution,
		RecentSubmissions: recentSubmissions,
		LastSubmissionAt:  stats.LastSubmissionAt,
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// GetBatchStatistics 获取批次统计信息
// @Summary 获取批次统计信息
// @Description 获取指定批次标题下所有题目的统计信息
// @Tags Quiz
// @Param title path string true "批次标题"
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=view.BatchStatisticsResponse} "批次统计信息"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/batch/{title}/statistics [get]
func GetBatchStatistics(c *gin.Context) {
	batchTitle := c.Param("title")
	if batchTitle == "" {
		ErrorResponse(c, "INVALID_PARAMS", "批次标题不能为空", 400)
		return
	}

	// 获取批次下的所有统计信息
	quizDao := database.GetQuizDao()
	batchStats, err := quizDao.GetBatchStatistics(batchTitle)
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "获取批次统计失败: "+err.Error(), 500)
		return
	}

	if len(batchStats) == 0 {
		// 返回空的批次统计
		response := view.BatchStatisticsResponse{
			BatchTitle:       batchTitle,
			TotalQuizzes:     0,
			TotalSubmissions: 0,
			AverageScore:     0,
			QuizStatistics:   []view.QuizStatisticsResponse{},
		}

		c.JSON(http.StatusOK, view.Result{
			Result: "success",
			Data:   response,
		})
		return
	}

	// 计算批次级别的统计
	var totalSubmissions int
	var totalScore float64
	var quizStatistics []view.QuizStatisticsResponse

	for _, stats := range batchStats {
		totalSubmissions += stats.TotalSubmissions
		totalScore += stats.AverageScore * float64(stats.TotalSubmissions)

		// 获取每个quiz的详细统计
		scoreDistribution, _ := quizDao.GetScoreDistribution(stats.QuizUUID)
		submissions, _, _ := quizDao.GetQuizSubmissions(stats.QuizUUID, 1, 5)

		var recentSubmissions []view.SubmissionSummary
		for _, submission := range submissions {
			recentSubmissions = append(recentSubmissions, view.SubmissionSummary{
				SubmissionID: submission.ID,
				UserInfo:     submission.UserInfo,
				Score:        submission.Score,
				Percentage:   submission.Percentage,
				SubmittedAt:  submission.SubmittedAt,
			})
		}

		quizStatistics = append(quizStatistics, view.QuizStatisticsResponse{
			QuizUUID:          stats.QuizUUID,
			QuizTitle:         stats.QuizTitle,
			TotalSubmissions:  stats.TotalSubmissions,
			AverageScore:      stats.AverageScore,
			HighestScore:      stats.HighestScore,
			LowestScore:       stats.LowestScore,
			PassRate:          stats.PassRate,
			ScoreDistribution: scoreDistribution,
			RecentSubmissions: recentSubmissions,
			LastSubmissionAt:  stats.LastSubmissionAt,
		})
	}

	// 计算批次平均分
	var batchAverageScore float64
	if totalSubmissions > 0 {
		batchAverageScore = totalScore / float64(totalSubmissions)
		batchAverageScore = math.Round(batchAverageScore*100) / 100
	}

	// 构建响应
	response := view.BatchStatisticsResponse{
		BatchTitle:       batchTitle,
		TotalQuizzes:     len(batchStats),
		TotalSubmissions: totalSubmissions,
		AverageScore:     batchAverageScore,
		QuizStatistics:   quizStatistics,
		CreatedAt:        batchStats[0].CreatedAt, // 使用第一个quiz的创建时间
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// GetQuizDashboard 获取答题系统仪表板
// @Summary 获取答题系统仪表板
// @Description 获取答题系统的总体统计信息
// @Tags Quiz
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=view.QuizDashboardResponse} "仪表板数据"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/dashboard [get]
func GetQuizDashboard(c *gin.Context) {
	quizDao := database.GetQuizDao()

	// 获取所有批次标题
	batchTitles, err := quizDao.GetAllBatchTitles()
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "获取批次列表失败: "+err.Error(), 500)
		return
	}

	var batchStatistics []view.BatchStatisticsResponse
	var totalQuizzes, totalSubmissions int

	// 为每个批次获取统计信息
	for _, title := range batchTitles {
		batchStats, err := quizDao.GetBatchStatistics(title)
		if err != nil {
			continue // 跳过错误的批次
		}

		if len(batchStats) == 0 {
			continue
		}

		// 计算批次统计
		var batchTotalSubmissions int
		var batchTotalScore float64

		var quizStatistics []view.QuizStatisticsResponse
		for _, stats := range batchStats {
			batchTotalSubmissions += stats.TotalSubmissions
			batchTotalScore += stats.AverageScore * float64(stats.TotalSubmissions)

			// 获取最近的提交记录
			recentSubmissions, err := quizDao.GetQuizRecentSubmissions(stats.QuizUUID, 5)
			if err != nil {
				slog.Error("获取题目最近提交记录失败", "quiz_uuid", stats.QuizUUID, "error", err)
				recentSubmissions = []view.SubmissionSummary{} // 设置为空切片
			}

			quizStatistics = append(quizStatistics, view.QuizStatisticsResponse{
				QuizUUID:          stats.QuizUUID,
				QuizTitle:         stats.QuizTitle,
				TotalSubmissions:  stats.TotalSubmissions,
				AverageScore:      stats.AverageScore,
				HighestScore:      stats.HighestScore,
				LowestScore:       stats.LowestScore,
				PassRate:          stats.PassRate,
				LastSubmissionAt:  stats.LastSubmissionAt,
				RecentSubmissions: recentSubmissions,
			})
		}

		var batchAverageScore float64
		if batchTotalSubmissions > 0 {
			batchAverageScore = batchTotalScore / float64(batchTotalSubmissions)
			batchAverageScore = math.Round(batchAverageScore*100) / 100
		}

		batchStatistics = append(batchStatistics, view.BatchStatisticsResponse{
			BatchTitle:       title,
			TotalQuizzes:     len(batchStats),
			TotalSubmissions: batchTotalSubmissions,
			AverageScore:     batchAverageScore,
			QuizStatistics:   quizStatistics,
			CreatedAt:        batchStats[0].CreatedAt,
		})

		totalQuizzes += len(batchStats)
		totalSubmissions += batchTotalSubmissions
	}

	// 构建仪表板响应
	response := view.QuizDashboardResponse{
		TotalBatches:     len(batchTitles),
		TotalQuizzes:     totalQuizzes,
		TotalSubmissions: totalSubmissions,
		BatchStatistics:  batchStatistics,
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// ExportQuizToExcel 导出题目到Excel文件
// @Summary 导出题目到Excel
// @Description 导出指定题目的所有问题数据到Excel文件
// @Tags Quiz
// @Param uuid path string true "题目UUID"
// @Accept json
// @Produce application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Success 200 {file} file "Excel文件"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 404 {object} view.DifyError "题目不存在"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/{uuid}/export [get]
func ExportQuizToExcel(c *gin.Context) {
	uuidParam := c.Param("uuid")
	if uuidParam == "" {
		ErrorResponse(c, "INVALID_PARAMS", "UUID参数不能为空", 400)
		return
	}

	// 解析UUID
	quizUUID, err := uuid.Parse(uuidParam)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的UUID格式", 400)
		return
	}

	// 获取题目数据
	tempDataDao := database.GetTempDataDao()
	tempData, err := tempDataDao.GetByUUID(quizUUID)
	if err != nil {
		if err.Error() == "record not found" {
			ErrorResponse(c, "NOT_FOUND", "题目不存在", 404)
			return
		}
		ErrorResponse(c, "DATABASE_ERROR", "查询题目失败: "+err.Error(), 500)
		return
	}

	// 解析题目数据
	questions, err := parseQuizQuestions(tempData.Content)
	if err != nil {
		ErrorResponse(c, "JSON_PARSE_ERROR", "题目数据解析失败: "+err.Error(), 500)
		return
	}

	// 生成Excel文件
	excelData, err := generateQuizExcel(questions, tempData.Title)
	if err != nil {
		ErrorResponse(c, "EXCEL_GENERATION_ERROR", "生成Excel文件失败: "+err.Error(), 500)
		return
	}

	// 设置响应头 - 使用CSV格式，但可以用Excel打开
	fileName := fmt.Sprintf("%s_题目列表_%s.csv", tempData.Title, time.Now().Format("2006-01-02"))
	c.Header("Content-Description", "File Transfer")
	//responseWriter.Header().Add("Content-Type", "application/octet-stream")

	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))
	//c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Transfer-Encoding", "binary")

	// 返回CSV文件
	c.Data(http.StatusOK, "text/csv", excelData)
}

// GetQuizResults 获取题目的答题结果列表
// @Summary 获取题目答题结果
// @Description 获取指定题目的所有答题结果，支持分页
// @Tags Quiz
// @Param uuid path string true "题目UUID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=view.QuizResultsResponse} "答题结果列表"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 404 {object} view.DifyError "题目不存在"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/{uuid}/results [get]
func GetQuizResults(c *gin.Context) {
	uuidParam := c.Param("uuid")
	if uuidParam == "" {
		ErrorResponse(c, "INVALID_PARAMS", "UUID参数不能为空", 400)
		return
	}

	// 解析UUID
	quizUUID, err := uuid.Parse(uuidParam)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的UUID格式", 400)
		return
	}

	// 获取分页参数
	page := 1
	pageSize := 20

	if pageParam := c.Query("page"); pageParam != "" {
		if p, err := strconv.Atoi(pageParam); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeParam := c.Query("page_size"); pageSizeParam != "" {
		if ps, err := strconv.Atoi(pageSizeParam); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 获取题目信息
	tempDataDao := database.GetTempDataDao()
	tempData, err := tempDataDao.GetByUUID(quizUUID)
	if err != nil {
		if err.Error() == "record not found" {
			ErrorResponse(c, "NOT_FOUND", "题目不存在", 404)
			return
		}
		ErrorResponse(c, "DATABASE_ERROR", "查询题目失败: "+err.Error(), 500)
		return
	}

	// 获取答题结果
	quizDao := database.GetQuizDao()
	submissions, total, err := quizDao.GetQuizSubmissions(quizUUID, page, pageSize)
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "获取答题结果失败: "+err.Error(), 500)
		return
	}

	// 转换为响应格式
	var submissionSummaries []view.SubmissionSummary
	for _, submission := range submissions {
		submissionSummaries = append(submissionSummaries, view.SubmissionSummary{
			SubmissionID: submission.ID,
			UserInfo:     submission.UserInfo,
			Score:        submission.Score,
			Percentage:   submission.Percentage,
			SubmittedAt:  submission.SubmittedAt,
		})
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	// 构建响应
	response := view.QuizResultsResponse{
		QuizUUID:    quizUUID,
		QuizTitle:   tempData.Title,
		Submissions: submissionSummaries,
		TotalCount:  int(total),
		Page:        page,
		PageSize:    pageSize,
		TotalPages:  totalPages,
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// ShowStatisticsPage 显示统计页面
// @Summary 显示统计页面
// @Description 显示答题统计页面
// @Tags Quiz
// @Accept html
// @Produce html
// @Success 200 {string} string "统计页面HTML"
// @Router /statistics [get]
func ShowStatisticsPage(c *gin.Context) {
	c.HTML(http.StatusOK, "statistics.tmpl", gin.H{
		"title": "答题统计",
	})
}

// ShowDashboardPage 显示仪表板页面
// @Summary 显示仪表板页面
// @Description 显示答题系统仪表板页面
// @Tags Quiz
// @Accept html
// @Produce html
// @Success 200 {string} string "仪表板页面HTML"
// @Router /dashboard [get]
func ShowDashboardPage(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.tmpl", gin.H{
		"title": "系统仪表板",
	})
}

// VerifyLoginStatus 验证用户登录状态
// @Summary 验证用户登录状态
// @Description 验证用户token是否有效，用于前端页面加载时检查
// @Tags Auth
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=map[string]interface{}} "登录状态有效"
// @Failure 401 {object} view.DifyError "未登录或token过期"
// @Router /api/verify-login [get]
func VerifyLoginStatus(c *gin.Context) {
	// 获取用户信息（如果中间件验证通过，这些信息应该已经设置）
	userID := c.GetString(define.UserIDKey)
	userEmail := c.GetString(define.UserEmailKey)
	userAttr, exists := c.Get(define.UserAttrKey)

	if userID == "" || !exists {
		c.JSON(http.StatusUnauthorized, view.DifyError{
			Code:    "UNAUTHORIZED",
			Message: "用户未登录或登录状态已过期",
		})
		return
	}

	// 获取是否为超管
	isAdmin, _ := c.Get(define.IsAdminKey)

	// 构建用户信息响应
	userInfo := map[string]interface{}{
		"user_id":    userID,
		"user_email": userEmail,
		"logged_in":  true,
		"is_admin":   isAdmin,
	}

	// 如果有完整的用户信息，添加更多字段
	if account, ok := userAttr.(models.Account); ok {
		userInfo["user_name"] = account.Name
		userInfo["user_status"] = account.Status
		userInfo["user_unit"] = account.Unit
		userInfo["user_department"] = account.Department
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   userInfo,
	})
}

// GetBatchUserStatistics 获取批次用户统计
// @Summary 获取批次用户统计
// @Description 获取指定批次的用户答题统计信息
// @Tags Quiz
// @Param title path string true "批次标题"
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=view.BatchUserStatistics} "批次用户统计"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 401 {object} view.DifyError "未登录"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /api/quiz/batch/{title}/user-statistics [get]
func GetBatchUserStatistics(c *gin.Context) {
	title := c.Param("title")
	if title == "" {
		ErrorResponse(c, "INVALID_PARAMS", "批次标题不能为空", 400)
		return
	}

	quizDao := database.GetQuizDao()

	// 获取批次下所有题目的提交记录
	submissions, err := quizDao.GetBatchSubmissions(title)
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "获取批次提交记录失败: "+err.Error(), 500)
		return
	}

	// 按用户分组统计
	userStatsMap := make(map[string]*view.UserQuizStatistics)
	totalAttempts := 0
	totalScore := 0.0

	for _, submission := range submissions {
		totalAttempts++
		totalScore += float64(submission.Score)

		// 获取用户标识（优先使用user_id，否则使用session_id）
		var userKey string
		var userInfo map[string]interface{}

		if submission.UserID != nil {
			userKey = submission.UserID.String()
		} else {
			userKey = submission.SessionID
		}

		// 解析用户信息
		if submission.UserInfo != nil {
			userInfo = submission.UserInfo
		}

		// 如果用户统计不存在，创建新的
		if _, exists := userStatsMap[userKey]; !exists {
			userStatsMap[userKey] = &view.UserQuizStatistics{
				UserID:        submission.UserID,
				UserInfo:      userInfo,
				QuizUUID:      submission.QuizUUID,
				QuizTitle:     submission.QuizTitle,
				AttemptCount:  0,
				BestScore:     0,
				LatestScore:   0,
				AverageScore:  0,
				FirstAttempt:  submission.SubmittedAt,
				LatestAttempt: submission.SubmittedAt,
				HasAnswered:   true,
			}
		}

		userStats := userStatsMap[userKey]
		userStats.AttemptCount++

		// 更新最高分
		if submission.Score > userStats.BestScore {
			userStats.BestScore = submission.Score
		}

		// 更新最新得分和时间
		if submission.SubmittedAt.After(userStats.LatestAttempt) {
			userStats.LatestScore = submission.Score
			userStats.LatestAttempt = submission.SubmittedAt
		}

		// 更新首次答题时间
		if submission.SubmittedAt.Before(userStats.FirstAttempt) {
			userStats.FirstAttempt = submission.SubmittedAt
		}
	}

	// 计算每个用户的平均分和详细记录
	userStatsList := make([]view.UserQuizStatistics, 0, len(userStatsMap))
	for userKey, userStats := range userStatsMap {
		// 计算该用户的平均分
		userSubmissions := make([]models.QuizSubmission, 0)
		totalUserScore := 0.0
		quizDetailsList := make([]view.QuizDetail, 0)

		for _, submission := range submissions {
			var submissionUserKey string
			if submission.UserID != nil {
				submissionUserKey = submission.UserID.String()
			} else {
				submissionUserKey = submission.SessionID
			}

			if submissionUserKey == userKey {
				userSubmissions = append(userSubmissions, submission)
				totalUserScore += float64(submission.Score)

				// 收集所有答题记录（不去重，保留所有答题历史）
				quizDetailsList = append(quizDetailsList, view.QuizDetail{
					QuizUUID:    submission.QuizUUID,
					QuizTitle:   submission.QuizTitle,
					Score:       submission.Score,
					Percentage:  submission.Percentage,
					SubmittedAt: submission.SubmittedAt,
				})
			}
		}

		userStats.AverageScore = totalUserScore / float64(len(userSubmissions))

		// 按时间排序答题记录
		for i := 0; i < len(quizDetailsList)-1; i++ {
			for j := i + 1; j < len(quizDetailsList); j++ {
				if quizDetailsList[i].SubmittedAt.After(quizDetailsList[j].SubmittedAt) {
					quizDetailsList[i], quizDetailsList[j] = quizDetailsList[j], quizDetailsList[i]
				}
			}
		}

		userStats.QuizDetails = quizDetailsList
		userStatsList = append(userStatsList, *userStats)
	}

	// 构建响应
	response := view.BatchUserStatistics{
		BatchTitle:     title,
		UserStatistics: userStatsList,
		TotalUsers:     len(userStatsList),
		TotalAttempts:  totalAttempts,
	}

	if totalAttempts > 0 {
		response.AverageScore = totalScore / float64(totalAttempts)
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// CheckUserAnswered 检查用户是否已答题
// @Summary 检查用户答题状态
// @Description 检查用户是否已经答过指定题目
// @Tags Quiz
// @Param uuid path string true "题目UUID"
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=map[string]interface{}} "答题状态"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 401 {object} view.DifyError "未登录"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/{uuid}/check [get]
func CheckUserAnswered(c *gin.Context) {
	uuidParam := c.Param("uuid")
	if uuidParam == "" {
		ErrorResponse(c, "INVALID_PARAMS", "UUID参数不能为空", 400)
		return
	}

	// 解析UUID
	quizUUID, err := uuid.Parse(uuidParam)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的UUID格式", 400)
		return
	}

	// 获取用户信息
	userID := c.GetString(define.UserIDKey)
	var userUUID *uuid.UUID
	if userID != "" {
		if parsedUUID, err := uuid.Parse(userID); err == nil {
			userUUID = &parsedUUID
		}
	}

	// 检查用户是否已答题
	quizDao := database.GetQuizDao()
	hasAnswered, err := quizDao.CheckUserAnswered(quizUUID, userUUID)
	if err != nil {
		ErrorResponse(c, "DATABASE_ERROR", "检查答题状态失败: "+err.Error(), 500)
		return
	}

	// 如果已答题，获取答题统计
	var userStats *models.QuizSubmission
	if hasAnswered {
		userStats, err = quizDao.GetUserQuizStatistics(quizUUID, userUUID)
		if err != nil {
			ErrorResponse(c, "DATABASE_ERROR", "获取答题统计失败: "+err.Error(), 500)
			return
		}
	}

	response := map[string]interface{}{
		"has_answered": hasAnswered,
		"quiz_uuid":    quizUUID,
	}

	if userStats != nil {
		response["latest_score"] = userStats.Score
		response["latest_percentage"] = userStats.Percentage
		response["submitted_at"] = userStats.SubmittedAt
		response["user_answers"] = userStats.UserAnswers
		response["user_info"] = userStats.UserInfo

		// 获取题目数据以生成详细结果
		tempDataDao := database.GetTempDataDao()
		tempData, err := tempDataDao.GetByUUID(quizUUID)
		if err == nil {
			questions, err := parseQuizQuestions(tempData.Content)
			if err == nil {
				// 转换用户答案格式
				userAnswersMap := make(map[string]string)

				// 处理JSON类型的用户答案
				if userStats.UserAnswers != nil {
					for k, v := range userStats.UserAnswers {
						if vStr, ok := v.(string); ok {
							userAnswersMap[k] = vStr
						}
					}
				}

				// 生成详细结果
				_, detailedResults := calculateScore(questions, userAnswersMap)
				response["detailed_results"] = detailedResults
			}
		}
	}

	c.JSON(http.StatusOK, view.Result{
		Result: "success",
		Data:   response,
	})
}

// DeleteBatch 删除批次（仅超管）
// @Summary 删除批次
// @Description 删除指定批次及其所有相关数据（仅超级管理员）
// @Tags Quiz
// @Param title path string true "批次标题"
// @Accept json
// @Produce json
// @Success 200 {object} view.Result "删除成功"
// @Failure 400 {object} view.DifyError "参数错误"
// @Failure 403 {object} view.DifyError "权限不足"
// @Failure 500 {object} view.DifyError "服务器错误"
// @Router /quiz/batch/{title} [delete]
func DeleteBatch(c *gin.Context) {
	batchTitle := c.Param("title")
	if batchTitle == "" {
		ErrorResponse(c, "INVALID_PARAMS", "批次标题不能为空", 400)
		return
	}

	// 获取用户信息
	userID := c.GetString(define.UserIDKey)
	isAdmin, _ := c.Get(define.IsAdminKey)

	slog.Info("删除批次请求", "用户ID", userID, "是否超管", isAdmin, "批次", batchTitle)

	// 再次确认是否为超管（双重检查）
	if !isAdmin.(bool) {
		ErrorResponse(c, "PERMISSION_DENIED", "只有超级管理员才能删除批次", 403)
		return
	}

	// 开始删除操作
	quizDao := database.GetQuizDao()
	tempDataDao := database.GetTempDataDao()

	// 1. 获取该批次下的所有题目UUID
	quizUUIDs, err := tempDataDao.GetUUIDsByBatchTitle(batchTitle)
	if err != nil {
		slog.Error("获取批次题目UUID失败", "error", err)
		ErrorResponse(c, "DATABASE_ERROR", "获取批次数据失败: "+err.Error(), 500)
		return
	}

	if len(quizUUIDs) == 0 {
		ErrorResponse(c, "NOT_FOUND", "批次不存在或已被删除", 404)
		return
	}

	// 2. 删除所有答题记录
	deletedSubmissions := 0
	for _, quizUUID := range quizUUIDs {
		count, err := quizDao.DeleteSubmissionsByQuizUUID(quizUUID)
		if err != nil {
			slog.Error("删除题目的答题记录失败", "quizUUID", quizUUID, "error", err)
			ErrorResponse(c, "DATABASE_ERROR", "删除答题记录失败: "+err.Error(), 500)
			return
		}
		deletedSubmissions += count
	}

	// 3. 删除所有题目数据
	deletedQuizzes, err := tempDataDao.DeleteByBatchTitle(batchTitle)
	if err != nil {
		slog.Error("删除批次题目失败", "error", err)
		ErrorResponse(c, "DATABASE_ERROR", "删除题目数据失败: "+err.Error(), 500)
		return
	}

	slog.Info("批次删除成功", "批次", batchTitle, "删除题目", deletedQuizzes, "删除答题记录", deletedSubmissions)

	response := map[string]interface{}{
		"message":             "批次删除成功",
		"batch_title":         batchTitle,
		"deleted_quizzes":     deletedQuizzes,
		"deleted_submissions": deletedSubmissions,
	}

	SuccessResponse(c, response)
}

// generateQuizExcel 生成测验Excel文件
func generateQuizExcel(questions []Question, quizTitle string) ([]byte, error) {
	// 创建一个简单的CSV格式，然后转换为Excel格式
	// 由于没有Excel库，我们先创建CSV内容，然后手动构建简单的Excel格式

	var csvContent strings.Builder

	// 添加BOM以支持中文
	csvContent.WriteString("\xEF\xBB\xBF")

	// 写入表头
	csvContent.WriteString("序号,题目,分值,类型,参考答案,选项,解析\n")

	// 计算每题分值
	scorePerQuestion := 100.0 / float64(len(questions))

	// 写入数据行
	for i, q := range questions {
		// 序号
		csvContent.WriteString(fmt.Sprintf("%d,", i+1))

		// 题目 - 处理逗号和引号
		question := strings.ReplaceAll(q.Question, "\"", "\"\"")
		if strings.Contains(question, ",") || strings.Contains(question, "\n") {
			question = "\"" + question + "\""
		}
		csvContent.WriteString(question + ",")

		// 分值
		csvContent.WriteString(fmt.Sprintf("%.1f,", scorePerQuestion))

		// 类型
		questionType := getQuestionTypeText(q.QuestionType)
		csvContent.WriteString(questionType + ",")

		// 参考答案
		answer := strings.ReplaceAll(q.CorrectAnswer, "\"", "\"\"")
		if strings.Contains(answer, ",") || strings.Contains(answer, "\n") {
			answer = "\"" + answer + "\""
		}
		csvContent.WriteString(answer + ",")

		// 选项
		var options string
		if len(q.Options) > 0 {
			optionsList := make([]string, len(q.Options))
			for j, opt := range q.Options {
				optionsList[j] = opt
			}
			options = strings.Join(optionsList, "; ")
		}
		options = strings.ReplaceAll(options, "\"", "\"\"")
		if strings.Contains(options, ",") || strings.Contains(options, "\n") {
			options = "\"" + options + "\""
		}
		csvContent.WriteString(options + ",")

		// 解析
		explanation := strings.ReplaceAll(q.Explanation, "\"", "\"\"")
		if strings.Contains(explanation, ",") || strings.Contains(explanation, "\n") {
			explanation = "\"" + explanation + "\""
		}
		csvContent.WriteString(explanation)

		csvContent.WriteString("\n")
	}

	return []byte(csvContent.String()), nil
}

// getQuestionTypeText 获取题目类型的中文文本
func getQuestionTypeText(questionType string) string {
	typeMap := map[string]string{
		"single_choice":   "单选题",
		"multiple_choice": "多选题",
		"true_false":      "判断题",
		"fill_blank":      "填空题",
		"short_answer":    "简答题",
	}

	if text, exists := typeMap[questionType]; exists {
		return text
	}
	return questionType
}
