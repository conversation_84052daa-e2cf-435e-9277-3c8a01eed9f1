package handlers

import (
	"database/sql"
	"time"

	"difyserver/database"
	"difyserver/models"
	"difyserver/utils/define"
	"difyserver/view"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GetAllFeedbacks godoc
// @Summary 获取所有反馈
// @Description 管理员获取所有用户的反馈列表
// @Tags 反馈管理
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=[]view.FeedbackResponse} "成功获取反馈列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /feedbacks.json [get]
// @Security ApiKeyAuth
func GetAllFeedbacks(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	var feedbacks []models.FeedbackResponse

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 从数据库获取当前租户的所有反馈，按创建时间降序排序
	query := `
		SELECT f.*, u.name as user_name 
		FROM feedbacks f
		LEFT JOIN accounts u ON f.user_id = u.id
		WHERE f.tenant_id = ?
		ORDER BY f.created_at DESC
	`
	if err := database.DB.Raw(query, tenantUUID).Scan(&feedbacks).Error; err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	SuccessResponse(c, feedbacks)
}

// GetUserFeedbacks godoc
// @Summary 获取用户自己的反馈
// @Description 用户获取自己提交的反馈列表
// @Tags 反馈管理
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=[]models.Feedback} "成功获取用户反馈列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /user/feedbacks.json [get]
// @Security ApiKeyAuth
func GetUserFeedbacks(c *gin.Context) {
	// 获取当前用户ID和租户ID
	userID := c.GetString(define.UserIDKey)
	tenantID := c.GetString(define.CurrentTenantIDKey)

	if userID == "" || tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	var feedbacks []models.Feedback

	// 解析用户ID和租户ID为UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的用户ID", 400)
		return
	}

	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 查询指定用户和租户的反馈
	query := `
		SELECT * FROM feedbacks 
		WHERE user_id = ? AND tenant_id = ?
		ORDER BY created_at DESC
	`
	if err := database.DB.Raw(query, userUUID, tenantUUID).Scan(&feedbacks).Error; err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	SuccessResponse(c, feedbacks)
}

// CreateFeedback godoc
// @Summary 提交反馈
// @Description 用户提交新的反馈
// @Tags 反馈管理
// @Accept json
// @Produce json
// @Param feedback body view.CreateFeedbackRequest true "反馈内容"
// @Success 200 {object} view.Result{data=models.Feedback} "成功提交反馈"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /feedbacks.json [post]
// @Security ApiKeyAuth
func CreateFeedback(c *gin.Context) {
	// 获取当前用户ID和租户ID
	userID := c.GetString(define.UserIDKey)
	tenantID := c.GetString(define.CurrentTenantIDKey)

	if userID == "" || tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 绑定请求参数
	var req view.CreateFeedbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证反馈内容
	if req.FeedbackContent == "" {
		ErrorResponse(c, "INVALID_PARAMS", "反馈内容不能为空", 400)
		return
	}

	// 解析用户ID和租户ID为UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的用户ID", 400)
		return
	}

	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 创建反馈记录
	feedbackID := uuid.New() // 生成新的UUID

	feedback := models.Feedback{
		ID:              feedbackID,
		UserID:          userUUID,
		TenantID:        tenantUUID,
		FeedbackContent: req.FeedbackContent,
		Status:          models.FeedbackStatusPending,
		CreatedAt:       time.Now(),
	}

	// 使用原始 SQL 查询插入数据
	query := `
		INSERT INTO feedbacks (id, user_id, tenant_id, feedback_content, status, created_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`

	if err := database.DB.Exec(query,
		feedbackID,
		userUUID,
		tenantUUID,
		req.FeedbackContent,
		string(models.FeedbackStatusPending),
		time.Now()).Error; err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	SuccessResponse(c, feedback)
}

// RespondToFeedback godoc
// @Summary 回复反馈
// @Description 管理员回复用户的反馈
// @Tags 反馈管理
// @Accept json
// @Produce json
// @Param id path int true "反馈ID"
// @Param response body view.RespondFeedbackRequest true "回复内容"
// @Success 200 {object} view.Result{data=models.Feedback} "成功回复反馈"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /feedbacks/{id}/respond.json [post]
// @Security ApiKeyAuth
func RespondToFeedback(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 获取反馈ID
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的反馈ID", 400)
		return
	}

	// 绑定请求参数
	var req view.RespondFeedbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	// 验证回复内容
	if req.Response == "" {
		ErrorResponse(c, "INVALID_PARAMS", "回复内容不能为空", 400)
		return
	}

	// 获取反馈（确保只能回复自己租户的反馈）
	var feedback models.Feedback
	if err := database.DB.Where("id = ? AND tenant_id = ?", id, tenantUUID).First(&feedback).Error; err != nil {
		ErrorResponse(c, "NOT_FOUND", "反馈不存在", 404)
		return
	}

	// 更新反馈状态和回复
	feedback.Response = sql.NullString{String: req.Response, Valid: true}
	feedback.Status = models.FeedbackStatusAnswered
	feedback.UpdatedAt = sql.NullTime{Time: time.Now(), Valid: true}

	// 保存到数据库
	if err := database.DB.Save(&feedback).Error; err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	SuccessResponse(c, feedback)
}

// GetFeedback godoc
// @Summary 查看反馈详情
// @Description 查看单个反馈的详细信息
// @Tags 反馈管理
// @Accept json
// @Produce json
// @Param id path int true "反馈ID"
// @Success 200 {object} view.Result{data=models.Feedback} "成功获取反馈详情"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /feedbacks/{id}/info.json [get]
// @Security ApiKeyAuth
func GetFeedback(c *gin.Context) {
	// 获取当前租户ID
	tenantID := c.GetString(define.CurrentTenantIDKey)
	if tenantID == "" {
		ErrorResponse(c, "UNAUTHORIZED", "未授权的访问", 401)
		return
	}

	// 解析租户ID为UUID
	tenantUUID, err := uuid.Parse(tenantID)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的租户ID", 400)
		return
	}

	// 获取反馈ID
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的反馈ID", 400)
		return
	}

	// 查询反馈（确保只能查看自己租户的反馈）
	var feedback models.Feedback
	query := `
		SELECT f.*, u.name as user_name 
		FROM feedbacks f
		LEFT JOIN accounts u ON f.user_id = u.id
		WHERE f.id = ? AND f.tenant_id = ?
	`
	if err := database.DB.Raw(query, id, tenantUUID).Scan(&feedback).Error; err != nil {
		ErrorResponse(c, "NOT_FOUND", "反馈不存在", 404)
		return
	}

	SuccessResponse(c, feedback)
}
