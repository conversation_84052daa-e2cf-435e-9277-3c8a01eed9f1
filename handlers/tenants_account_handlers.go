package handlers

import (
	"errors"
	"math"
	"strconv"
	"time"

	"difyserver/database"
	"difyserver/models"
	"difyserver/utils/define"
	"difyserver/view"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/samber/lo"
)

// ListCanJoinTenant 获取有权限加入的团队列表
// @Summary 获取有权限加入的团队列表
// @Tags 团队账户管理
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=[]view.SimpleTenant} "成功获取团队账户关联列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /list_can_join_tenant.json [get]
// @Security ApiKeyAuth
func ListCanJoinTenant(c *gin.Context) {
	tenants, err := listCanJoinTenant(c.GetString(define.UserIDKey))
	if err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}
	SuccessResponse(c, tenants)
}
func listCanJoinTenant(userID string) ([]view.SimpleTenant, error) {
	var accounts []view.SimpleTenant

	result := database.DB.Model(&models.TenantAccountJoin{}).Select("tenants.id,tenants.name").Joins("JOIN tenants ON tenants.id= tenant_id ").Where("account_id=?", userID).Where("role in (?)", []string{view.TenantAccountRoleOwner, view.TenantAccountRoleAdmin}).Group("tenants.id,tenants.name").Order("tenants.created_at").Find(&accounts)
	if result.Error != nil {
		return nil, result.Error
	}
	return accounts, nil
}

// ListCanJoinAccount 获取可加入的账户
// @Summary 获取可加入的账户
// @Description 获取可加入的账户
// @Tags 团队账户管理
// @Accept  json
// @Produce  json
// @Param tenant_id query string true "租户ID"
// @Success 200 {object} view.Result{data=[]view.SimpleAccount} "成功获取团队账户关联列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /list_tenant_can_join_account.json [get]
func ListCanJoinAccount(c *gin.Context) {
	tenantID := c.DefaultQuery("tenant_id", "")
	if tenantID == "" {
		ErrorResponse(c, "PARAM_ERROR", "tenant_id is required", 500)
		return
	}
	accounts, err := listCanJoinAccount(tenantID)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", err.Error(), 500)
		return
	}

	SuccessResponse(c, accounts)
}

func listCanJoinAccount(tenantID string) ([]view.SimpleAccount, error) {
	accounts := make([]view.SimpleAccount, 0)
	if result := database.DB.Model(&models.Account{}).Where("status=?", models.ACTIVE).Scan(&accounts); result.Error != nil {
		return nil, result.Error
	}
	ajs := make([]models.TenantAccountJoin, 0)
	if result := database.DB.Model(&models.TenantAccountJoin{}).Where("tenant_id=?", tenantID).Scan(&ajs); result.Error != nil {
		return nil, result.Error
	}
	acs := lo.Map(ajs, func(item models.TenantAccountJoin, _ int) string {
		return item.AccountID
	})
	filter := lo.Filter(accounts, func(item view.SimpleAccount, _ int) bool {
		return !lo.Contains(acs, item.ID)
	})
	return filter, nil
}

// ListTenantAccount godoc
// @Summary 获取团队账户关联列表
// @Description 获取团队账户关联列表，支持分页，返回包含团队名称和账户名称的详细信息
// @Tags 团队账户管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1"
// @Param tenant_id query string false "团队ID"
// @Success 200 {object} view.Result{data=models.PageResponse{data=[]view.TenantAccountView}} "成功获取团队账户关联列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /list_tenant_account.json [get]
// @Security ApiKeyAuth
func ListTenantAccount(c *gin.Context) {
	var total int64
	pageStr := c.DefaultQuery("page", "1")
	tenantID := c.DefaultQuery("tenant_id", "")
	if err := checkCurrentUserPermission(c, tenantID, ""); err != nil {
		ErrorResponse(c, "PARAM_ERROR", err.Error(), 500)
		return
	}
	page, _ := strconv.Atoi(pageStr)
	limit := 10
	offset := (page - 1) * limit

	// 先获取总记录数
	database.DB.Model(&models.TenantAccountJoin{}).Where("tenant_id = ?", tenantID).Count(&total)

	datas := new([]view.TenantAccountView)
	// 获取分页数据，包含团队名称和账户名称
	result := database.DB.Model(&models.TenantAccountJoin{}).
		Select("tenant_account_joins.*, tenants.name as tenant_name, accounts.name as account_name,accounts.unit as unit,accounts.department as department").
		InnerJoins("JOIN accounts ON accounts.id = tenant_account_joins.account_id").
		InnerJoins("JOIN tenants ON tenants.id = tenant_account_joins.tenant_id").
		Where("tenant_id = ?", tenantID).Limit(limit).Offset(offset).Find(datas)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := models.PageResponse{
		Data:       datas,
		Total:      total,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
	}

	SuccessResponse(c, response)
}

// ListTenantAccountByAccount godoc
// @Summary 根据账户获取团队账户关联
// @Description 根据指定账户ID获取团队账户关联列表
// @Tags 团队账户管理
// @Accept json
// @Produce json
// @Param account_id query string true "账户ID"
// @Param page query int false "页码，默认为1"
// @Success 200 {object} view.Result{data=models.PageResponse} "成功获取账户关联的团队列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /list_tenant_account_by_account.json [get]
// @Security ApiKeyAuth
func ListTenantAccountByAccount(c *gin.Context) {
	var total int64
	accountID := c.Query("account_id")
	pageStr := c.DefaultQuery("page", "1")
	page, _ := strconv.Atoi(pageStr)
	limit := 10
	offset := (page - 1) * limit

	if accountID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "account_id 参数必填", 400)
		return
	}
	// 先获取总记录数
	database.DB.Model(&models.TenantAccountJoin{}).Where("account_id = ?", accountID).Count(&total)
	datas := new([]view.TenantAccountView)
	// 获取分页数据
	result := database.DB.Model(&models.TenantAccountJoin{}).
		Select("tenant_account_joins.*, tenants.name as tenant_name,accounts.name as account_name,accounts.unit as unit,accounts.department as department").
		InnerJoins("JOIN accounts ON accounts.id = tenant_account_joins.account_id").
		InnerJoins("JOIN tenants ON tenants.id = tenant_account_joins.tenant_id").
		Where("account_id = ?", accountID).Limit(limit).Offset(offset).Find(datas)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := models.PageResponse{
		Data:       datas,
		Total:      total,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
	}
	SuccessResponse(c, response)
}

// ListTenantAccountByTenant godoc
// @Summary 根据团队获取团队账户关联
// @Description 根据指定团队ID获取团队账户关联列表
// @Tags 团队账户管理
// @Accept json
// @Produce json
// @Param tenant_id query string true "团队ID"
// @Param page query int false "页码，默认为1"
// @Success 200 {object} view.Result{data=models.PageResponse} "成功获取团队关联的账户列表"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /list_tenant_account_by_tenant.json [get]
// @Security ApiKeyAuth
func ListTenantAccountByTenant(c *gin.Context) {
	var total int64
	tenantID := c.Query("tenant_id")
	if err := checkCurrentUserPermission(c, tenantID, ""); err != nil {
		ErrorResponse(c, "PARAM_ERROR", err.Error(), 500)
		return
	}
	pageStr := c.DefaultQuery("page", "1")
	page, _ := strconv.Atoi(pageStr)
	limit := 10
	offset := (page - 1) * limit

	if tenantID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "tenant_id 参数必填", 400)
		return
	}
	datas := new([]view.TenantAccountView)
	// 先获取总记录数
	database.DB.Model(&models.TenantAccountJoin{}).Where("tenant_id = ?", tenantID).Count(&total)

	// 获取分页数据
	result := database.DB.Model(&models.TenantAccountJoin{}).
		Select("tenant_account_joins.*, tenants.name as tenant_name,accounts.name as account_name,accounts.unit as unit,accounts.department as department").
		InnerJoins("JOIN accounts ON accounts.id = tenant_account_joins.account_id").
		InnerJoins("JOIN tenants ON tenants.id = tenant_account_joins.tenant_id").
		Where("tenant_id = ?", tenantID).Limit(limit).Offset(offset).Find(datas)
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	response := models.PageResponse{
		Data:       datas,
		Total:      total,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
	}
	SuccessResponse(c, response)
}

// AddTenantAccount godoc
// @Summary 添加团队账户关联
// @Description 创建团队与账户的关联关系
// @Tags 团队账户管理
// @Accept json
// @Produce json
// @Param tenantAccount body models.TenantAccountRequest true "团队账户关联信息"
// @Success 200 {object} view.Result{data=map[string]string} "成功创建关联"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /add_tenant_account.json [post]
// @Security ApiKeyAuth
func AddTenantAccount(c *gin.Context) {
	var req struct {
		AccountID string `json:"account_id"`
		TenantID  string `json:"tenant_id"`
		Role      string `json:"role"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}
	if err := checkCurrentUserPermission(c, req.TenantID, ""); err != nil {
		ErrorResponse(c, "PARAM_ERROR", err.Error(), 500)
		return
	}
	// 不能添加团队已存在的用户
	accounts, err := listCanJoinAccount(req.TenantID)
	if err != nil {
		ErrorResponse(c, "PARAM_ERROR", err.Error(), 500)
		return
	}
	filter := lo.Filter(accounts, func(account view.SimpleAccount, _ int) bool {
		return account.ID == req.AccountID
	})
	if len(filter) == 0 {
		ErrorResponse(c, "PARAM_ERROR", "该用户已存在，不能重复添加", 500)
		return
	}

	// 验证参数不为空
	if req.AccountID == "" || req.TenantID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "account_id 和 tenant_id 不能为空", 400)
		return
	}

	if req.Role == "" {
		req.Role = view.TenantAccountRoleNormal // 默认角色
	} else if !view.ValidRoles[req.Role] {
		ErrorResponse(c, "INVALID_PARAMS", "无效的角色值", 400)
		return
	}

	join := models.TenantAccountJoin{
		ID:        uuid.New().String(),
		TenantID:  req.TenantID,
		AccountID: req.AccountID,
		Role:      req.Role,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if result := database.DB.Create(&join); result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	SuccessResponse(c, join)
}

// DelTenantAccount godoc
// @Summary 删除团队账户关联
// @Description 删除团队与账户的关联关系
// @Tags 团队账户管理
// @Accept json
// @Produce json
// @Param tenantAccount body models.DeleteTenantAccount true "团队账户关联信息"
// @Success 200 {object} view.Result{data=map[string]string} "成功删除关联"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /del_tenant_account.json [post]
// @Security ApiKeyAuth
func DelTenantAccount(c *gin.Context) {
	req := new(models.DeleteTenantAccount)
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}
	tenantAccountJoin := new(models.TenantAccountJoin)
	if result := database.DB.Where("id = ?", req.ID).First(tenantAccountJoin); result.Error != nil || tenantAccountJoin.ID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "未找到用户", 500)
		return
	}
	if err := checkCurrentUserPermission(c, tenantAccountJoin.TenantID, tenantAccountJoin.AccountID); err != nil {
		ErrorResponse(c, "PARAM_ERROR", err.Error(), 500)
		return
	}

	account := GetFirstAccount(c)
	userID := c.GetString(define.UserIDKey)
	if tenantAccountJoin.AccountID == account.ID || tenantAccountJoin.AccountID == userID {
		ErrorResponse(c, "INVALID_PARAMS", "不能删除自己以及超管账号", 400)
		return
	}

	result := database.DB.Where("id=?", tenantAccountJoin.ID).Delete(&models.TenantAccountJoin{})
	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}
	SuccessResponse(c, gin.H{"message": "删除成功"})
}

// UpdateTenantAccountRole godoc
// @Summary 更新团队账户关联角色
// @Description 更新团队账户关联中的角色
// @Tags 团队账户管理
// @Accept json
// @Produce json
// @Param updateInfo body models.UpdateTenantAccountRole true "角色更新信息"
// @Success 200 {object} view.Result{data=map[string]string} "更新角色成功"
// @Failure 200 {object} view.DifyError "错误响应"
// @Router /update_tenant_account_role.json [post]
// @Security ApiKeyAuth
func UpdateTenantAccountRole(c *gin.Context) {
	req := new(models.UpdateTenantAccountRole)
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", err.Error(), 400)
		return
	}

	tenantAccountJoin := new(models.TenantAccountJoin)
	if result := database.DB.Where("id = ?", req.ID).First(tenantAccountJoin); result.Error != nil || tenantAccountJoin.ID == "" {
		ErrorResponse(c, "INVALID_PARAMS", "未找到用户", 500)
		return
	}
	if err := checkCurrentUserPermission(c, tenantAccountJoin.TenantID, tenantAccountJoin.AccountID); err != nil {
		ErrorResponse(c, "PARAM_ERROR", err.Error(), 500)
		return
	}

	// 验证角色值是否有效
	if !view.ValidRoles[req.Role] {
		ErrorResponse(c, "INVALID_PARAMS", "无效的角色值", 400)
		return
	}
	//account := GetFirstAccount(c)
	//userID := c.GetString(define.UserIDKey)
	//if tenantAccountJoin.AccountID == account.ID || tenantAccountJoin.AccountID == userID {
	//	ErrorResponse(c, "INVALID_PARAMS", "只允许修改自己账号以下的权限", 400)
	//	return
	//}

	// 更新角色
	result := database.DB.Model(&models.TenantAccountJoin{}).
		Where("id=?", req.ID).
		Update("role", req.Role)

	if result.Error != nil {
		ErrorResponse(c, "DB_ERROR", result.Error.Error(), 500)
		return
	}

	if result.RowsAffected == 0 {
		ErrorResponse(c, "NOT_FOUND", "未找到指定的关联关系", 404)
		return
	}

	SuccessResponse(c, gin.H{"message": "角色更新成功"})
}

func checkCurrentUserPermission(c *gin.Context, tenantID, accountID string) error {
	taj := new(models.TenantAccountJoin)
	if result := database.DB.Model(&models.TenantAccountJoin{}).Where("tenant_id = ? AND account_id = ?", tenantID, c.GetString(define.UserIDKey)).First(taj); result.Error != nil {
		return errors.New("您没有权限操作该团队用户")
	}
	hasAccount := false
	reqInfo := new(models.UpdateTenantAccountRole)
	if accountID != "" {
		if result := database.DB.Model(&models.TenantAccountJoin{}).Where("tenant_id = ? AND account_id = ?", tenantID, accountID).First(taj); result.Error == nil {
			hasAccount = true
		}
	}

	if view.RolesNum[taj.Role] > 2 || (hasAccount && view.RolesNum[taj.Role] >= view.RolesNum[reqInfo.Role]) {
		return errors.New("您没有权限操作该团队用户")
	}
	return nil
}
