package handlers

import (
	"strconv"
	"time"

	"difyserver/database"
	"difyserver/models"
	"difyserver/utils/define"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetUserDocuments 获取所有用户文档列表
// @Summary 获取用户文档列表
// @Description 获取所有用户文档的列表信息（不包含内容）
// @Tags 用户文档管理
// @Accept json
// @Produce json
// @Success 200 {object} view.Result{data=[]models.UserDocumentListResponse}
// @Failure 500 {object} view.DifyError
// @Router /documents.json [get]
func GetUserDocuments(c *gin.Context) {
	dao := database.GetUserDocumentDao()
	documents, err := dao.GetAll()
	if err != nil {
		ErrorResponse(c, "DB_ERROR", "获取用户文档列表失败: "+err.Error(), 500)
		return
	}

	SuccessResponse(c, documents)
}

// GetUserDocumentByType 根据文档类型获取用户文档
// @Summary 根据文档类型获取用户文档
// @Description 根据文档类型获取用户文档数据列表
// @Tags 用户文档管理
// @Accept json
// @Produce json
// @Param doc_type path string true "文档类型"
// @Success 200 {object}  view.Result{data=[]models.UserDocumentResponse}
// @Failure 404 {object} view.DifyError
// @Failure 500 {object} view.DifyError
// @Router /documents/{doc_type} [get]
func GetUserDocumentByType(c *gin.Context) {
	docType := c.Param("doc_type")
	if docType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "文档类型不能为空", 400)
		return
	}

	dao := database.GetUserDocumentDao()
	documents, err := dao.GetListByDocType(docType)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", "获取用户文档失败: "+err.Error(), 500)
		return
	}

	// 如果没有找到文档，返回空列表而不是404错误
	if len(documents) == 0 {
		SuccessResponse(c, []*models.UserDocumentResponse{})
		return
	}

	SuccessResponse(c, documents)
}

// GetUserDocumentByID 根据ID获取用户文档详情
// @Summary 根据ID获取用户文档详情
// @Description 根据用户文档ID获取完整的用户文档内容
// @Tags 用户文档管理
// @Accept json
// @Produce json
// @Param id path int true "用户文档ID"
// @Success 200 {object} view.Result{data=models.UserDocumentResponse}
// @Failure 400 {object} view.DifyError
// @Failure 404 {object} view.DifyError
// @Failure 500 {object} view.DifyError
// @Router /documents/id/{id} [get]
func GetUserDocumentByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的用户文档ID", 400)
		return
	}

	dao := database.GetUserDocumentDao()
	document, err := dao.GetByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, "NOT_FOUND", "用户文档不存在", 404)
			return
		}
		ErrorResponse(c, "DB_ERROR", "获取用户文档失败: "+err.Error(), 500)
		return
	}

	response := &models.UserDocumentResponse{
		ID:        document.ID,
		DocType:   document.DocType,
		Content:   document.Content,
		CreatedBy: document.CreatedBy,
		CreatedAt: document.CreatedAt,
		UpdatedAt: document.UpdatedAt,
	}

	SuccessResponse(c, response)
}

// CreateUserDocument 创建用户文档
// @Summary 创建用户文档
// @Description 创建新的用户文档，只有超管可以操作
// @Tags 用户文档管理
// @Accept json
// @Produce json
// @Param request body models.UserDocumentRequest true "用户文档信息"
// @Success 200 {object} view.Result{data=models.UserDocumentResponse}
// @Failure 400 {object} view.DifyError
// @Failure 409 {object} view.DifyError
// @Failure 500 {object} view.DifyError
// @Router /documents.json [post]
func CreateUserDocument(c *gin.Context) {
	var req models.UserDocumentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "参数错误: "+err.Error(), 400)
		return
	}

	// 获取创建人邮箱
	createdBy := c.GetString(define.UserEmailKey)
	if createdBy == "" {
		ErrorResponse(c, "UNAUTHORIZED", "无法获取用户信息", 401)
		return
	}

	dao := database.GetUserDocumentDao()

	// 创建用户文档（移除唯一性检查，允许同一类型的多个文档）
	document := &models.UserDocument{
		DocType:   req.DocType,
		Content:   req.Content,
		CreatedBy: createdBy,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := dao.Create(document); err != nil {
		ErrorResponse(c, "DB_ERROR", "创建用户文档失败: "+err.Error(), 500)
		return
	}

	response := &models.UserDocumentResponse{
		ID:        document.ID,
		DocType:   document.DocType,
		Content:   document.Content,
		CreatedBy: document.CreatedBy,
		CreatedAt: document.CreatedAt,
		UpdatedAt: document.UpdatedAt,
	}

	SuccessResponse(c, response)
}

// UpdateUserDocumentByID 根据ID更新用户文档
// @Summary 根据ID更新用户文档
// @Description 根据用户文档ID更新用户文档内容，只有超管可以操作
// @Tags 用户文档管理
// @Accept json
// @Produce json
// @Param id path int true "用户文档ID"
// @Param request body models.UserDocumentUpdateByIDRequest true "更新内容"
// @Success 200 {object} view.Result{data=models.UserDocumentResponse}
// @Failure 400 {object} view.DifyError
// @Failure 404 {object} view.DifyError
// @Failure 500 {object} view.DifyError
// @Router /documents/id/{id} [put]
func UpdateUserDocumentByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的用户文档ID", 400)
		return
	}

	var req models.UserDocumentUpdateByIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "参数错误: "+err.Error(), 400)
		return
	}

	dao := database.GetUserDocumentDao()

	// 检查用户文档是否存在
	_, err = dao.GetByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, "NOT_FOUND", "用户文档不存在", 404)
			return
		}
		ErrorResponse(c, "DB_ERROR", "获取用户文档失败: "+err.Error(), 500)
		return
	}

	// 更新用户文档内容
	if err := dao.UpdateByID(id, req.Content); err != nil {
		ErrorResponse(c, "DB_ERROR", "更新用户文档失败: "+err.Error(), 500)
		return
	}

	// 重新获取更新后的文档
	updatedDocument, err := dao.GetByID(id)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", "获取更新后的用户文档失败: "+err.Error(), 500)
		return
	}

	response := &models.UserDocumentResponse{
		ID:        updatedDocument.ID,
		DocType:   updatedDocument.DocType,
		Content:   updatedDocument.Content,
		CreatedBy: updatedDocument.CreatedBy,
		CreatedAt: updatedDocument.CreatedAt,
		UpdatedAt: updatedDocument.UpdatedAt,
	}

	SuccessResponse(c, response)
}

// DeleteUserDocumentByType 根据文档类型删除用户文档（会同时删除所有该类型的文档）
// @Summary 删除用户文档（会同时删除所有该类型的文档）
// @Description 根据文档类型删除用户文档，只有超管可以操作
// @Tags 用户文档管理
// @Accept json
// @Produce json
// @Param doc_type path string true "文档类型"
// @Success 200 {object} view.Result{data=string}
// @Failure 400 {object} view.DifyError
// @Failure 404 {object} view.DifyError
// @Failure 500 {object} view.DifyError
// @Router /documents/{doc_type} [delete]
func DeleteUserDocumentByType(c *gin.Context) {
	docType := c.Param("doc_type")
	if docType == "" {
		ErrorResponse(c, "INVALID_PARAMS", "文档类型不能为空", 400)
		return
	}

	dao := database.GetUserDocumentDao()

	// 检查用户文档是否存在
	exists, err := dao.Exists(docType)
	if err != nil {
		ErrorResponse(c, "DB_ERROR", "检查用户文档失败: "+err.Error(), 500)
		return
	}
	if !exists {
		ErrorResponse(c, "NOT_FOUND", "用户文档不存在", 404)
		return
	}

	// 删除用户文档
	if err := dao.DeleteByDocType(docType); err != nil {
		ErrorResponse(c, "DB_ERROR", "删除用户文档失败: "+err.Error(), 500)
		return
	}

	SuccessResponse(c, "用户文档删除成功")
}

// DeleteUserDocument 根据ID删除用户文档
// @Summary 根据ID删除用户文档
// @Description 根据用户文档ID删除用户文档，只有超管可以操作
// @Tags 用户文档管理
// @Accept json
// @Produce json
// @Param id path int true "用户文档ID"
// @Success 200 {object} view.Result{data=string}
// @Failure 400 {object} view.DifyError
// @Failure 404 {object} view.DifyError
// @Failure 500 {object} view.DifyError
// @Router /documents/id/{id} [delete]
func DeleteUserDocument(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		ErrorResponse(c, "INVALID_PARAMS", "无效的用户文档ID", 400)
		return
	}

	dao := database.GetUserDocumentDao()

	// 检查用户文档是否存在
	_, err = dao.GetByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, "NOT_FOUND", "用户文档不存在", 404)
			return
		}
		ErrorResponse(c, "DB_ERROR", "检查用户文档失败: "+err.Error(), 500)
		return
	}

	// 删除用户文档
	if err := dao.Delete(id); err != nil {
		ErrorResponse(c, "DB_ERROR", "删除用户文档失败: "+err.Error(), 500)
		return
	}

	SuccessResponse(c, "用户文档删除成功")
}
