import {createRouter, createWebHashHistory, createWebHistory, RouteRecordRaw} from 'vue-router';
// import { usePermissStore } from '../store/permiss';
import Dashboard from '../views/dashboard.vue';
import Index from '../views/index.vue';
import Home from '../views/home.vue';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        meta: {
            title: '首页',
            noAuth: true
        },
        component: Index,
    },
    {
        path: '/aimain/:from',
        name: 'aimain',
        meta: {
            title: 'AI',
        },
        component: () => import(/* webpackChunkName: "aimain" */ '../views/aimain.vue'),
    },
    {
        path: '/botSetting',
        name: 'botSetting',
        meta: {
            title: 'AI设置',
        },
        component: () => import(/* webpackChunkName: "botSetting" */ '../views/botSetting.vue'),
    },
    {
        path: '/flowSetting',
        name: 'flowSetting',
        meta: {
            title: 'AI设置',
        },
        component: () => import(/* webpackChunkName: "flowSetting" */ '../views/flowSetting.vue'),
    },
    {
        path: '/explanation',
        name: 'explanation',
        meta: {
            title: '文档',
        },
        component: () => import(/* webpackChunkName: "botSetting" */ '../views/explanation.vue'),
    },
    {
        path: '/knowledgeUpload',
        name: 'knowledgeUpload',
        meta: {
            title: '知识库上传文件',
        },
        component: () => import(/* webpackChunkName: "knowledgeUpload" */ '../views/robots/knowledgeUpload.vue'),
    },
    {
        path: '/',
        name: 'Home',
        component: Home,
        children: [
            {
                path: '/home',
                name: 'home',
                meta: {
                    title: '首页',
                },
                component: Dashboard,
            },
            {
                path: '/knowledges',
                name: 'knowledges',
                meta: {
                    title: '知识库',
                },
                component: () => import(/* webpackChunkName: "knowledges" */ '../views/robots/knowledges.vue'),
            },
            {
                path: '/myFeedbacks',
                name: 'myFeedbacks',
                meta: {
                    title: '我的反馈',
                },
                component: () => import(/* webpackChunkName: "myFeedbacks" */ '../views/myFeedbacks.vue'),
            },
            {
                path: '/feedbacks',
                name: 'feedbacks',
                meta: {
                    title: '我的反馈',
                },
                component: () => import(/* webpackChunkName: "feedbacks" */ '../views/feedbacks.vue'),
            },
            {
                path: '/myAgents',
                name: 'myAgents',
                meta: {
                    title: '我的bot',
                },
                component: () => import(/* webpackChunkName: "myAgents" */ '../views/robots/myAgents.vue'),
            },
            {
                path: '/knowledgeManagementItem',
                name: 'knowledgeManagementItem',
                meta: {
                    title: '知识库详情',
                },
                component: () => import(/* webpackChunkName: "knowledgeManagementItem" */ '../views/robots/knowledgeManagementItem.vue'),
            },

            {
                path: '/system-user',
                name: 'system-user',
                meta: {
                    title: '用户管理',
                    permiss: '11',
                },
                component: () => import(/* webpackChunkName: "user" */ '../views/system/user.vue'),
            },
            {
                path: '/agent',
                name: 'agent',
                meta: {
                    title: '机器人管理',
                    permiss: '21',
                },
                component: () => import(/* webpackChunkName: "index" */ '../views/robots/index.vue'),
            },
            {
                path: '/knowledgeManagement',
                name: 'knowledgeManagement',
                meta: {
                    title: '知识库管理',
                    permiss: '21',
                },
                component: () => import(/* webpackChunkName: "knowledgeManagement" */ '../views/robots/knowledgeManagement.vue'),
            },
            {
                path: '/conversationManagement',
                name: 'conversationManagement',
                meta: {
                    title: '对话管理',
                    permiss: '21',
                },
                component: () => import(/* webpackChunkName: "conversationManagement" */ '../views/robots/conversationManagement.vue'),
            },
            {
                path: '/feedbackManagement',
                name: 'feedbackManagement',
                meta: {
                    title: '反馈管理',
                    permiss: '21',
                },
                component: () => import(/* webpackChunkName: "feedbackManagement" */ '../views/robots/feedbackManagement.vue'),
            },
            {
                path: '/messageManagement',
                name: 'messageManagement',
                meta: {
                    title: '对话内容管理',
                    permiss: '21',
                },
                component: () => import(/* webpackChunkName: "messageManagement" */ '../views/robots/messageManagement.vue'),
            },
            {
                path: '/modelManagement',
                name: 'modelManagement',
                meta: {
                    title: '模型管理',
                    permiss: '21',
                },
                component: () => import(/* webpackChunkName: "modelManagement" */ '../views/robots/modelManagement.vue'),
            },
            {
                path: '/system-role',
                name: 'system-role',
                meta: {
                    title: '角色管理',
                    permiss: '12',
                },
                component: () => import(/* webpackChunkName: "role" */ '../views/system/role.vue'),
            },
            {
                path: '/tenants',
                name: 'tenants',
                meta: {
                    title: '团队管理',
                    permiss: '12',
                },
                component: () => import(/* webpackChunkName: "tenants" */ '../views/system/tenants.vue'),
            },
            {
                path: '/tenant-user',
                name: 'tenant-user',
                meta: {
                    title: '团员管理',
                    permiss: '12',
                },
                component: () => import(/* webpackChunkName: "tenants-user" */ '../views/system/tenant-user.vue'),
            },
            {
                path: '/system-menu',
                name: 'system-menu',
                meta: {
                    title: '菜单管理',
                    permiss: '13',
                },
                component: () => import(/* webpackChunkName: "menu" */ '../views/robots/menu.vue'),
            },
            {
                path: '/user',
                name: 'user',
                meta: {
                    title: '用户管理',
                    permiss: '13',
                },
                component: () => import(/* webpackChunkName: "users" */ '../views/system/user.vue'),
            },
            {
                path: '/ucenter',
                meta: {
                    title: '个人中心',
                    noAuth: true,
                },
                component: () => import(/* webpackChunkName: "ucenter" */ '../views/pages/ucenter.vue'),
            },
            {
                path: '/theme',
                name: 'theme',
                meta: {
                    title: '主题设置',
                    permiss: '7',
                },
                component: () => import(/* webpackChunkName: "theme" */ '../views/pages/theme.vue'),
            },
        ],
    },
    {
        path: '/login',
        meta: {
            title: '登录',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "login" */ '../views/pages/login.vue'),
    },

    {
        path: '/403',
        meta: {
            title: '没有权限',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "403" */ '../views/pages/403.vue'),
    },
    {
        path: '/404',
        meta: {
            title: '找不到页面',
            noAuth: true,
        },
        component: () => import(/* webpackChunkName: "404" */ '../views/pages/404.vue'),
    },
    { path: '/:path(.*)', redirect: '/404' },
];

const router = createRouter({
    history: createWebHistory(),
    // history: createWebHashHistory(),
    routes,
});

router.beforeEach((to, from, next) => {
    NProgress.start();
    const token = localStorage.getItem('console_token');
    console.log('to:', to);
    if (!token && to.meta.noAuth !== true) {
        console.log('没有 token，跳转到登录页');
        next('/login');
    } else {
        console.log('有 token 或者已经在登录页，继续跳转');
        next();
    }
});

router.afterEach(() => {
    NProgress.done();
});

export default router;
