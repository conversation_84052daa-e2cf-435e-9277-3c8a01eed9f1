* {
    margin: 0;
    padding: 0;
    outline: 0 !important;
}

/* 定义PingFang SC字体 */
@font-face {
    font-family: 'PingFang SC';
    src: url('../../assets/fonts/PingFang-SC-Regular.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap; /* 避免阻塞渲染 */
}

@font-face {
    font-family: 'PingFang SC Bold';
    src: url('../../assets/fonts/PingFang-SC-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap; /* 避免阻塞渲染 */
}

body {
    font-family: 'PingFang SC Bold', 'Helvetica Neue', Helvetica, 'microsoft yahei', arial, STHeiTi, sans-serif;
    background-color: #F7F7FA;
}

#app {
    background-color: #fff;
}

a {
    text-decoration: none;
}

i {
    font-style: normal;
}

.container {
    padding: 30px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.el-table th {
    background-color: #f5f7fa !important;
}

.plugins-tips {
    padding: 20px 10px;
    margin-bottom: 20px;
    background: #eef1f6;
}

.plugins-tips a {
    color: var(--el-color-primary);
}

.el-button + .el-system_prompt {
    margin-left: 10px;
}

.mgb20 {
    margin-bottom: 20px;
}

.mgb10 {
    margin-bottom: 10px;
}

.mr10 {
    margin-right: 10px;
}

.move-enter-active,
.move-leave-active {
    transition: opacity 0.1s ease;
}

.move-enter-from,
.move-leave-to {
    opacity: 0;
}

.el-time-panel__content::after,
.el-time-panel__content::before {
    margin-top: -7px;
}

.el-time-spinner__wrapper .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
    padding-bottom: 0;
}

[hidden] {
    display: none !important;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

:root {
    --header-bg-color: #242f42;
    --header-text-color: #fff;
    --active-color: var(--el-color-primary);
}

/* 经典主题样式 */
[data-theme="classic"] {
    --header-bg-color: #ffffff;
    --header-text-color: #000000;
    --sidebar-bg-color: #ffffff;
    --sidebar-text-color: #000000;
    --el-bg-color: #ffffff; /* Element Plus背景色 */
    --el-text-color-primary: #000000; /* Element Plus主文字色 */

    /* 边框颜色调整 */
    --el-border-color-light: #e4e7ed;

    /* 卡片阴影调整 */

    .el-card {
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }

    /* 表格样式调整 */

    .el-table {
        --el-table-border-color: #e4e7ed;
        --el-table-header-bg-color: #f5f7fa;
    }

}


.el-menu-item.is-active {
    color: #2C30B5 !important;
}

.el-link {
    color: #2C30B5 !important;
}

.el-checkbox.el-checkbox--large .el-checkbox__label {
    color: #2C30B5 !important;
}

:root {
    --el-color-primary: rgb(81, 71, 255) !important;
}

.el-button:hover {
    background-color: #2C30B5 !important; /* 修改按钮悬停背景色 */
    border-color: #2C30B5 !important;
    color: #fff !important;
}

.el-sub-menu__title {
    height: 32px !important;
}

.el-menu-item {
    height: 32px !important;
}

.el-menu {
    border-right: none !important;
}

.layout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 10px;
    font-size: 20px;
}

.el-table .el-table__cell {
    padding: 10px !important;
}

.el-dialog {
    padding: 16px 24px !important;
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.is-success, .is-process {
    color: rgb(77, 83, 232) !important;
}

.backIcon {
    padding: 8px;
    cursor: pointer;
    font-size: 16px !important;
}

.hoverIcon {
    padding: 2px 8px;
}

.hoverIcon:hover {
    cursor: pointer;
    background-color: rgb(234, 236, 240);
}

.backIcon:hover {
    background-color: rgba(87, 104, 161, .08);
    border-radius: 8px;
}

.flex {
    display: flex;
}

.justify-start {
    justify-content: start;
}

.justify-end {
    justify-content: flex-end;
}

.items-center {
    align-items: center;
}

.mb-1 {
    margin-bottom: 5px;
}

.mb-2 {
    margin-bottom: 10px;
}
.mb-4 {
    margin-bottom: 20px;
}

.mr-1 {
    margin-right: 5px;
}

.ml-1 {
    margin-left: 5px;
}

.mt-1 {
    margin-top: 5px;
}

.mt-2 {
    margin-top: 10px;
}

.mr-2 {
    margin-right: 10px;
}

.justify-between {
    justify-content: space-between;
}

.gap-4 {
    gap: 1rem; /* 作用于 flex/grid 容器 */
}

.gap-2 {
    gap: 0.5rem; /* 作用于 flex/grid 容器 */
}

.mb-4 {
    margin-bottom: 1rem; /* 下外边距 16 px */
}

.type-grid {
    display: grid; /* 网格布局 */
    grid-template-columns: repeat(auto-fill, /* 根据宽度自动换行 */ minmax(110px, 1fr));
    gap: 12px; /* 行列间距 */
}

.font-12 {
    font-size: 12px !important;
}

.font-13 {
    font-size: 13px !important;
}

.font-14 {
    font-size: 14px !important;
}

.w-15 {
    width: 15px;
}

.w-20 {
    width: 20px;
}

.justify-center {
    justify-content: center;
}

.justify-around {
    justify-content: space-around;
}

.cursor {
    cursor: pointer;
}

.m-2 {
    margin: 10px;
}

.mt-4 {
    margin-top: 20px;
}

.flex-1 {
    flex: 1;
}

.float-right {
    float: right;
}

.gray {
    color: #676f83;
}

.orange {
    color: #f79009;
}

.blue{
    color: blue;
}

.green{
    color: green;
}

.purple{
    color: purple;
}
.box-shadow{
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, .08), 0 8px 24px 0 rgba(0, 0, 0, .04);
    border-color: rgba(82, 100, 154, 0.13);
}