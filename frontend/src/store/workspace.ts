/* src/store/workspace.ts */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/** 工作空间实体类型 */
import {Workspace} from "@/types/workspace";
/** 工作空间 Store */
export const useWorkspaceStore = defineStore('workspace', () => {
    /* ----------------------- state ----------------------- */
    /** 所有工作空间列表 */
    const list = ref<Workspace[]>([])

    /** 当前工作空间 ID（可空；第一次 setList 时自动选定） */
    const currentId = ref<number | null>(null)

    /* ----------------------- getters --------------------- */
    /** 当前工作空间对象（找不到返回 null） */
    const current = computed<Workspace | null>(() =>
        list.value.find(ws => ws.id === currentId.value) ?? null
    )

    /* ----------------------- actions --------------------- */
    /**
     * 批量设置工作空间列表；第一次调用时会自动把
     * `current === true` 的那一项或第一项设为当前工作空间
     */
    const setList = (wsList: Workspace[]) => {
        list.value = wsList

        // 首次初始化 currentId
        if (currentId.value === null && wsList.length) {
            currentId.value = (wsList.find(w => w.current) ?? wsList[0]).id
        }
    }

    /** 新增一个工作空间 */
    const addWorkspace = (ws: Workspace) => {
        list.value.push(ws)
    }

    /** 修改工作空间基本信息 */
    const updateWorkspace = (ws: Workspace) => {
        const idx = list.value.findIndex(w => w.id === ws.id)
        if (idx !== -1) list.value[idx] = { ...list.value[idx], ...ws }
    }

    /** 删除工作空间；若删的是当前项则自动切到列表第一项 */
    const removeWorkspace = (id: number) => {
        list.value = list.value.filter(w => w.id !== id)
        if (currentId.value === id) {
            currentId.value = list.value.length ? list.value[0].id : null
        }
    }

    /** 手动切换当前工作空间 */
    const setCurrent = (id: number) => {
        if (list.value.some(w => w.id === id)) currentId.value = id
    }

    return {
        /* state */
        list,
        currentId,

        /* getters */
        current,

        /* actions */
        setList,
        addWorkspace,
        updateWorkspace,
        removeWorkspace,
        setCurrent,
    }
})
