import {defineStore} from 'pinia';
import {ref, computed} from 'vue';

export interface User {
    id: number;
    name: string;
    email: string;
    avatar: string;
    avatar_url: string;
    unit?: string;
    Department?: string;
}

export const useUserStore = defineStore('user', () => {
    // state
    const user = ref<User | null>();

    const workspace = ref({});
    const defaultModel = ref({});
    const userRole = ref('');

    // getters
    const isLoggedIn = computed(() => user.value !== null);
    const avatar_url = computed(() => `${import.meta.env.VITE_SERVE_HOST}` + user.value?.avatar_url);
    const avatar = computed(() => user.value?.avatar || '');
    const userName = computed(() => user.value?.name || '');

    // actions
    const setUser = (userData: User) => {
        user.value = userData;
    }
    const setWorkspace = (workspaceData) => {
        workspace.value = workspaceData;
    }
    const setDefaultModel = (model) => {
        defaultModel.value = model;
    }
    const setUserAvatar = (avatar: string) => {
        user.value.avatar_url = avatar;
    }
    const setUserRole = (role: string) => {
        userRole.value = role;
    }

    return {
        user,
        isLoggedIn,
        avatar,
        avatar_url,
        userName,
        setUser,
        setUserAvatar,
        workspace,
        setWorkspace,
        defaultModel,
        setDefaultModel,
        userRole,
        setUserRole,
    };
});
