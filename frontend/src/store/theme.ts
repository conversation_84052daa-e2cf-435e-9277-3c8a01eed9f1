import { mix, setProperty } from '@/utils';
import { defineStore } from 'pinia';
import { useSidebarStore } from '@/store/sidebar';
export const useThemeStore = defineStore('theme', {
    state: () => {
        return {
            primary: '',
            success: '',
            warning: '',
            danger: '',
            info: '',
            headerBgColor: '#242f42',
            headerTextColor: '#fff',
            themeType: 'light'
        };
    },
    getters: {},
    actions: {
        initTheme() {
            const sidebar = useSidebarStore();

            const hasTheme = ['primary', 'success', 'warning', 'danger', 'info'].some((type) =>
                !!localStorage.getItem(`theme-${type}`)
            );
            const hasHeader = localStorage.getItem('header-bg-color') || localStorage.getItem('header-text-color');
            const hasSidebar = localStorage.getItem('sidebar-bg-color') || localStorage.getItem('sidebar-text-color');

            if (!hasTheme && !hasHeader && !hasSidebar) {
                // 👉 没有任何主题缓存时，加载 white（经典）主题
                this.setHeaderBgColor('#ffffff');       // 白色头部背景
                this.setHeaderTextColor('#000000');     // 黑色头部文字
                this.setPropertyColor('#409EFF', 'primary'); // 蓝色主色
                this.setPropertyColor('#909399', 'info');    // 灰色辅助色

                sidebar.setBgColor('#ffffff');          // 白色菜单背景
                sidebar.setTextColor('#000000');        // 黑色菜单文字
                return;
            }

            // 👉 加载 Element Plus 主题色缓存
            ['primary', 'success', 'warning', 'danger', 'info'].forEach((type) => {
                const color = localStorage.getItem(`theme-${type}`);
                if (color) {
                    this.setPropertyColor(color, type);
                }
            });

            // 👉 加载头部缓存
            const headerBgColor = localStorage.getItem('header-bg-color');
            headerBgColor && this.setHeaderBgColor(headerBgColor);

            const headerTextColor = localStorage.getItem('header-text-color');
            headerTextColor && this.setHeaderTextColor(headerTextColor);

            // 👉 加载菜单栏缓存
            const sidebarBgColor = localStorage.getItem('sidebar-bg-color');
            sidebarBgColor && sidebar.setBgColor(sidebarBgColor);

            const sidebarTextColor = localStorage.getItem('sidebar-text-color');
            sidebarTextColor && sidebar.setTextColor(sidebarTextColor);
        },
        resetTheme() {
            ['primary', 'success', 'warning', 'danger', 'info'].forEach((type) => {
                this.setPropertyColor('', type); // 重置主题色
            });
        },
        setPropertyColor(color: string, type: string = 'primary') {
            this[type] = color;
            setProperty(`--el-color-${type}`, color);
            localStorage.setItem(`theme-${type}`, color);
            this.setThemeLight(type);
        },
        setThemeLight(type: string = 'primary') {
            [3, 5, 7, 8, 9].forEach((v) => {
                setProperty(`--el-color-${type}-light-${v}`, mix('#ffffff', this[type], v / 10));
            });
            setProperty(`--el-color-${type}-dark-2`, mix('#ffffff', this[type], 0.2));
        },
        setHeaderBgColor(color: string) {
            this.headerBgColor = color;
            setProperty('--header-bg-color', color);
            localStorage.setItem(`header-bg-color`, color);
        },
        setHeaderTextColor(color: string) {
            this.headerTextColor = color;
            setProperty('--header-text-color', color);
            localStorage.setItem(`header-text-color`, color);
        }
    }
});