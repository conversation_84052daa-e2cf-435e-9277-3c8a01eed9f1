import request from "@/utils/request";
import request2 from "@/utils/request2";

export const loginApi = (data) => {
    return request({
        url: '/login',
        method: 'post',
        data
    });
};
export const registerApi = (data) => {
    return request2({
        url: '/add_account.json',
        method: 'post',
        data
    });
};
// 获取用户列表（支持分页）
export const getUsersApi = (params) => {
    return request2({
        url: '/accounts.json',
        method: 'get',
        params
    });
};

// 获取单个用户
export const getUserApi = (id) => {
    return request({
        url: `/users/${id}`,
        method: 'get'
    });
};

// 创建用户
export const createUserApi = (data) => {
    return request({
        url: '/users',
        method: 'post',
        data
    });
};

// 更新用户
export const updateUserApi = (data) => {
    return request2({
        url: `/update_account_info.json`,
        method: 'post',
        data
    });
};

// 删除用户
export const deleteUserApi = (userId) => {
    return request2({
        url: `/del_account.json`,
        method: 'post',
        data:{
            id:userId
        }
    });
};
// 获取当前用户
export const getNowUser = () => {
    return request({
        url: `/account/profile`,
        method: 'get'
    });
};
// 修改用户密码
export const changePassword = (data) => {
    return request({
        url: `/account/password`,
        method: 'post',
        data
    });
};

// 获取所有角色
export const getRolesApi = () => {
    return [
        {
            "name": "超管",
            "value": "owner",
            "description": "拥有最高权限，可以管理所有资源和成员，包括添加、修改、删除和分配权限。"
        },
        {
            "name": "管理者",
            "value": "admin",
            "description": "负责日常管理工作，可以管理成员、配置项目设置，但无法删除所有者。"
        },
        {
            "name": "编辑者",
            "value": "editor",
            "description": "可以编辑内容和资源，但不能修改权限和删除成员。"
        },
        {
            "name": "普通员工",
            "value": "normal",
            "description": "仅限访问和查看权限，不能修改内容或管理其他成员。"
        }
    ];
};

// 获取所有权限
export const getPermissionsApi = () => {
    return request({
        url: '/rbac/permissions',
        method: 'get'
    });
};

// 为角色分配权限
export const assignPermissionsApi = (roleId, data) => {
    return request({
        url: `/rbac/roles/${roleId}/permissions`,
        method: 'post',
        data
    });
};

// 获取用户的角色和权限
export const getUserRolesPermissionsApi = () => {
    return request({
        url: '/rbac/user/roles-permissions',
        method: 'get'
    });
};

// 为用户分配角色
export const assignRolesApi = (userId, data) => {
    return request({
        url: `/rbac/users/${userId}/roles`,
        method: 'post',
        data
    });
};

// Menu API

// 获取所有菜单
export const getMenusApi = () => {
    return request({
        url: '/system-menus',
        method: 'get'
    });
};

// 获取单个菜单
export const getMenuApi = (menuId) => {
    return request({
        url: `/system-menus/${menuId}`,
        method: 'get'
    });
};

// 创建菜单
export const createMenuApi = (data) => {
    return request({
        url: '/system-menus',
        method: 'post',
        data
    });
};

// 更新菜单
export const updateMenuApi = (menuId, data) => {
    return request({
        url: `/system-menus/${menuId}`,
        method: 'put',
        data
    });
};

// 删除菜单
export const deleteMenuApi = (menuId) => {
    return request({
        url: `/system-menus/${menuId}`,
        method: 'delete'
    });
};
//修改用户头像
export const setAvatar = (data) => {
    return request({
        url: `/account/avatar`,
        method: 'post',
        data
    });
};
//修改用户角色
export const changeRoleApi = (data) => {
    return request2({
        url: `/update_tenant_account_role.json`,
        method: 'post',
        data
    });
};
//修改用户密码
export const upUserPwd = (data) => {
    return request2({
        url: `/set_account_password.json`,
        method: 'post',
        data
    });
};