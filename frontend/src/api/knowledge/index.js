import request from "@/utils/request";
import request2 from "@/utils/request2";


//获取所有知识库
export const getKnowledgeBaseListApi = (params) => {
    return request({
        url: `/datasets`,
        method: 'get',
        params
    });
};
// 更新知识库条目
export const updateKnowledgeBaseApi = (id, data) => {
    return request({
        url: `/ai-knowledge-base/${id}`,
        method: 'put',
        data
    });
};
// 获取单个知识库条目
export const getKnowledgeBaseApi = (id) => {
    return request({
        url: `/ai-knowledge-base/${id}`,
        method: 'get'
    });
};

// 创建知识库条目
export const createKnowledgeBaseApi = (data) => {
    return request({
        url: '/ai-knowledge-base',
        method: 'post',
        data
    });
};

// 更新知识库条目
export const updateKnowledgeBase = (id, data) => {
    return request({
        url: `/datasets/${id}`,
        method: 'patch',
        data
    });
};

// 删除知识库条目
export const deleteKnowledgeBaseApi = (id) => {
    return request({
        url: `/datasets/${id}`,
        method: 'delete'
    });
};
// 知识库列表
export const knowledgeBaseList = (id) => {
    return request({
        url: `/datasets?page=1`,
        method: 'get'
    });
};
// 创建知识库
export const createKnowledgeBase = (data) => {
    return request({
        url: `/datasets`,
        method: 'post',
        data
    });
};
// 获取知识库
export const getKnowledgeBase = (id) => {
    return request({
        url: `/datasets/`+id,
        method: 'get',
    });
};
// 获取知识库
export const getKnowledgeBaseDocument = (data,id) => {
    return request({
        url: `/datasets/`+id +'/documents',
        method: 'get',
        params: data
    });
};
// 修改知识库
export const postKnowledgeBaseDocument = (data,id) => {
    return request({
        url: `/datasets/`+id +'/documents',
        method: 'post',
        data
    });
};
// 删除知识库
export const delKnowledgeBase = (id) => {
    return request({
        url: `datasets/${id}`,
        method: 'delete',
    });
};
// 删除文件
export const delDocument = (dbId,id) => {
    return request({
        url: `/datasets/${dbId}/documents?document_id=`+id,
        method: 'delete',
    });
};
// 解析excel
export const extractText = (id) => {
    return request({
        url: `/extractText/`+id,
        method: 'post',
    });
};
export const getMyKnowledge = (data) => {
    return request({
        url: '/datasets',
        method: 'get',
        data
    });
};

export const integrates = (id) => {
    return request({
        url: '/data-source/integrates',
        method: 'get',
    });
};
//获取切片设置
export const getProcessRules = () => {
    return request({
        url: '/datasets/process-rule',
        method: 'get',
    });
};
//获取rerank模型
export const getReranks = () => {
    return request({
        url: '/workspaces/current/models/model-types/rerank',
        method: 'get',
    });
};
//预览切片
export const getIndexingEstimate = (data) => {
    return request({
        url: '/datasets/indexing-estimate',
        method: 'post',
        data
    });
};
//获取默认rerank模型
export const getDefaultModel = () => {
    return request({
        url: '/workspaces/current/default-model?model_type=rerank',
        method: 'get',
        model_type:'rerank'
    });
};
//嵌入加载
export const indexingStatus = (dbId,batchId) => {
    return request({
        url: `/datasets/${dbId}/batch/${batchId}/indexing-status`,
        method: 'get',
    });
};
//获取embedding
export const textEmbedding = () => {
    return request({
        url: `/workspaces/current/models/model-types/text-embedding`,
        method: 'get',
    });
};
//知识库使用检查
export const dbUseCheck = (dbId) => {
    return request({
        url: `/datasets/${dbId}/use-check`,
        method: 'get',
    });
};