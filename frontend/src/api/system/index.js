import request from "@/utils/request";
import request2 from "@/utils/request2";
// 获取所有角色
export const getRolesApi = () => {
    return request({
        url: '/roles',
        method: 'get'
    });
};

// 获取所有权限
export const getPermissionsApi = () => {
    return request({
        url: '/permissions',
        method: 'get'
    });
};

// 为角色分配权限
export const assignPermissionsApi = (roleId, data) => {
    return request({
        url: `/roles/${roleId}/permissions`,
        method: 'post',
        data
    });
};

// 获取用户的角色和权限
export const getUserRolesPermissionsApi = () => {
    return request({
        url: '/user/roles-permissions',
        method: 'get'
    });
};

// 为用户分配角色
export const assignRolesApi = (userId, data) => {
    return request({
        url: `/users/${userId}/roles`,
        method: 'post',
        data
    });
};

// Menu API

// 获取所有菜单
export const getMenusApi = () => {
    return request2({
        url: '/menus.json',
        method: 'get',
    });
};

// 获取单个菜单
export const getMenuApi = (menuId) => {
    return request({
        url: `/system-menus/${menuId}`,
        method: 'get'
    });
};

// 创建菜单
export const createMenuApi = (data) => {
    return request({
        url: '/system-menus',
        method: 'post',
        data
    });
};

// 更新菜单
export const updateMenuApi = (menuId, data) => {
    return request({
        url: `/system-menus/${menuId}`,
        method: 'put',
        data
    });
};

// 删除菜单
export const deleteMenuApi = (menuId) => {
    return request({
        url: `/system-menus/${menuId}`,
        method: 'delete'
    });
};

//获取用户信息
export const getProfile = (menuId) => {
    return request({
        url: `/account/profile`,
        method: 'get'
    });
};
//获取当前团队
export const workspacesCurrent = () => {
    return request({
        url: `/workspaces/current`,
        method: 'get'
    });
};
//刷新token
export const refreshToken = (data) => {
    return request({
        url: `/refresh-token`,
        method: 'post',
        data
    });
};
//预览图片
export const previewFile = (file_id) => {
    return request({
        url: `/files/${file_id}/preview`,
        method: 'get',
    });
};
//退出登录
export const logout = () => {
    return request({
        url: `/logout`,
        method: 'get',
    });
};
//获取默认模型
export const getDefaultModel = () => {
    return request({
        url: `/workspaces/current/default-model?model_type=llm`,
        method: 'get',
    });
};
//获取当前工作区
export const getCurrentWorkspaces = () => {
    return request({
        url: `/workspaces/current`,
        method: 'get',
    });
};
//获取账户的关联
export const getAllAccount = (data) => {
    return request2({
        url: `/list_tenant_account_by_account.json`,
        method: 'get',
        data
    });
};
//获取应用状态
export const getAppStatus = () => {
    return request2({
        url: `/apps/status.json`,
        method: 'get',
    });
};
//获取点击数
export const getAppClicks = () => {
    return request2({
        url: `/anchor-statistics/resource-summary.json`,
        method: 'get',
        data:{
            resource_type:'app',
            action_type:'click',
        }
    });
};
//记录点击
//获取点击数
export const saveClick = (id) => {
    return request2({
        url: `/anchor-statistics/record.json`,
        method: 'post',
        data:{
            resource_type:'app',
            action_type:'click',
            resource_id:id
        }
    });
};

//上传文件
export const uploadFile = (file) => {
    return request({
        url: `/files/upload`,
        method: 'post',
        data:file
    });
};

//平台数据
export const getDashboard = (data) => {
    return request2({
        url: `/dashboard/summary`,
        method: 'get',
        data
    });
};