import request from "@/utils/request";

// 获取用户与机器人之间的所有聊天记录
export const getUserAgentConversationsApi = (params) => {
    return request({
        url: '/conversations/user-agent',
        method: 'get',
        params
    });
};

// 创建新的聊天记录
export const createConversationApi = (data) => {
    return request({
        url: '/conversations/create',
        method: 'post',
        data
    });
};

// 删除聊天记录
export const deleteConversationApi = (id) => {
    return request({
        url: `/conversations/delete/${id}`,
        method: 'delete'
    });
};
