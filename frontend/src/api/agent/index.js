import request from "@/utils/request";
import request2 from "@/utils/request2";

// 获取AI应用列表（支持分页）
export const getAgentsApi = (params) => {
    return request({
        url: '/apps',
        method: 'get',
        params
    });
};

// 获取单个AI应用
export const getAgentApi = (id) => {
    return request({
        url: `/apps/${id}`,
        method: 'get'
    });
};

// 创建AI应用
export const createAgentApi = (data) => {
    return request({
        url: '/apps',
        method: 'post',
        data
    });
};

// 更新AI应用
export const updateAgentApi = (id, data) => {
    return request({
        url: `/apps/${id}`,
        method: 'put',
        data
    });
};

// 删除AI应用
export const deleteAgentApi = (id) => {
    return request({
        url: `/apps/${id}`,
        method: 'delete'
    });
};

// 获取个人AI应用列表
export const getMyAgentsApi = (data) => {
    return request({
        url: '/apps',
        method: 'get',
        data
    });
};
// 复制应用
export const copyApp = (data, appId) => {
    return request({
        url: `/apps${appId}/copy`,
        method: 'post',
        data
    });
};
// 获取个人AI应用列表
export const generatePrompt = (data) => {
    return request({
        url: '/rule-generate',
        method: 'post',
        data
    });
};
// 点击数
export const incrementAgentClick = (agentId) => {
    return request({
        url: '/agents/' + agentId + '/click',
        method: 'post'
    });
};

export const toggleAgentPublicStatusApi = (id) => {
    return request({
        url: `/agents/${id}/toggle-public`,
        method: 'patch'
    });
};
//保存APP设置
export const setModelConfig = (appId, data) => {
    return request({
        url: `/apps/${appId}/model-config`,
        method: 'post',
        data
    });
};
//获取聊天记录
export const getMessagesHistory = (appId, conversation_id) => {
    return request({
        url: `/apps/${appId}/chat-messages?conversation_id=${conversation_id}`,
        method: 'get',
    });
};
//站点信息设置
export const setSiteImage = (appId, data) => {
    return request({
        url: `/apps/${appId}/site`,
        method: 'post',
        data
    });
};
//更改公开状态
export const setSiteStatus = (data) => {
    return request2({
        url: `/apps/update_only_me_status.json`,
        method: 'post',
        data
    });
};
//复制应用
export const copyAgentFuc = (appId,data) => {
    return request({
        url: `/apps/${appId}/copy`,
        method: 'post',
        data
    });
};
//导出应用
export const exportAgentFuc = (appId) => {
    return request({
        url: `/apps/${appId}/export?include_secret=false`,
        method: 'get',
    });
};
//导入应用
export const importAgentFuc = (data) => {
    return request({
        url: `/apps/imports`,
        method: 'post',
        data
    });
};
//获取工具
export const getTools = () => {
    return request({
        url: `/workspaces/current/tools/builtin`,
        method: 'get',
    });
};
//停止对话
export const stopChat = (appId, messageId) => {
    return request({
        url: `/apps/${appId}/chat-messages/${messageId}/stop`,
        method: 'POST',
    });
};
//获取工具详情
export const getToolInfo = (tool) => {
    return request({
        url: `/workspaces/current/tool-provider/builtin/${tool}/tools`,
        method: 'get',
    });
};
//获取应用会话统计数据
export const getDailyConversations = (params) => {
    return request({
        url: `/apps/${params.appId}/statistics/daily-conversations`,
        method: 'get',
        params: {
            start: params.start,
            end: params.end
        }
    });
};
//获取应用活跃用户数统计数据
export const getDailyEndUser = (params) => {
    return request({
        url: `/apps/${params.appId}/statistics/daily-end-users`,
        method: 'get',
        params: {
            start: params.start,
            end: params.end
        }
    });
};
//获取应用平均会话互动数统计数据
export const getDailyAvgSession = (params) => {
    return request({
        url: `/apps/${params.appId}/statistics/average-session-interactions`,
        method: 'get',
        params: {
            start: params.start,
            end: params.end
        }
    });
};
//获取应用平均响应
export const getDailyAvgResponseTime = (params) => {
    return request({
        url: `/apps/${params.appId}/statistics/average-response-time`,
        method: 'get',
        params: {
            start: params.start,
            end: params.end
        }
    });
};
//获取应用token输出速度统计数据
export const getDailyToken = (params) => {
    return request({
        url: `/apps/${params.appId}/statistics/tokens-per-second`,
        method: 'get',
        params: {
            start: params.start,
            end: params.end
        }
    });
};