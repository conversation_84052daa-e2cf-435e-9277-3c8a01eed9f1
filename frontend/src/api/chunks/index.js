import request from "@/utils/request";

// 获取知识库列表（支持分页）
export const getChunksByDocumentId = (dbId,dcId,data) => {
    return request({
        url: '/datasets/'+dbId+'/documents/' +dcId + '/segments',
        method: 'get',
        data
    });
};
// 切片
export const process = (id) => {
    return request({
        url: '/knowledge-bases/' + id + '/process',
        method: 'post',
    });
};
// 修改切片
export const editChunks = (data,dbId,dcId,id) => {
    return request({
        url: `/datasets/${dbId}/documents/${dcId}/segments/${id}`,
        method: 'PATCH',
        data
    });
};
// 删除切片
export const delChunks = (dbId,dcId,id) => {
    return request({
        url: `/datasets/${dbId}/documents/${dcId}/segments?segment_id=${id}`,
        method: 'delete',
    });
};

//禁用切片状态
export const changeChunksStatus = (dbId,dcId,id) => {
    return request({
        url: `/datasets/${dbId}/documents/${dcId}/segment/disable?segment_id=${id}`,
        method: 'PATCH',
    });
};
//启用切片状态
export const changeChunksStatusTrue = (dbId,dcId,id) => {
    return request({
        url: `/datasets/${dbId}/documents/${dcId}/segment/enable?segment_id=${id}`,
        method: 'PATCH',
    });
};