import request from "@/utils/request";
import request2 from "@/utils/request2";

export const getTags = () => {
    return request({
        url: '/tags?type=app',
        method: 'get',
    });
};

export const addTag = (data) => {
    return request({
        url: '/tags',
        method: 'post',
        data
    });
};

export const appBindTag = (data) => {
    return request({
        url: '/tag-bindings/create',
        method: 'post',
        data
    });
};

export const appDelTag = (data) => {
    return request({
        url: '/tag-bindings/remove',
        method: 'post',
        data
    });
};

export const delTag = (id) => {
    return request({
        url: '/tags/' + id,
        method: 'delete',
    });
};

export const updateTag = (id, name) => {
    return request({
        url: '/tags/' + id,
        method: 'PATCH',
        data: {name: name}
    });
};

export const setTagIds = (ids) => {
    return request({
        url: '/apps?tagIDs=' + ids,
        method: 'get',
    });
};