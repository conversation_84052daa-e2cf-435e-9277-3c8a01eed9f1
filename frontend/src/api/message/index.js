import request from "@/utils/request";

// 获取对话中的消息列表（支持分页）
export const getMessagesApi = (params) => {
    return request({
        url: '/messages',
        method: 'get',
        params
    });
};

// 发送用户消息
export const sendUserMessageApi = (conversationId, data) => {
    return request({
        url: `/conversations/${conversationId}/messages`,
        method: 'post',
        data
    });
};

// 删除消息
export const deleteMessageApi = (conversationId, messageId) => {
    return request({
        url: `/conversations/${conversationId}/messages/${messageId}`,
        method: 'delete'
    });
};