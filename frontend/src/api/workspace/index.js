import request from "@/utils/request";
import request2 from "@/utils/request2";

//本人工作空间列表
export const getWorkspaces = () => {
    return request({
        url: '/workspaces',
        method: 'get',
    });
};
//切换工作空间
export const switchWorkspaces = (id) => {
    return request({
        url: '/workspaces/switch',
        method: 'post',
        data:{
            tenant_id:id
        }
    });
};
//获取工作空间列表
export const getWorkspacesList = (params) => {
    return request2({
        url: '/tenants.json',
        method: 'get',
        params
    });
};
//添加团队
export const addWorkspaces = (data) => {
    return request2({
        url: '/add_tenant.json',
        method: 'post',
        data
    });
};
//获取关联关系列表
export const getTenantAccountList = (params) => {
    return request2({
        url: '/list_tenant_account_by_tenant.json',
        method: 'get',
        params
    });
};
//获取有权限加入的团队
export const getCanjoinTenant = () => {
    return request2({
        url: '/list_can_join_tenant.json',
        method: 'get',
    });
};
//获取可加入的账号
export const getCanjoinAccount = (tenant_id) => {
    return request2({
        url: '/list_tenant_can_join_account.json',
        method: 'get',
        data:{
            tenant_id:tenant_id
        }
    });
};
//添加关系
export const addRelationApi = (data) => {
    return request2({
        url: '/add_tenant_account.json',
        method: 'post',
        data
    });
};
//删除关系
export const delRelationApi = (id) => {
    return request2({
        url: '/del_tenant_account.json',
        method: 'post',
        data:{
            id
        }
    });
};
//删除团队
export const delTenantApi = (id) => {
    return request2({
        url: '/del_tenant.json',
        method: 'post',
        data:{
            tenant_id:id
        }
    });
};