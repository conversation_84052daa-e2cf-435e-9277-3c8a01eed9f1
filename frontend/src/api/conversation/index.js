import request from "@/utils/request";

// 获取对话列表（支持分页）
export const getConversationsApi = (params) => {
    return request({
        url: '/conversations',
        method: 'get',
        params
    });
};

// 创建新对话
export const createConversationApi = () => {
    return request({
        url: '/conversations',
        method: 'post'
    });
};

// 获取单个对话详情
export const getConversationApi = (id) => {
    return request({
        url: `/conversations/${id}`,
        method: 'get'
    });
};

// 结束对话
export const endConversationApi = (id) => {
    return request({
        url: `/conversations/${id}/end`,
        method: 'post'
    });
};

// 删除对话
export const deleteConversationApi = (id) => {
    return request({
        url: `/conversations/${id}`,
        method: 'delete'
    });
};

// 更新对话
export const updateConversationApi = (id, data) => {
    return request({
        url: `/conversations/${id}`,
        method: 'put',
        data
    });
};