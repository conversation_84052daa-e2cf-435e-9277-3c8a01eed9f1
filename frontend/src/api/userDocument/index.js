import request from "@/utils/request";
import request2 from "@/utils/request2";

export const getDocuments = () => {
    return request2({
        url: '/documents.json',
        method: 'get',
    });
};

export const getDocumentsByDoctype = (doc_type) => {
    return request2({
        url: `/documents/${doc_type}`,
        method: 'get',
    });
};

export const putDocumentsByDoctype = (doc_type,content) => {
    return request2({
        url: `/documents/${doc_type}`,
        method: 'put',
        data:{
            content
        }
    });
};

export const addDocuments = (data) => {
    return request2({
        url: `/documents.json`,
        method: 'post',
        data
    });
};

export const getDocumentsByID = (id) => {
    return request2({
        url: `/documents/id/${id}`,
        method: 'get',
    });
};

export const upDocuments = (id,content) => {
    return request2({
        url: `/documents/id/${id}`,
        method: 'put',
        data:{
            content
        }
    });
};

export const delDocuments = (id) => {
    return request2({
        url: `/documents/id/${id}`,
        method: 'delete',
    });
};