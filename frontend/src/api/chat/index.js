import request from "@/utils/request";

// 获取AI应用列表（支持分页）
// export const createChat = (data) => {
//     return request({
//         url: '/create-chat',
//         method: 'post',
//         data,
//         responseType: 'stream'
//     });
// };

export const clearChatHistory = (data) => {
    return request({
        url: '/clear-chat-history',
        method: 'post',
        data
    });
};

export const getSuggesstions = (appId,messageId) => {
    return request({
        url: `/apps/${appId}/chat-messages/${messageId}/suggested-questions`,
        method: 'get',
    });
};

export const streamChat = async (data, onMessage) => {
    const host = import.meta.env.VITE_SERVE_HOST || '127.0.0.1';
    const response = await fetch(`${host}/api/create-chat`, {
    // const response = await fetch('http://localhost:8000/api/create-chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('console_token')}`
        },
        body: JSON.stringify(data)
    });

    const reader = response.body && response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed) continue;

            const cleanLine = trimmed.startsWith('data:')
                ? trimmed.replace(/^data:\s*/, '')
                : trimmed;

            try {
                JSON.parse(cleanLine);
                onMessage(cleanLine);
            } catch (e) {
                console.warn('[跳过非完整 JSON]', cleanLine);
            }
        }
    }
};


// export const streamTempChat = (data,appId) => {
//     return request({
//         url: '/apps/' + appId + '/chat-messages',
//         method: 'post',
//         data,
//     });
// };

export const streamTempChat = async (data, onMessage) => {
    // const host = import.meta.env.VITE_SERVE_HOST || '127.0.0.1';
    const response = await fetch(`/console/api/apps/${data?.model_config?.appId}/chat-messages`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('console_token')}`
        },
        body: JSON.stringify(data)
    });

    const reader = response.body && response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed) continue;

            const cleanLine = trimmed.startsWith('data:')
                ? trimmed.replace(/^data:\s*/, '')
                : trimmed;

            try {
                JSON.parse(cleanLine);
                onMessage(cleanLine);
            } catch (e) {
                console.warn('[跳过非完整 JSON]', cleanLine);
            }
        }
    }
};
export const streamTempChat2 = async (data, onMessage) => {
    // const host = import.meta.env.VITE_SERVE_HOST || '127.0.0.1';
    const response = await fetch(`/console/api/apps/${data?.model_config?.appId}/completion-messages`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('console_token')}`
        },
        body: JSON.stringify(data)
    });

    const reader = response.body && response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed) continue;

            const cleanLine = trimmed.startsWith('data:')
                ? trimmed.replace(/^data:\s*/, '')
                : trimmed;

            try {
                JSON.parse(cleanLine);
                onMessage(cleanLine);
            } catch (e) {
                console.warn('[跳过非完整 JSON]', cleanLine);
            }
        }
    }
};

