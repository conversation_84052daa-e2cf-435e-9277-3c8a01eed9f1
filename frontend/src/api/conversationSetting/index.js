import request from "@/utils/request";



export const getSettingsApi = (params) => {
    return request({
        url: '/settings',
        method: 'get',
        params
    });
};
// 获取对话设置
export const getConversationSettingsApi = (conversationId) => {
    return request({
        url: `/conversations/${conversationId}/settings`,
        method: 'get'
    });
};

// 更新对话设置
export const updateConversationSettingsApi = (conversationId, data) => {
    return request({
        url: `/conversations/${conversationId}/settings`,
        method: 'put',
        data
    });
};