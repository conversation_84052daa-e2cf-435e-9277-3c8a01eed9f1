import request from "@/utils/request";

// 获取模型列表（支持分页）
export const getModelsApi = (params) => {
    return request({
        url: '/models',
        method: 'get',
        params
    });
};

// 创建新模型
export const createModelApi = (data) => {
    return request({
        url: '/models',
        method: 'post',
        data
    });
};

// 获取单个模型详情
export const getModelApi = (id) => {
    return request({
        url: `/models/${id}`,
        method: 'get'
    });
};

// 更新模型
export const updateModelApi = (id, data) => {
    return request({
        url: `/models/${id}`,
        method: 'put',
        data
    });
};

// 删除模型
export const deleteModelApi = (id) => {
    return request({
        url: `/models/${id}`,
        method: 'delete'
    });
};