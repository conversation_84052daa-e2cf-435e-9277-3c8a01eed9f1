import request from "@/utils/request";
import request2 from "@/utils/request2";

// 用户提交反馈
export const submitFeedbackApi = (data) => {
    return request2({
        url: '/feedbacks.json',
        method: 'post',
        data
    });
};

// 用户查看自己的反馈列表
export const getMyFeedbacksApi = () => {
    return request2({
        url: '/user/feedbacks.json',
        method: 'get'
    });
};

// 管理员查看所有反馈
export const getAllFeedbacksApi = () => {
    return request2({
        url: '/feedbacks.json',
        method: 'get'
    });
};

// 查看单个反馈（用户或管理员）
export const getFeedbackApi = (id) => {
    return request2({
        url: `/feedbacks/${id}/info.json`,
        method: 'get'
    });
};

// 管理员回复反馈
export const respondFeedbackApi = (id, data) => {
    return request2({
        url: `/feedbacks/${id}/respond.json`,
        method: 'post',
        data
    });
};
