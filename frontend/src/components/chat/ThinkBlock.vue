<template>
  <details :open="!isComplete" class="think-block">
    <summary class="summary">
      <svg class="arrow" viewBox="0 0 24 24">
        <path
            d="M9 5l7 7-7 7"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
      </svg>
      {{ isComplete ? `已深度思考（${elapsedTime.toFixed(1)}s）` : `深度思考中（${elapsedTime.toFixed(1)}s）` }}
    </summary>
    <div class="content2" v-html="cleanedContent"></div>
  </details>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps<{
  content: string
  isComplete: boolean
}>()

const elapsedTime = ref(0)
const startTime = Date.now()
let timer: number | null = null

// 提取 <think> 中的内容，支持未闭合时提前展示
const cleanedContent = computed(() => {
  const match = props.content.match(/^<think>([\s\S]*?)(<\/think>|$)/)
  return match ? match[1].replace(/\n/g, '<br>') : ''
})

// 启动计时器
onMounted(() => {
  timer = window.setInterval(() => {
    elapsedTime.value = (Date.now() - startTime) / 1000
  }, 100)
})

// isComplete 变为 true 时停止计时器
watch(
    () => props.isComplete,
    (newVal) => {
      if (newVal && timer) {
        clearInterval(timer)
        timer = null
      }
    }
)

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style scoped lang="less">
.think-block {
  border-left: 2px solid #eee;
  padding: 10px;
  background: rgb(249, 250, 251);
  margin-bottom: 10px;
  .content2 {
    padding: 0 5px;
    margin-top: 8px;
    color: #666;
    font-size: 14px;
  }
}

.summary {
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #555;
}

.arrow {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  transition: transform 0.3s ease;
}

details[open] .arrow {
  transform: rotate(90deg);
}


</style>
