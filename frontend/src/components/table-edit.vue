<template>
  <el-form ref="formRef" :model="form" :rules="rules" :label-width="options.labelWidth">
    <el-row>
      <el-col :span="options.span" v-for="item in options.list" :key="item.prop">
        <el-form-item :label="item.label" :prop="item.prop">
          <!-- 文本框、数字框、下拉框、日期框、开关、上传 -->
          <el-input v-if="item.type === 'input'" v-model="form[item.prop]" :disabled="item.disabled"
                    :placeholder="item.placeholder" clearable></el-input>
          <el-input-number v-else-if="item.type === 'number'" v-model="form[item.prop]"
                           :disabled="item.disabled" controls-position="right"></el-input-number>
          <el-input
              v-else-if="item.type ==='textarea'"
              v-model="form[item.prop]"
              style="width: 100%"
              :rows="10"
              type="textarea"
              placeholder="请输入提示內容"
          />
          <el-select v-else-if="item.type ==='select'" v-model="form[item.prop]" :disabled="item.disabled"
                     :placeholder="item.placeholder" clearable>
            <el-option v-for="opt in item.opts" :label="opt.label" :value="opt.value"></el-option>
          </el-select>
          <el-date-picker v-else-if="item.type === 'date'" type="date" v-model="form[item.prop]"
                          :value-format="item.format"></el-date-picker>
          <el-switch v-else-if="item.type ==='switch'" v-model="form[item.prop]"
                     :active-value="item.activeValue" :inactive-value="item.inactiveValue"
                     :active-text="item.activeText" :inactive-text="item.inactiveText"></el-switch>
          <el-upload v-else-if="item.type === 'upload'" class="avatar-uploader"
                     :action="uploadUrl"
                     :headers="uploadHeaders"
                     :show-file-list="false"
                     :multiple="false"
                     :on-success="handleAvatarSuccess"
                     :before-upload="beforeUpload"
                     :on-error="handleUploadError">
            <img v-if="form[item.prop]" :src="form[item.prop]" class="avatar"/>
            <el-icon v-else class="avatar-uploader-icon">
              <Plus/>
            </el-icon>
          </el-upload>
          <slot :name="item.prop" v-else></slot>
        </el-form-item>
      </el-col>
      <el-upload v-if="isQuick" class="avatar-uploader"
                 :action="uploadUrl2"
                 :headers="uploadHeaders2"
                 show-file-list
                 multiple
                 drag
                 :on-success="handleAvatarSuccess2"
                 :before-upload="beforeUpload2"
                 :on-error="handleUploadError2">
        提示内容快捷上传+
<!--        <el-icon class="avatar-uploader-icon">-->
<!--          <Plus/>-->
<!--        </el-icon>-->
      </el-upload>
    </el-row>
    <el-form-item>
      <el-button type="primary" @click="saveEdit(formRef)">保 存</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import {FormOption} from '@/types/form-option';
import {FormInstance, FormRules, UploadProps} from 'element-plus';
import {PropType, ref, nextTick} from 'vue';
import {ElMessage} from 'element-plus';
import { Plus } from '@element-plus/icons-vue'; // 添加 Plus 图标的导入

// 接收父组件传递的属性
const {options, formData, edit, update, isQuick} = defineProps({
  options: {
    type: Object as PropType<FormOption>,
    required: true
  },
  formData: {
    type: Object,
    required: true
  },
  edit: {
    type: Boolean,
    required: false
  },
  update: {
    type: Function,
    required: true
  },
  isQuick: {
    type: Boolean,
    default: false
  }
});

// 初始化表单数据
const formInitialValues = options.list.reduce((acc, item) => {
  switch (item.type) {
    case 'input':
    case 'upload':
      acc[item.prop] = '';
      break;
    case 'number':
      acc[item.prop] = 0;
      break;
    case 'date':
      acc[item.prop] = null;
      break;
    case 'switch':
      acc[item.prop] = item.inactiveValue;
      break;
    case 'select':
      acc[item.prop] = item.opts.length > 0 ? item.opts[0].value : null;
      break;
    default:
      acc[item.prop] = '';
  }
  return acc;
}, {});
const form = ref({...(edit ? formData : formInitialValues)});

// 生成表单验证规则
const rules: FormRules = options.list.reduce((acc, item) => {
  if (item.required) {
    acc[item.prop] = [{required: true, message: `${item.label}不能为空`, trigger: 'blur'}];
  }
  return acc;
}, {} as FormRules);

// 表单引用
const formRef = ref<FormInstance>();

// 保存表单数据的方法
const saveEdit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;

  try {
    await formEl.validate();
    const success = await update({
      ...form.value,
    });

    if (success) {
      ElMessage.success('操作成功');
    }
  } catch (error) {
    ElMessage.error('保存失败');
  }
};

// 上传地址
const uploadUrl = `${import.meta.env.VITE_SERVE_HOST}/console/api/files/upload`;
const uploadUrl2 = `${import.meta.env.VITE_SERVE_HOST}/api/uploadToText`;
console.log('uploadUrl',import.meta)
// 上传请求头，带上 token
const getUploadHeaders = () => {
  const token = localStorage.getItem('console_token');
  if (!token) {
    ElMessage.error('未找到有效的 token，请重新登录');
    return null;
  }
  return {
    'Authorization': `Bearer ${token}`,
    // 'X-Requested-With': 'XMLHttpRequest',
    // 'Accept': 'application/json'
  };
};
const getUploadHeaders2 = () => {
  const token = localStorage.getItem('console_token');
  if (!token) {
    ElMessage.error('未找到有效的 token，请重新登录');
    return null;
  }
  return {
    'Authorization': `Bearer ${token}`,
    // 'X-Requested-With': 'XMLHttpRequest',
    // 'Accept': 'application/json'
  };
};
const uploadHeaders = ref(getUploadHeaders());
const uploadHeaders2 = ref(getUploadHeaders2());

// 上传前的文件验证方法
const beforeUpload = (file) => {
  console.log('上传文件类型:', file.type);
  console.log('上传文件大小:', file.size);
  const allowedTypes = ['image/jpeg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB');
    return false;
  }
  return true;
};

// 上传成功后的处理方法
const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  if (response) {
    console.log('fileUrlaaaaaaa',response)
    let fileUrl = response.data.file_urls[0];
    // 检查并补充端口号
    fileUrl = fileUrl.replace('http://localhost/',`${import.meta.env.VITE_SERVE_HOST}/`);
    console.log('fileUrl',fileUrl,form.value)
    form.value[Object.keys(form.value).find(key => options.list.find(opt => opt.prop === key && opt.type === 'upload'))] = fileUrl;
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error('图片上传失败');
  }
};

// 上传失败处理方法
const handleUploadError = (error, uploadFile) => {
  console.error('图片上传失败:', error);
  ElMessage.error('图片上传失败，请检查网络或服务器状态');
};

// 知识库docx上传前的文件验证方法
const beforeUpload2 = (file) => {
  console.log('上传文件:', file);
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'text/plain',
  ];

  // 检查文件扩展名作为后备方案
  const fileExt = file.name.split('.').pop().toLowerCase();
  const allowedExts = ['docx', 'doc', 'txt'];

  if (!allowedTypes.includes(file.type) && !allowedExts.includes(fileExt)) {
    ElMessage.error(`只能上传 ${allowedExts.join(', ')} 格式的文件`);
    return false;
  }

  // 添加文件大小限制
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过10MB');
    return false;
  }
  console.log('验证成功')
  return true;
};

// 知识库docx上传成功后的处理方法
const handleAvatarSuccess2: UploadProps['onSuccess'] = async (response, uploadFile) => {
  try {
    console.log('responseaaaaaaaaa',response)
    if (!response) {
      throw new Error('无响应数据');
    }

    if (response.code !== 200 && response.success !== true) {
      throw new Error(response.message || '文件解析失败');
    }

    const textareaItem = options.list.find(item => item.type === 'textarea');
    if (!textareaItem) {
      throw new Error('未找到文本框项');
    }

    const propKey = textareaItem.prop;
    let content = '';

    // 处理不同的响应格式
    if (Array.isArray(response.data?.texts)) {
      content = response.data.texts.join('\n');
    } else if (response.data?.text) {
      content = response.data.text;
    } else if (response.data?.content) {
      content = response.data.content;
    } else {
      content = JSON.stringify(response.data);
    }

    // 添加到文本框内容中
    form.value[propKey] = form.value[propKey]
        ? `${form.value[propKey]}\n\n${content}`
        : content;

    await nextTick();
    ElMessage.success('文件内容已添加到文本框');

  } catch (error) {
    console.error('处理上传响应失败:', error);
    ElMessage.error(error.message || '处理上传响应失败');
  }
};

// 知识库docx上传失败处理方法
const handleUploadError2 = (error, uploadFile) => {
  console.error('文件上传失败:', error);
  let errorMessage = '文件上传失败';

  if (error.response) {
    // 处理HTTP错误响应
    const status = error.response.status;
    if (status === 401) {
      errorMessage = '认证失败，请重新登录';
      // router.push('/login');
    } else if (status === 413) {
      errorMessage = '文件大小超过服务器限制';
    } else if (status === 415) {
      errorMessage = '不支持的媒体类型';
    }

    // 尝试从响应中获取错误信息
    const serverMessage = error.response.data?.message;
    if (serverMessage) {
      errorMessage += `: ${serverMessage}`;
    }
  } else if (error.message) {
    errorMessage += `: ${error.message}`;
  }

  ElMessage.error(errorMessage);
};
</script>

<style scoped>
.avatar-uploader .el-upload {

  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
.avatar{
  width: 50px;
  height: 50px;
  border-radius: 8px;
}
</style>