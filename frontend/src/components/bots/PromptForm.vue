<template>
  <div class="lineInfo">
    <div
        v-for="(part, i) in parsedContent"
        :key="i"
        class="editable-text"
    >
      <!-- 可编辑 Slot 区 -->
      <div
          v-if="part.isInputSlot"
          contenteditable="true"
          class="editable-input"
          data-placeholder="请输入"
          @compositionstart="onCompositionStart"
          @compositionend="onCompositionEnd(part.id!, $event)"
          @input="onInput(part.id!, $event)"
          :ref="el => (editableRefs[part.id!] = el)"
      ></div>
      <span v-else class="text-part">{{ part.content }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, nextTick } from 'vue';

interface Part {
  content: string;
  isInputSlot: boolean;
  placeholder?: string;
}

const props = defineProps<{ prompt: string }>();
const emit = defineEmits<{
  (e: 'updatePrompt', value: string): void
}>();

// 监测输入法组合状态
const isComposing = ref(false);
// 解析内容
const parsedContent = ref<Part[]>([]);
// 存储输入内容
const formData = ref<Record<string, string>>({});
// 管理 contenteditable DOM 引用
const editableRefs = ref<Record<string, HTMLElement | null>>({});

function parsePrompt(prompt: string) {
  const regex = /<input\s*\/?>/g;
  const parts: Part[] = [];
  let lastIndex = 0;
  let match: RegExpExecArray | null;
  let inputIndex = 0;

  while ((match = regex.exec(prompt)) !== null) {
    if (match.index > lastIndex) {
      parts.push({
        content: prompt.slice(lastIndex, match.index),
        isInputSlot: false
      });
    }

    const id = `input_${inputIndex++}`;
    parts.push({
      content: '',
      isInputSlot: true,
      id
    });

    lastIndex = match.index + match[0].length;
  }

  if (lastIndex < prompt.length) {
    parts.push({
      content: prompt.slice(lastIndex),
      isInputSlot: false
    });
  }

  parsedContent.value = parts;
}


// 初始化或 prompt 变更时解析
watch(
    () => props.prompt,
    (raw) => {
      if (typeof raw !== 'string') {
        parsedContent.value = [];
        return;
      }
      parsePrompt(raw.replace(/\r\n?/g, '\n'));
    },
    { immediate: true }
);

// 初始化 formData keys
watch(parsedContent, (parts) => {
  parts.forEach((part) => {
    if (part.isInputSlot && part.id && !(part.id in formData.value)) {
      formData.value[part.id] = '';
    }
  });

  nextTick(() => {
    parts.forEach((part) => {
      if (part.isInputSlot && part.id) {
        const el = editableRefs.value[part.id];
        if (el && el.innerText !== formData.value[part.id]) {
          el.innerText = formData.value[part.id];
        }
      }
    });
  });
}, { immediate: true });


function onInput(key: string, ev: InputEvent) {
  if (isComposing.value) return;
  const el = ev.target as HTMLElement;
  formData.value[key] = el.innerText;
  emitUpdatedPrompt();
}

function onCompositionStart() {
  isComposing.value = true;
}

function onCompositionEnd(key: string, ev: CompositionEvent) {
  isComposing.value = false;
  const el = ev.target as HTMLElement;
  formData.value[key] = el.innerText;
  emitUpdatedPrompt();
}

function emitUpdatedPrompt() {
  let result = '';
  parsedContent.value.forEach((part) => {
    if (part.isInputSlot && part.id) {
      const txt = formData.value[part.id] || '';
      result += `<input>${txt}`;
    } else {
      result += part.content;
    }
  });
  emit('updatePrompt', result);
}
</script>

<style scoped lang="less">
.lineInfo {
  padding: 0 2px 0 20px;
  font-size: 14px;
  white-space: pre-wrap;
}
.editable-text {
  display: inline;
}
.text-part {
  color: #222;
  font-family: "PingFang SC", "Noto Sans SC", sans-serif;
}
.editable-input {
  display: inline-block;
  padding: 0 4px;
  margin: 0 1px 4px;
  background-color: rgba(186, 192, 255, 0.2);
  color: rgba(148, 152, 247, 0.7);
  border-radius: 4px;
  min-width: 20px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.7;
  outline: none;
  cursor: text;
}
.editable-input:empty:before {
  content: attr(data-placeholder);
  color: #aaa;
  pointer-events: none;
}
.editable-input:focus {
  background-color: #fff;
  border: 1px solid #66afe9;
}
</style>
