<script setup lang="ts">
import {getAgentsApi} from '@/api/agent';
import {ref} from "vue";
import {useRouter} from "vue-router";
import {ElMessage} from "element-plus";

const agents = ref([]);
const getData = async () => {

  const res = await getAgentsApi({
    page: 1,
    per_page: 9,
  });
  agents.value = res.data.list;
};
const goto = (id: string) => {
  if(!localStorage.getItem('console_token')){
    router.push('/login');
    return;
  }
  router.push(`/aimain/${id}`);
};
const goto2 = (url: string) => {
  window.open(url, '_blank');
};
const goto3 = (url: string) => {
  ElMessage.warning('即将上线~')
};
const router = useRouter();
getData();
</script>

<template>
  <el-row :gutter="20">
    <el-col :sm="12" :md="8" :lg="6" @click="goto2('http://***********')">
      <div class="modelf">
        <div class="model">
          <img src="@/assets/img/study.png" alt=""/>
          <div class="info2">
            <div title="商洛烟草专卖局（公司）培训学习平台">商洛烟草专卖局（公司）培训学习平台</div>
            <div title="作者：稷下仕智能科技">作者：稷下仕智能科技</div>
            <div class="info"
                 title="智能刷题、自主多媒学习、一站式高效培训平台。">智能刷题、自主多媒学习、一站式高效培训平台。
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :sm="12" :md="8" :lg="6" v-for="item in agents" :key="item.id">
      <div class="modelf" @click="goto(item.id)">
        <div class="model">
          <img :src="item.icon" alt=""/>
          <div class="info2">
            <div :title="item.name">{{ item.name }}</div>
            <div :title="item.creator_name">作者：{{ item.creator_name }}</div>
            <div class="info" :title="item.function_description">{{ item.function_description }}</div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<style scoped lang="less">
.modelf {
  margin-top: 24px;
  padding: 12px 12px 16px;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(82, 100, 154, 0.13);
  border-radius: 16px;
  background-color: #fff;
  cursor: pointer;

  img {
    width: 100%;
    height: 140px;
    border-radius: 8px;
    object-fit: cover;
  }

  .info2 {
    font-size: 14px;

    > :nth-child(2) {
      margin-bottom: 5px;
      color: rgba(32, 41, 69, 0.62);
    }

    .info {
      color: rgba(32, 41, 69, 0.62);
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      min-height: 44px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.modelf:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, .08), 0 8px 24px 0 rgba(0, 0, 0, .04);
  border-color: rgba(82, 100, 154, 0.13);
}
</style>