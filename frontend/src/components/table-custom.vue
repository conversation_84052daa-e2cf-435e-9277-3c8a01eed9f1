<template>
  <div>
    <div class="table-toolbar" v-if="hasToolbar">
      <div class="table-toolbar-left">
        <slot name="toolbarBtn"></slot>
      </div>
      <div class="table-toolbar-right flex-center">
        <template v-if="multipleSelection.length > 0">
          <el-tooltip effect="dark" content="删除选中" placement="top">
            <el-icon class="columns-setting-icon" @click="delSelection(multipleSelection)">
              <Delete/>
            </el-icon>
          </el-tooltip>
          <el-divider direction="vertical"/>
        </template>
        <el-tooltip effect="dark" content="刷新" placement="top">
          <el-icon class="columns-setting-icon" @click="refresh">
            <Refresh/>
          </el-icon>
        </el-tooltip>
        <el-divider direction="vertical"/>
        <el-tooltip effect="dark" content="列设置" placement="top">
          <el-dropdown :hide-on-click="false" size="small" trigger="click">
            <el-icon class="columns-setting-icon">
              <Setting/>
            </el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="c in columns">
                  <el-checkbox v-model="c.visible" :label="c.label"/>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
      </div>
    </div>
    <el-table class="mgb20" :style="{ width: '100%' }" border :data="tableData" :row-key="rowKey"
              @selection-change="handleSelectionChange" table-layout="auto">
      <template v-for="item in columns" :key="item.prop">
        <el-table-column v-if="item.visible" :prop="item.prop" :label="item.label" :width="item.width"
                         :type="item.type" :align="item.align || 'center'">

          <template #default="{ row, column, $index }" v-if="item.type === 'index'">
            {{ getIndex($index) }}
          </template>
          <template #default="{ row, column, $index }" v-if="item.type === 'img'">
            <img v-if="row[item.prop]" :src="row[item.prop]" style="width: 50px;height: 50px;border-radius: 8px" @error="onImageError($event)"/>
            <img v-else src="@/assets/img/robot.png" style="width: 50px;height: 50px;border-radius: 8px" @error="onImageError($event)"/>
          </template>
          <template #default="{ row, column, $index }" v-else-if="item.prop === 'is_enabled'">
            <el-switch v-model="row[item.prop]" :active-value="1" :inactive-value="0"
                       @change="handleSwitchChange(row)" :disabled="isSwitchDisabled(row)"/>
          </template>
          <template #default="{ row, column, $index }" v-else-if="!item.type">
            <slot :name="item.prop" :rows="row" :index="$index">
              <template v-if="item.prop == 'operator'">
                <el-button type="warning" size="small" :icon="View" @click="viewFunc(row)">
                  查看
                </el-button>
<!--                <el-button type="primary" size="small" :icon="Edit" @click="editFunc(row)">-->
<!--                  编辑-->
<!--                </el-button>-->
                <el-button type="danger" size="small" :icon="Delete" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
              <template v-else-if="item.prop == 'operator2'">
                <el-button type="warning" size="small" :icon="View" @click="viewFunc(row)">
                  查看
                </el-button>
                <el-button type="primary" size="small" :icon="Edit" @click="editFunc(row)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" :icon="Delete" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
              <span v-else-if="item.formatter">
                                {{ item.formatter(row[item.prop]) }}
                            </span>
              <span v-else>
                                {{ row[item.prop] }}
                            </span>
            </slot>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <el-pagination v-if="hasPagination" :current-page="currentPage" :page-size="limit" :background="true"
                   :layout="layout" :total="total" @current-change="handleCurrentChange"/>
  </div>
</template>

<script setup lang="ts">
import {toRefs, PropType, ref} from 'vue'
import {Delete, Edit, View, Refresh} from '@element-plus/icons-vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {User} from "@/types/user";
import {updateUserApi} from "@/api/user";
import defaultImage from '@/assets/img/robot.png';
const props = defineProps({
  // 表格相关
  tableData: {
    type: Array,
    default: []
  },
  columns: {
    type: Array as PropType<any[]>,
    default: []
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  hasToolbar: {
    type: Boolean,
    default: true
  },
  //  分页相关
  hasPagination: {
    type: Boolean,
    default: true
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  limit: {
    type: Number,
    default: 10
  },

  layout: {
    type: String,
    default: 'total, prev, pager, next'
  },
  delFunc: {
    type: Function,
    default: () => {
    }
  },
  viewFunc: {
    type: Function,
    default: () => {
    }
  },
  editFunc: {
    type: Function,
    default: () => {
    }
  },
  delSelection: {
    type: Function,
    default: () => {
    }
  },
  refresh: {
    type: Function,
    default: () => {
    }
  },
  changePage: {
    type: Function,
    default: () => {
    }
  }
})

let {
  tableData,
  columns,
  rowKey,
  hasToolbar,
  hasPagination,
  total,
  currentPage,
  limit,
  layout,
} = toRefs(props)

columns.value.forEach((item) => {
  if (item.visible === undefined) {
    item.visible = true
  }
})

const onImageError = (event) => {
  event.target.src = defaultImage;
};
// 当选择项发生变化时会触发该事件
const multipleSelection = ref([])
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}
const isSwitchDisabled = (row: User) => {
  // 判断是否需要禁用某些用户的 Switch（例如，超级管理员的状态不能修改）
  return row.role === 'admin'; // 假设管理员不能修改自己的启用状态
};
const handleSwitchChange = async (row: User) => {
  // 获取最新的 is_enabled 值，翻转状态
  const updatedStatus = row.is_enabled === 1 ? 1 : 0;

  // 构造只包含需要更新字段的数据对象
  const updateData = {
    is_enabled: updatedStatus, // 只更新is_enabled
  };

  // 调用更新接口
  const response = await updateUserApi(row.id, updateData);
  // 根据返回值判断更新是否成功
  if (response) {
    ElMessage.success('用户状态更新成功');
  } else {
    ElMessage.error('用户状态更新失败');
  }
};
// 当前页码变化的事件
const handleCurrentChange = (val: number) => {
  console.log('handleCurrentChange', val)
  props.changePage(val)
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    type: 'warning'
  })
      .then(async () => {
        props.delFunc(row);
      })
      .catch(() => {
      });
};

const getIndex = (index: number) => {
  return index + 1 + (currentPage.value - 1) * limit.value
}

</script>

<style scoped>
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 10px;
}

.columns-setting-icon {
  display: block;
  font-size: 18px;
  cursor: pointer;
  color: #676767;
}
</style>
<style>
.table-header .cell {
  color: #333;
}
</style>