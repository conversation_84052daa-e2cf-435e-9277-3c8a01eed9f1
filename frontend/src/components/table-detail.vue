<template>
	<el-descriptions :title="title" :column="column" border>
		<el-descriptions-item v-for="item in list" :span="item.span">
			<template #label> {{ item.label }}</template>
			<slot :name="item.prop" :rows="row" v-if="item.prop === 'icon'">
        <img v-if="row[item.prop]" :src="row[item.prop]" style="width: 50px;height: 50px;" @error="onImageError($event)"/>
        <img v-else src="@/assets/img/robot.png" style="width: 50px;height: 50px;" @error="onImageError($event)"/>
			</slot>
      <slot :name="item.prop" :rows="row" v-else>
        {{ item.value || row[item.prop] }}
      </slot>
		</el-descriptions-item>
	</el-descriptions>
</template>

<script lang="ts" setup>
import defaultImage from '@/assets/img/robot.png';
const props = defineProps({
	data: {
		type: Object,
		required: true,
	}
});
const { row, title, column = 2, list } = props.data;
const onImageError = (event) => {
  event.target.src = defaultImage;
};
</script>
