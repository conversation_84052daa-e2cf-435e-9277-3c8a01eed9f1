import axios, {AxiosInstance, AxiosError, AxiosResponse, InternalAxiosRequestConfig} from 'axios';
import {ElMessage} from "element-plus";

const createAxiosService = (baseURL: string, timeout: number, requestInterceptors?, responseInterceptors?) => {
    const service: AxiosInstance = axios.create({
        baseURL,
        timeout
    });

    let isRefreshing = false; // 是否正在刷新token
    let failedQueue: Array<{
        resolve: (value?: unknown) => void,
        reject: (error?: unknown) => void,
        config: InternalAxiosRequestConfig
    }> = [];

    const processQueue = (error: any, token: string | null = null) => {
        failedQueue.forEach(prom => {
            if (error) {
                prom.reject(error);
            } else {
                if (token && prom.config.headers) {
                    prom.config.headers['Authorization'] = `Bearer ${token}`;
                }
                service.request(prom.config).then(prom.resolve).catch(prom.reject);
            }
        });
        failedQueue = [];
    };

    // 请求拦截器
    service.interceptors.request.use(
        (config: InternalAxiosRequestConfig) => {
            const token = localStorage.getItem('console_token');
            if (token && !config.url?.includes('/login') && !config.url?.includes('/refresh-token')) {
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            if (config.method?.toLowerCase() === 'get' && config.data) {
                config.params = config.data;
                delete config.data;
            }
            return config;
        },
        (error: AxiosError) => Promise.reject(error)
    );

    // 响应拦截器
    service.interceptors.response.use(
        (response: AxiosResponse) => {
            if (response.config.responseType === 'stream') {
                return response;
            }
            if (response.status >= 200 && response.status < 300) {
                return response.data;
            }
            return response;
        },
        (error: AxiosError) => {
            const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

            if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url?.includes('/refresh-token')) {
                if (isRefreshing) {
                    // 如果已经在刷新token，返回一个 Promise，等待刷新完成后再重试请求
                    return new Promise((resolve, reject) => {
                        failedQueue.push({ resolve, reject, config: originalRequest });
                    });
                }

                originalRequest._retry = true;
                isRefreshing = true;

                // 发送刷新token请求
                return service.post('/refresh-token', {
                    refresh_token: localStorage.getItem('refresh_token')
                }).then(res => {
                    // 假设返回格式是 { access_token: 'xxx', refresh_token: 'yyy' }
                    const { access_token, refresh_token } = res.data;

                    if (access_token) {
                        localStorage.setItem('console_token', access_token);
                        if (refresh_token) {
                            localStorage.setItem('refresh_token', refresh_token);
                        }

                        // 更新请求头token
                        originalRequest.headers['Authorization'] = `Bearer ${access_token}`;

                        processQueue(null, access_token);
                        console.log('开始重试原请求', originalRequest.url);
                        return service(originalRequest).then(res => {
                            console.log('原请求重试成功', res);
                            return res;
                        }).catch(err => {
                            console.log('原请求重试失败', err);
                            return Promise.reject(err);
                        });
                    } else {
                        // 刷新失败，登出或跳转登录页
                        processQueue(new Error('刷新token失败'));
                        // 这里可以跳转登录页面，示例：
                        // window.location.href = '/login';
                        return Promise.reject(error);
                    }
                }).catch(err => {
                    processQueue(err);
                    // 这里也可以跳转登录
                    return Promise.reject(err);
                }).finally(() => {
                    isRefreshing = false;
                });
            }

            // 其他错误处理
            if (error.response) {
                ElMessage.error(`请求失败：${error.response.data.message || error.message}`);
            } else if (error.request) {
            } else {
                ElMessage.error('发生了未知错误，请稍后重试。');
            }

            return Promise.reject(error);
        }
    );

    if (requestInterceptors) {
        service.interceptors.request.use(...requestInterceptors);
    }
    if (responseInterceptors) {
        service.interceptors.response.use(...responseInterceptors);
    }

    return service;
};

const apiBaseUrl = import.meta.env.VITE_BASE_API_URL;
const service = createAxiosService(apiBaseUrl, 3000000);
export default service;
