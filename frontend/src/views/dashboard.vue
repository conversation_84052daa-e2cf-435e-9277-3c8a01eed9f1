<template>
  <div class="scroll-wrapper" ref="scrollContainer">
    <div class="tech-bg">
      <div class="info">
        <h1 class="tit">商洛市烟草专卖局(公司)<br/>AI平台欢迎您</h1>
      </div>
    </div>
    <el-row>
      <el-col :span="24" class="tit1">
        <div class="flex items-center">
          <h3>团队共享应用 </h3>
          <span class="ml-1 mr-1">轻松使用 AI 满足你的需求</span>
          <span>
          <el-select v-show="role !== 'normal'" v-model="selectedTags" @change="setTags" clearable filterable
                     collapse-tags multiple style="width: 150px;" placeholder="选择标签">
            <el-option v-for="item in allTags" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </span>
        </div>
      </el-col>
      <el-col :span="24" class="mt-4">
        <el-row :gutter="20">
          <el-col :span="24" v-for="(item2, mode) in displayAgents" :key="item2.id">
            <template v-if="item2.length>0" class="tit">
              <h3 class="mode-title" @click="toggleCollapse(mode)" style="cursor: pointer;">
                <span><img :src="`/images/${mode}.png`" alt="" class="w-15"> {{ getModeName(mode) }}</span>
                <span style="margin-left: 10px; font-size: 14px; color: #999;display: flex;align-items: center">
                  <template v-if="isCollapsed(mode)">
                    <ArrowUp style="width: 20px;"/>
                  </template>
                  <template v-else>
                    <ArrowDown style="width: 20px;"/>
                  </template>
                </span>
              </h3>
              <el-row v-show="!isCollapsed(mode)" :gutter="20">
                <el-col :sm="12" :md="8" :lg="6" v-if="zhiduAgents.length > 0 && mode === 'chat'">
                  <div class="modelf" @click="showZhiduDialog = true">
                    <div class="model">
                      <img src="/images/chat.png" alt=""/>
                      <div class="info2">
                        <span>制度查询</span>
                        <span>包含 {{ zhiduAgents.length }} 个应用</span>
                        <span class="info" style="margin-bottom: 30px;">点击查看全部制度查询类应用</span>
                      </div>
                    </div>
                    <div class="info3">
                      试一试
                    </div>
                  </div>
                </el-col>
                <el-col :sm="12" :md="8" :lg="6" v-for="item in item2" :key="item.id">
                  <!-- 保留原来的 agent 卡片内容 -->
                  <div class="modelf" @click="goto(item)">
                    <div class="model">
                      <div class="newImg">
                        <img v-if="item.icon_type!=='emoji'" :src="item.icon_url" alt=""/>
                        <div v-else>
                          <div class="emoji" :style="{background:item.icon_background}">{{ item.icon }}</div>
                        </div>
                        <img :src="`/images/${item.mode}.png`" alt="" class="tag">
                      </div>
                      <div class="info2">
                        <span :title="item.name">{{ item.name }}</span>
                        <span :title="item.creator_name" v-if="item.mode === 'chat'">聊天助手(作者:{{
                            item.author_name
                          }})</span>
                        <span :title="item.creator_name"
                              v-else-if="item.mode === 'agent-chat'">Agent(作者:{{ item.author_name }})</span>
                        <span :title="item.creator_name"
                              v-else-if="item.mode === 'completion'">文本生成(作者:{{ item.author_name }})</span>
                        <span :title="item.creator_name"
                              v-else-if="item.mode === 'advanced-chat'">Chatflow(作者:{{ item.author_name }})</span>
                        <span :title="item.creator_name"
                              v-else-if="item.mode === 'workflow'">工作流(作者:{{ item.author_name }})</span>
                        <span class="info" :title="item.description">{{ item.description }}</span>
                      </div>
                    </div>
                    <el-dropdown trigger="click" style="width: 100%">
                      <div class="appTags" @click.stop="showNowAppTags(item.tags)" v-show="role !== 'normal'">
                        <template v-if="item.tags.length>0">
                          <el-icon class="mr-1">
                            <Flag/>
                          </el-icon>
                          <span class="ellipsis" style="width: calc(100% - 20px);display: inline-block">{{
                              item.tags.map(item2 => item2.name).join(', ')
                            }}</span>
                        </template>
                        <template v-else>
                          <div class="flex items-center appTag">
                            <el-icon>
                              <Flag/>
                            </el-icon>
                            <span class="ml-1">添加标签</span>
                          </div>
                        </template>
                      </div>
                      <template #dropdown>
                        <div class="tagInfos">
                          <el-input v-model="filterTags" :prefix-icon="Search"
                                    placeholder="搜索标签" clearable class="mb-1"></el-input>
                          <el-checkbox-group v-model="nowAppTags" class="tagsList" @change="changeTag(item)">
                            <el-checkbox v-for="item2 in filteredTags" :value="item2.id" class="mt-1 mb-1 font-14"
                                         @click.native="lastClicked = item2.id">
                              {{ item2.name }}
                            </el-checkbox>
                          </el-checkbox-group>
                          <div @click="showAppEdit(item)" class="hoverIcon font-14 mt-1">
                            <el-icon>
                              <Flag/>
                            </el-icon>
                            管理标签
                          </div>
                        </div>
                      </template>
                    </el-dropdown>
                    <div class="info3 flex justify-between items-center">
                      <div class="countInfo">
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            content="点击量"
                            placement="top-start"
                        >
                          <img src="@/assets/img/svg/fire.svg" alt="" class="w-15"/>
                        </el-tooltip>
                        <span>{{ item.click_count }}</span>
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            content="使用人次"
                            placement="top-start"
                        >
                          <img src="@/assets/img/svg/audience_insight.svg" alt="" class="w-15 ml-1 mr-1"/>
                        </el-tooltip>
                        <span>{{ item.user_count }}</span>
                      </div>
                      <div class="action-menu" @click.stop v-if="role !== 'normal'">
                        <el-dropdown trigger="click">
                          <el-icon class="more">
                            <MoreFilled/>
                          </el-icon>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item @click.stop="copyAgent(item)">复制到我的应用</el-dropdown-item>
                              <el-dropdown-item @click.stop="exportAgent(item)">导出 DSL</el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <div v-else>试一试</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
          </el-col>
          <el-col :span="24" @click="toggleOther">
            <h3 class="mode-title">其他
              <span style="margin-left: 10px; font-size: 14px; color: #999;display: flex;align-items: center">
                <template v-if="otherCollapse">
                  <ArrowUp style="width: 20px;"/>
                </template>
                <template v-else>
                  <ArrowDown style="width: 20px;"/>
                </template>
              </span>
            </h3>
          </el-col>
          <el-col :sm="12" :md="8" :lg="6" @click="goto2('http://10.106.0.35')" v-if="!otherCollapse">
            <div class="modelf">
              <div class="model">
                <img src="@/assets/img/study.png" alt=""/>
                <div class="info2">
                  <span title="商洛烟草专卖局（公司）培训学习平台">商洛烟草专卖局（公司）培训学习平台</span>
                  <span title="作者：商小烟">作者：商小烟</span>
                  <span class="info"
                        title="智能刷题、自主多媒学习、一站式高效培训平台。">智能刷题、自主多媒学习、一站式高效培训平台。
            </span>
                </div>
              </div>
              <div class="info3">
                <div>试一试</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog
        v-model="showZhiduDialog"
        title="制度查询"
        width="1200px"
    >
      <el-row :gutter="20">
        <el-col :sm="12" :md="8" :lg="6" v-for="item in zhiduAgents" :key="item.id">
          <div class="modelf" @click="openZhiduApp(item)">
            <div class="model">
              <div class="newImg">
                <img v-if="item.icon_type!=='emoji'" :src="item.icon_url" alt=""/>
                <div v-else>
                  <div class="emoji" :style="{background:item.icon_background}">{{ item.icon }}</div>
                </div>
                <img :src="`/images/${item.mode}.png`" alt="" class="tag">
              </div>
              <div class="info2">
                <span>{{ item.name }}</span>
                <span :title="item.creator_name">聊天助手</span>
                <span class="info" :title="item.description">{{ item.description }}</span>
              </div>
            </div>
            <div class="info3 flex justify-between items-center">
              <div class="countInfo">
                <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="点击量"
                    placement="top-start"
                >
                  <img src="@/assets/img/svg/fire.svg" alt="" class="w-15"/>
                </el-tooltip>
                <span>{{ item.click_count }}</span>
                <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="使用人次"
                    placement="top-start"
                >
                  <img src="@/assets/img/svg/audience_insight.svg" alt="" class="w-15 ml-1 mr-1"/>
                </el-tooltip>
                <span>{{ item.user_count }}</span>
              </div>
              <div>试一试</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog v-model="nowAppTagsEditDialog" title="管理标签" width="500px">
      <div class="">


        <div class="tag-list flex items-center">
          <el-input
              style="width: 100px;"
              v-model="newTag"
              placeholder="创建新标签"
              class="create-input mr-1"
              @blur="addTagFuc"
          />
          <el-tag
              v-for="(tag, index) in allTags"
              :key="tag.id"
              class="tag-item mt-1 mb-1 mr-1 flex items-center"
              size="large"
              effect="plain"
          >
            <template v-if="editIndex === index">
              <el-input
                  v-model="editName"
                  size="small"
                  class="tag-edit-input"
                  @blur="confirmEdit(index,tag)"
                  @keyup.enter="confirmEdit(index,tag)"
              />
            </template>
            <template v-else>
              <span>{{ tag.name }}</span>
              <span class="bindCount">{{ tag.binding_count }}</span>
              <el-icon @click="editTag(index, tag)" class="icon-btn">
                <Edit/>
              </el-icon>
              <el-icon class="icon-btn" @click="delTagFuc(tag)">
                <Delete/>
              </el-icon>
            </template>
          </el-tag>
        </div>
      </div>
    </el-dialog>
    <el-dialog
        v-model="showDialog"
        width="500px"
        title="提示"
    >
      <div>
        本平台仅面向内部员工开放，用于学习与交流。使用人员须严格遵守保密相关规定，严禁复制、粘贴、下载平台内容，并将其发布至互联网等外部媒体平台
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="dashboard">
import {getAgentsApi, getAgentApi, setSiteStatus, copyAgentFuc, exportAgentFuc} from '@/api/agent';
import {getAppStatus, getAppClicks, saveClick} from '@/api/system';
import {getTags, setTagIds, addTag, appBindTag, appDelTag, delTag, updateTag} from '@/api/tags';
import {computed, onMounted, ref, toRefs} from "vue";
import {useRoute, useRouter} from "vue-router";
import {ElMessage, ElMessageBox} from "element-plus";
import {useUserStore} from '@/store/user'
import {ArrowDown, ArrowUp, QuestionFilled} from '@element-plus/icons-vue'
import {Search} from '@element-plus/icons-vue';

const router = useRouter();
if (!localStorage.getItem('console_token')) {
  router.push('/');
}
// ① 当前选中的过滤类型（'' 表示不过滤）
const scrollContainer = ref<HTMLElement | null>(null);
const agents = ref([]);
const appName = ref('');
const page = ref(1);
const loading = ref(false);
const hasMore = ref(true);
const allTags = ref([]);
const selectedTags = ref([]);
const getData = async () => {
  console.log('来了')
  if (loading.value || !hasMore.value) return;
  console.log('来了2')
  loading.value = true;
  let res1 = await getAppStatus();
  let res2 = await getAppClicks();
  try {
    const res = await getAgentsApi({
      page: page.value,
      limit: 100,
      is_created_by_me: false,
    });
    const tags = await getTags();
    allTags.value = tags;
    const statusMap = Object.fromEntries(
        res1.data.map(({app_id, only_me}) => [app_id, only_me])
    );
    const merged = [...agents.value, ...res.data];
    merged.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    agents.value = merged.map(app => ({
      ...app,
      only_me: statusMap[app.id] ?? false,
      click_count: res2.data[app.id]?.total_count ?? 0,
      user_count: res2.data[app.id]?.user_count ?? 0
    }));
    agents.value = agents.value.filter(item => item.only_me);
    // 用后端返回的 has_more 字段判断是否还有更多数据
    if (res.has_more === false) {
      hasMore.value = false;
    } else {
      page.value++;
    }
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};
const setTags = async () => {
  let tagIds = selectedTags.value?.length > 0 ? selectedTags.value.join(';') : undefined;
  console.log('tagIds', tagIds)
  await setTagIds(tagIds);
  await getData2();
}
const getData2 = async () => {
  console.log('收我的', selectedTags.value)
  loading.value = true;
  let res1 = await getAppStatus();
  let res2 = await getAppClicks();
  try {
    const res = await getAgentsApi({
      page: 1,
      limit: 100,
      name: appName.value,
      is_created_by_me: false,
      tag_ids: selectedTags.value?.length > 0 ? selectedTags.value.join(',') : undefined,
    });
    const statusMap = Object.fromEntries(
        res1.data.map(({app_id, only_me}) => [app_id, only_me])
    );
    const merged = [...res.data];
    merged.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
// 再统一做字段补充 / 映射
    agents.value = merged.map(app => ({
      ...app,
      only_me: statusMap[app.id] ?? false,
      click_count: res2.data[app.id]?.total_count ?? 0,
      user_count: res2.data[app.id]?.user_count ?? 0
    }));
    agents.value = agents.value.filter(item => item.only_me);
    console.log('agents.value', agents.value)
    // 用后端返回的 has_more 字段判断是否还有更多数据
    if (res.has_more === false) {
      console.log('没了')
      hasMore.value = false;
    } else {
      console.log('还有')
      page.value++;
    }
  } catch (error) {
    ElMessage.error('加载数据失败', error);
  } finally {
    loading.value = false;
  }
};
const goto = async (item) => {
  console.log('item', item)
  if (!localStorage.getItem('console_token')) {
    router.push('/login');
    return;
  }
  await saveClick(item.id);
  let res = await getAgentApi(item.id);
  if (!res.enable_site) {
    await getData();
    ElMessage.error('作者已设置他人禁用')
    return;
  }
  router.push(`/aimain/${res.site.access_token}?type=${item.mode}`);
};
const goto2 = (url: string) => {
  window.open(url, '_blank');
};
const goto3 = (url: string) => {
  ElMessage.warning('即将上线~')
};
const displayAgents = computed(() => {
  const group = {
    chat: [],
    'agent-chat': [],
    completion: [],
    'advanced-chat': [],
    workflow: [],
  };
  for (const agent of agents.value) {
    if (agent.name.includes('制度查询')) continue; // 隐藏单独展示
    if (group[agent.mode]) {
      group[agent.mode].push(agent);
    }
  }
  return group;
});
const getModeName = (mode: string) => {
  switch (mode) {
    case 'chat':
      return '聊天助手';
    case 'agent-chat':
      return 'Agent';
    case 'completion':
      return '文本生成';
    case 'advanced-chat':
      return 'ChatFlow';
    case 'workflow':
      return '工作流';
    default:
      return mode;
  }
};
const userStore = useUserStore()
const role = computed(() => {
  return userStore.userRole;
})
getData();
const modeCollapse = ref<Record<string, boolean>>({});

const toggleCollapse = (mode: string) => {
  modeCollapse.value[mode] = !modeCollapse.value[mode];
};

const isCollapsed = (mode: string) => {
  return modeCollapse.value[mode] ?? false;
};
const handleScroll = () => {
  if (!scrollContainer.value) return;
  const container = scrollContainer.value;
  const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;
  if (scrollBottom < 20) {
    getData();
  }
};

const topAgents = computed(() => {
  return [...agents.value]
      .sort((a, b) => b.click_count - a.click_count)
      .slice(0, 8);
});
const otherCollapse = ref(false);


const toggleOther = () => {
  otherCollapse.value = !otherCollapse.value;
};

const zhiduAgents = computed(() => {
  return agents.value.filter(item => item.name.includes('制度查询'));
});

const showZhiduDialog = ref(false);

const openZhiduApp = async (item) => {
  await saveClick(item.id);
  const res = await getAgentApi(item.id);
  if (!res.enable_site) {
    ElMessage.error('作者已设置他人禁用');
    return;
  }
  router.push(`/aimain/${res.site.access_token}?type=${item.mode}`);
};

const nowAppTags = ref([]);
const showNowAppTags = (tags) => {
  nowAppTags.value = tags.map(item => item.id);
}

const nowAppTagsEditDialog = ref(false);
const nowEditApp = ref({});
const showAppEdit = (item) => {
  nowAppTagsEditDialog.value = true;
  nowEditApp.value = item;
}
const newTag = ref('');
const addTagFuc = async () => {
  let res = await addTag({
    name: newTag.value,
    type: 'app',
  });
  allTags.value.push({
    name: newTag.value,
    id: res.id,
    type: res.type,
    binding_count: res.binding_count,
  });
}

const delTagFuc = async (tag) => {
  await delTag(tag.id);
  allTags.value = allTags.value.filter(item => item.id !== tag.id);
}
const lastClicked = ref(null)
const changeTag = async (app) => {
  const id = lastClicked.value
  if (!id) return

  if (nowAppTags.value.includes(id)) {
    console.log('新增:', id)
    await appBindTag({
      tag_ids: [id],
      target_id: app.id,
      type: "app"
    });
  } else {
    console.log('取消:', id, nowEditApp.value)
    await appDelTag({
      tag_ids: [id],
      target_id: app.id,
      type: "app"
    });
  }
  await getData2();

  lastClicked.value = null
}

const filterTags = ref('')
const filteredTags = computed(() => {
  const keyword = filterTags.value.trim().toLowerCase()
  if (!keyword) return allTags.value
  return allTags.value.filter(tag =>
      tag.name.toLowerCase().includes(keyword)
  )
})

const editIndex = ref(null)
const editName = ref('')
const editTag = (index, tag) => {
  editIndex.value = index
  editName.value = tag.name
}

const confirmEdit = async (index, tag) => {
  console.log('tag', tag)
  const newName = editName.value.trim()
  if (newName) {
    allTags.value[index].name = newName
  }
  editIndex.value = null
  await updateTag(tag.id, editName.value);
  await getData2();
}
const copyAgent = async (agent) => {
  console.log('agent', agent)
  ElMessageBox.confirm(
      `确定要复制应用 "${agent.name}" 吗？`,
      '复制确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(async () => {
        try {
          let data = {};
          if (agent.icon_type === 'image') {
            data = {
              icon: agent.icon,
              icon_type: agent.icon_type,
              mode: agent.mode,
              name: agent.name,
            }
          } else {
            data = {
              icon: agent.icon,
              icon_background: agent.icon_background,
              icon_type: agent.icon_type,
              mode: agent.mode,
              name: agent.name,
            }
          }
          await copyAgentFuc(agent.id, data);
          ElMessage.success('复制成功');
        } catch (err) {
          ElMessage.error('复制失败，请稍后重试');
        }
      })
      .catch(() => {
        ElMessage.info('已取消复制');
      });
};
const exportAgent = async (agent) => {
  try {
    // 调用接口，拿到文件内容
    const fileContent = await exportAgentFuc(agent.id);

    // 这里假设返回的是 YAML 文件内容
    const blob = new Blob([fileContent.data], {type: 'text/yaml;charset=utf-8'});

    // 创建一个下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'agent.yml'; // 你要保存的文件名
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  } catch (err) {
    console.error('导出失败：', err);
  }
}
const updatePublic = async (item) => {
  await setSiteStatus({
    only_me: item.only_me,
    app_id: item.id,
  });
}
const showDialog = ref(false);
onMounted(() => {
  getData();
  console.log('scrollContainer.value22', scrollContainer.value)
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll);
  }
  const route = useRoute();
  if (route.name === 'home') {
    const hasVisited = sessionStorage.getItem('hasVisitedHome');
    if (!hasVisited) {
      showDialog.value = true;
      setTimeout(() => {
        showDialog.value = false;
        sessionStorage.setItem('hasVisitedHome', 'true');
      }, 5000); // 显示5秒后自动关闭并标记
    }
  }
});
</script>

<style scoped lang="less">
.tech-bg {
  width: 100%;
  height: 150px;
  background: url('../assets/img/bg.png') no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  border-radius: 10px;
}

.info {
  height: 200px;
}

.tit {
  text-align: center;
  padding: 30px 30px 0;
  font-size: 36px;
  font-weight: 500;
  color: #fff;
}

.tit1 {
  margin-top: 24px;
  font-size: 20px;

  span {
    font-size: 16px;
    color: rgba(32, 41, 69, 0.6);
  }
}

.filters {
  display: flex;

  > span {
    margin-right: 10px;
    cursor: pointer;
    font-size: 20px;
  }

  img {
    width: 20px;
  }

  .textInfo {
    margin-left: 5px;
    vertical-align: 2px;
  }
}

.filterCard {
  margin-top: 10px;
  position: sticky;
  top: -16px;
  left: 0;
  background-color: #fff;
  z-index: 999;
}

.modelf {
  margin-bottom: 20px;
  border-radius: 10px;
  padding: 20px 20px 16px;
  border-color: rgba(82, 100, 154, 0.13);
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  cursor: pointer;
  background-color: #fff;
}

.model {
  display: flex;

  .emoji {
    font-size: 42px;
    border-radius: 14px;
    padding-bottom: 2px;
  }
}

.model img {
  width: 62px;
  height: 62px;
  border-radius: 8px;
}

.model span {
  color: #333333;
  font-size: 14px;
}

.model span:first-child {
  font-size: 18px;
}

.model span:last-child {
  color: rgba(56, 55, 67, .8);
}

.model span:nth-child(2) {
  color: rgba(56, 55, 67, .8);
  margin-bottom: 5px;
}

.modelf:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, .08), 0 8px 24px 0 rgba(0, 0, 0, .04);
  border-color: rgba(82, 100, 154, 0.13);

  .appTag {
    visibility: visible;
  }
}

.appTag {
  visibility: hidden;
}

.info {
  height: 40px;
}

.info2 {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
}

.info2 > :last-child {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info2 > :first-child {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  margin-bottom: 10px;
  text-overflow: ellipsis;
}

.info3 {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgb(240, 240, 245);
  color: gray;
  font-size: 12px;
  text-align: right;
}

.newImg {
  position: relative;

  .tag {
    position: absolute;
    right: 0;
    top: 50px;
    width: 20px;
    height: 20px;
  }
}

.countInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 60px;
}

.scroll-wrapper {
  height: calc(100vh);
  overflow-y: auto;
  padding-right: 10px;
}

.mode-title {
  display: flex;
  align-items: center;
  background-color: rgb(236, 245, 255);
  border-radius: 10px;
  padding: 10px 20px;
  margin-bottom: 10px;
  cursor: pointer;
}

.appTags {
  width: 100%;
  font-size: 14px;
  color: #98a2b2;
  padding: 7px 8px;
  border-radius: 7px;
  background-color: #fff;

  &:hover {
    background-color: #EAEBF1;
  }
}

.tagInfos {
  padding: 10px;

  :deep(.el-checkbox-group) {
    display: flex;
    flex-direction: column;
  }
}

.tagsList {
  max-height: 150px;
  overflow-y: auto;
  font-size: 14px;
}

.bindCount {
  margin: 0 5px;
}

.tag-list {
  flex-wrap: wrap;
}

.more {
  padding: 8px;
  cursor: pointer;
  font-size: 16px !important;
}

.more:hover {
  background-color: rgba(87, 104, 161, 0.08);
  border-radius: 8px;
}
</style>