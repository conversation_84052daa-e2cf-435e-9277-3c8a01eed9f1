<template>
  <div>
    <div class="layout-header">
      <span>所有反馈</span>
    </div>
    <div class="filter-container">
      <el-input v-model="searchQuery" placeholder="搜索用户或反馈内容" clearable @input="filterFeedbacks" style="width: 250px;" />
      <el-select v-model="filterStatus" placeholder="选择状态" clearable @change="filterFeedbacks" style="width: 150px;">
        <el-option label="全部" value=""></el-option>
        <el-option label="待回复" value="pending"></el-option>
        <el-option label="已回复" value="answered"></el-option>
      </el-select>
    </div>

    <el-table :data="filteredFeedbacks" stripe>
      <el-table-column type="index" label="序号" width="100" />
      <el-table-column prop="user_name" label="用户" />
      <el-table-column prop="feedback_content" label="反馈内容" />
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'answered' ? 'success' : 'warning'">
            {{ scope.row.status === 'answered' ? '已回复' : '待回复' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" type="primary" @click="viewFeedback(scope.row)">查看详情</el-button>
          <el-button size="small" type="warning" v-if="scope.row.status === 'pending'" @click="respondFeedback(scope.row)">回复</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 查看反馈详情弹窗 -->
    <el-dialog title="反馈详情" v-model="detailVisible" width="480px" class="feedback-dialog">
      <div class="feedback-detail">
        <div class="feedback-section">
          <p><strong>反馈内容：</strong>{{ selectedFeedback?.feedback_content }}</p>
        </div>
<!--        <div class="feedback-section">-->
<!--          <p><strong>反馈用户：</strong>{{ selectedFeedback?.user_id }}</p>-->
<!--        </div>-->
        <div class="feedback-section">
          <p><strong>状态：</strong>
            <el-tag :type="selectedFeedback?.status === 'answered' ? 'success' : 'warning'">
              {{ selectedFeedback?.status === 'answered' ? '已回复' : '待回复' }}
            </el-tag>
          </p>
        </div>
        <div class="feedback-section">
          <p><strong>管理员回复：</strong></p>
          <p v-if="selectedFeedback?.response?.String">{{ selectedFeedback.response.String }}</p>
          <p v-else class="no-response">暂无回复</p>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 回复反馈弹窗 -->
    <el-dialog title="回复反馈" v-model="respondVisible" width="480px">
      <el-input
          v-model="responseContent"
          type="textarea"
          placeholder="请输入您的回复内容"
          rows="4"
          class="input-content"
      ></el-input>
      <div class="dialog-footer">
        <el-button @click="respondVisible = false">取消</el-button>
        <el-button type="primary" @click="submitResponse">提交回复</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getAllFeedbacksApi, respondFeedbackApi, getFeedbackApi } from '@/api/feedback';
import { ElMessage } from 'element-plus';

const feedbacks = ref([]);
const filteredFeedbacks = ref([]);
const searchQuery = ref('');
const filterStatus = ref('');
const detailVisible = ref(false);
const respondVisible = ref(false);
const selectedFeedback = ref(null);
const responseContent = ref('');

// 加载所有反馈（管理员）
const loadFeedbacks = async () => {
  const res = await getAllFeedbacksApi();
  feedbacks.value = res.data;
  filterFeedbacks();
};

// 筛选反馈
const filterFeedbacks = () => {
  filteredFeedbacks.value = feedbacks.value?.filter((feedback) => {
    const matchesQuery =
        feedback.user_name?.includes(searchQuery.value) ||
        feedback.feedback_content.includes(searchQuery.value);
    const matchesStatus = filterStatus.value ? feedback.status === filterStatus.value : true;
    return matchesQuery && matchesStatus;
  });
};

// 查看反馈详情
const viewFeedback = async (feedback) => {
  const res = await getFeedbackApi(feedback.id);
  selectedFeedback.value = res.data;
  detailVisible.value = true;
};

// 回复反馈
const respondFeedback = (feedback) => {
  selectedFeedback.value = feedback;
  responseContent.value = '';
  respondVisible.value = true;
};

// 提交回复
const submitResponse = async () => {
  if (!responseContent.value.trim()) {
    ElMessage.error('回复内容不能为空');
    return;
  }

  await respondFeedbackApi(selectedFeedback.value.id, {
    response: responseContent.value.trim()
  });

  ElMessage.success('回复提交成功');
  respondVisible.value = false;
  loadFeedbacks();
};

// 初始化加载反馈列表
onMounted(() => {
  loadFeedbacks();
});
</script>

<style scoped>

.filter-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.feedback-dialog {
  max-width: 480px;
}

.feedback-detail {
  font-size: 14px;
  line-height: 1.6;
}

.feedback-section {
  margin-bottom: 10px;
}

.feedback-section strong {
  font-weight: 600;
  color: #333;
}

.no-response {
  color: #999;
  font-style: italic;
}

.dialog-footer {
  text-align: right;
  margin-top: 15px;
}
</style>
