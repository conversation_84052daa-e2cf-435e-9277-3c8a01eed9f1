<template>
  <div class="wrapper">
    <v-header/>
    <v-sidebar/>
    <div class="content-box" :class="{ 'content-collapse': sidebar.collapse }">
      <!--            <v-tabs></v-tabs>-->
      <div class="content">
        <router-view :key="$route.fullPath" v-slot="{ Component }">
          <transition name="move" mode="out-in">
            <keep-alive :include="tabs.nameList">
              <component :is="Component"></component>
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {useSidebarStore} from '@/store/sidebar';
import {useTabsStore} from '@/store/tabs';
import vHeader from '@/components/header.vue';
import vSidebar from '@/components/sidebar.vue';
import {onMounted} from "vue";
import {useUserStore} from '@/store/user'
import {useWorkspaceStore} from '@/store/workspace'

import {getNowUser} from '@/api/user'
import {getWorkspaces} from '@/api/workspace'
import {getCurrentWorkspaces,getDefaultModel,getAllAccount} from '@/api/system'
// import vTabs from '@/components/tabs.vue';
const userStore = useUserStore()
const workspaceStore = useWorkspaceStore()
const sidebar = useSidebarStore();
const tabs = useTabsStore();

onMounted(async () => {
  console.log('来了呀')
  let res = await getNowUser();
  userStore.setUser(res);
  let res2 = await getCurrentWorkspaces();
  console.log('res2',res2)
  userStore.setWorkspace(res2);
  let res3 = await getDefaultModel();
  console.log('res3',res3)
  userStore.setDefaultModel(res3.data);
  console.log('defaultModel',userStore,userStore.defaultModel)
  // let res4 = await getAllAccount({account_id:userStore.user.id});
  // console.log('res4',res4)
  // let role = res4.data.data.find(item=>item.TenantID === userStore.workspaceId)?.Role;
  // console.log('role',role)
  userStore.setUserRole(res2.role);
  let res4 = await getWorkspaces();
  workspaceStore.setList(res4.workspaces);
});
</script>

<style>
.wrapper {
  height: 100vh;
  overflow: hidden;
}

.content-box {
  position: absolute;
  left: 274px;
  right: 0;
  top: 0;
  bottom: 0;
  padding-bottom: 30px;
  -webkit-transition: left 0.3s ease-in-out;
  transition: left 0.3s ease-in-out;
  background: #FCFCFF;
  overflow: hidden;
}

.content {
  width: 100%;
  padding: 16px 24px;
  height: calc(100vh - 10px);
  overflow-y: auto;
  box-sizing: border-box;
}

.content::-webkit-scrollbar {
  width: 0;
}

.content-collapse {
  left: 274px;
}
.el-switch__core {
  border-radius: 5px!important;
}
</style>
