<template>
  <div class="container">
    <div class="backIcon back" @click="goList">
      <el-icon style="vertical-align: -2px;">
        <ArrowLeft/>
      </el-icon>
      返回
    </div>
    <div class="mt-4 mb-4">
      <el-button size="small" type="primary" v-if="isAdmin" @click="addDocument">新增</el-button>
    </div>
    <el-tabs tab-position="left" class="tabs" @tabChange="changeTab" v-model="nowID">
      <el-tab-pane :label="item.doc_type" v-for="item in infoList" :name="item.id"
      :key="item.id">
        <h3 class="mb-4">{{item.doc_type}}
          <el-button class="ml-1" size="small" type="primary" v-if="isAdmin" @click="editDialog = true">编辑</el-button>
          <el-button size="small" type="danger" v-if="isAdmin" @click="deleteDocument(item.id)">删除</el-button>
        </h3>
        <div class="hideOther">
          <MdEditor v-model="info"/>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
        v-model="editDialog"
        title="编辑文档"
        width="1200"
    >
      <div>
        <md-editor v-model="info" @on-upload-img="onUploadImg"/>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialog = false">取消</el-button>
          <el-button type="primary" @click="saveDocument">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import router from "@/router";
import {marked} from 'marked'
import {getDocuments, getDocumentsByDoctype,delDocuments,upDocuments, getDocumentsByID,putDocumentsByDoctype,addDocuments} from '@/api/userDocument'
import {workspacesCurrent, uploadFile} from '@/api/system'
import {computed, ref} from "vue";
import MdEditor from "md-editor-v3";
import 'md-editor-v3/lib/style.css';
import {ElMessage} from "element-plus";

const isAdmin = ref(false);
const editDialog = ref(false);

const goList = () => {
  router.push('/home');
}
const showInfo = computed(() => {
  return marked.parse(info.value);
})
const infoList = ref([]);
const info = ref('');
const nowType = ref('');
const nowID = ref(1);
const loadData = async () => {
  let res = await getDocuments();
  let res2 = await getDocumentsByID(res.data[0].id);
  infoList.value = res.data;
  info.value = res2.data.content;
  nowID.value = res2.data.id;
  let res3 = await workspacesCurrent();
  isAdmin.value = res3.role === 'admin' || res3.role === 'owner';
}

const saveDocument = async () => {
  await upDocuments(nowID.value, info.value);
  editDialog.value = false;
}

const onUploadImg = async (files: File[], callback: (urls: string[]) => void) => {
  try {
    const formData = new FormData();
    formData.append('file', files[0]); // 如果你想支持多图，可以循环添加

    const res = await uploadFile(formData);

    // 根据实际后端返回结构调整
    let urlImg = location.origin + import.meta.env.VITE_BASE_API_URL2 + "/file/" + res.id;
    callback([urlImg]);
  } catch (error) {
    ElMessage.error('图片上传异常');
    console.error('上传图片错误', error);
  }
};

const addDocument = async () => {
  try {
    const { value } = await ElMessageBox.prompt(
        '请输入文档名：',
        '新建文档',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '如 使用说明',
          inputValidator: val => val.trim() ? true : '文档名不能为空',
        }
    );
    // 调用接口添加文档
    let res = await addDocuments({
      content: '点击编辑输入内容',
      doc_type: value.trim(),
    });
    await loadData();
    await changeTab(res.data.id);
  } catch (e) {
    // 用户取消或关闭弹窗
  }
}

const changeTab = async (nowTab)=>{
  nowID.value = nowTab;
  let res2 = await getDocumentsByID(nowTab);
  info.value = res2.data.content;
}

const deleteDocument = async (id)=>{
  await delDocuments(id);
  await loadData();
}
loadData();


</script>

<style scoped lang="less">
.container {
  min-height: 100vh;
}

.tabs {

  line-height: 30px;
}

.back {
  display: inline-block;
}

.hideOther {
  :deep(.md-editor) {
    height: auto;
  }

  :deep(.md-editor-toolbar-wrapper) {
    display: none !important;
  }

  :deep(.md-editor-input-wrapper) {
    display: none !important;
  }

  :deep(.md-editor-footer) {
    display: none !important;
  }

  :deep(.md-editor-v3-preview) {
    min-height: 100vh;
  }

}

:deep(.md-editor-toolbar-right > .md-editor-toolbar-item:last-child) {
  display: none !important;
}
</style>
