<template>
  <div class="main">
    <div class="top">
      <div class="info">
        <el-icon @click="goUrl" class="backIcon">
          <arrow-left/>
        </el-icon>
        <div>
          <img v-if="robotInfo.icon_type!=='emoji'" :src="robotInfo.icon_url" alt=""/>
          <div v-else>
            <div class="emoji" :style="{background:robotInfo.icon_background}">{{ robotInfo.icon }}</div>
          </div>
          <div>{{ robotInfo.name }}</div>
          <el-tooltip
              effect="dark"
              content="编辑应用"
              placement="top-start"
          >
            <el-icon class="botIcon" @click="showEdit">
              <Edit/>
            </el-icon>
          </el-tooltip>

          <el-tooltip
              effect="dark"
              content="应用监控"
              placement="top-start"
          >
            <el-icon class="botIcon" @click="showCalc" >
              <Histogram/>
            </el-icon>
          </el-tooltip>
        </div>
      </div>
      <div class="rightInfo">
        <!--        <div class="publishBtn">上次修改时间： {{ $moment(robotInfo.updated_at).format('YYYY-MM-DD HH:mm') }}</div>-->
        <el-button type="primary" @click="toUpdateAgents2">发布</el-button>
      </div>
    </div>
    <div class="frames">
      <div>
        <div>编排</div>
        <div class="list">
          <el-card class="var-card" shadow="never" style="height: 300px">
            <div class="title">
              <span>提示词
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="提示词用于对 AI 的回复做出一系列指令和约束。可插入表单变量，例如 {{input}}。这段提示词不会被最终用户所看到。"
                  placement="top-start"
              >
                  <QuestionFilled style="width: 12px"/>
                </el-tooltip>
              </span>
              <div>
                <el-tag style="margin-left: 10px;cursor: pointer" click="" @click="createPrompt = true">
                  <img src="@/assets/img/bots/stars.png" alt="" style="width: 13px;margin-right: 3px;">生成
                </el-tag>
                <!--                <img src="@/assets/img/bots/brush.png" alt="清空" title="清空" class="icon3"-->
                <!--                     @click="clearPrompt">-->
              </div>
            </div>
            <el-input class="editable-placeholder" type="textarea" v-model="nowPromtInfo" :rows="11"
                      @blur="handlePromptBlur"/>
          </el-card>
          <el-card
              class="var-card"
              shadow="never"
          >
            <div class="title">
              <span>变量
                <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="变量将以表单形式让用户在对话前埴写，用户埴写的表单内容将自动替换提示词中的变量。"
                    placement="top-start"
                >
                  <QuestionFilled style="width: 12px"/>
                </el-tooltip>
                </span>
              <el-dropdown
                  trigger="click"
                  placement="bottom-end"
                  @command="handleAdd"
              >
                <span class="hoverIcon">+ 添加</span>

                <!-- ↓↓↓ 下拉面板 ↓↓↓ -->
                <template #dropdown>
                  <el-dropdown-menu class="add-menu">
                    <!-- 文本 -->
                    <el-dropdown-item command="text-input">
                      <el-icon>
                        <document/>
                      </el-icon>
                      <span class="label">文本</span>
                    </el-dropdown-item>

                    <!-- 段落 -->
                    <el-dropdown-item command="paragraph">
                      <el-icon>
                        <notebook/>
                      </el-icon>
                      <span class="label">段落</span>
                    </el-dropdown-item>

                    <!-- 下拉选项 -->
                    <el-dropdown-item command="select">
                      <el-icon>
                        <finished/>
                      </el-icon>
                      <span class="label">下拉选项</span>
                    </el-dropdown-item>

                    <!-- 数字 -->
                    <el-dropdown-item command="number">
                      <el-icon>
                        <Grid/>
                      </el-icon>
                      <span class="label">数字</span>
                    </el-dropdown-item>

                    <!-- 分隔线 -->
                    <!--                    <el-dropdown-item disabled divided style="cursor:default">-->
                    <!--                      基于 API 的变量-->
                    <!--                    </el-dropdown-item>-->
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <div class="tip" v-if="model_config?.user_input_form?.length<=0">变量能使用户输入表单引入提示词或开场白，你可以试试在提示词中输入
              {{ input }}
            </div>
            <div
                v-if="model_config?.user_input_form?.length"
                class="var-card"
                shadow="never"
            >
              <div
                  v-for="(raw, idx) in model_config.user_input_form"
                  :key="idx"
                  class="var-line"
              >
                <!-- 内层循环：拿到 {type, cfg} -->
                <div v-for="(cfg, type) in raw" :key="type">
                  <div>
                    <el-icon class="var-mark">
                      <img src="@/assets/img/svg/var.svg" alt="" style="width: 18px"/>
                    </el-icon>

                    <!-- 基本信息 -->
                    <span class="var-key">{{ cfg.variable }}</span>
                    <span class="sep">·</span>
                    <span class="var-label">{{ cfg.label }}</span>
                  </div>
                  <div>
                    <span class="var-tags">
                      <el-tag size="small" effect="plain" type="info" v-if="cfg.required">
                        REQUIRED
                      </el-tag>
                      <span v-if="type==='text-input'" style="margin: 0 10px;font-size: 12px;color:rgb(103, 111, 131)">string</span>
                      <span v-else style="margin: 0 10px;font-size: 12px;color:rgb(103, 111, 131)">{{ type }}</span>
                      <el-tag size="small" class="type">
                        <!-- 可根据类型换不同图标 -->
                        <template v-if="type==='number'">#</template>
                        <template v-else-if="type==='paragraph'">≡</template>
                        <template v-else-if="type==='select'">▤</template>
                        <template v-else>T</template>
                      </el-tag>
                    </span>
                    <span class="var-actions">
                    <el-icon class="icon-btn" style="margin-right: 10px;" @click="openEditor(idx)"><Edit/></el-icon>
                    <el-icon class="icon-btn" @click="handleDelete(idx)"><Delete/></el-icon>
                  </span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
          <el-card class="var-card" shadow="never">
            <div class="title">
              <span>知识库</span>
              <span class="hoverIcon" @click="showAddKnowledge">+ 添加</span>
            </div>
            <div v-if="selectKnowledgesIds.length<=0" class="tip">您可以导入知识库作为上下文</div>
            <div v-for="item in selectKnowledgesIds" class="knowledgesLine">
              <div>
                <img src="@/assets/img/svg/bag.svg" alt="" style="width: 13px"/>
                <span class="ellipsis">{{ knowledges.find(i => i.id === item)?.name }}</span>
              </div>
              <div class="var-actions" @click="delKnowledge(item)">
                <span class="hoverIcon"><Delete style="width: 12px;"/></span>
              </div>
            </div>
            <div v-if="selectKnowledgesIds.length>0 && robotInfo.mode == 'completion'"
                 class="query-var-row flex justify-between">
              <div class="flex items-center">
                <el-icon class="var-mark">
                  <img src="@/assets/img/svg/var.svg" alt="" style="width:18px"/>
                </el-icon>
                <span class="searchVar">查询变量</span>
                <el-tooltip content="选择哪个输入作为知识库检索查询" placement="top">
                  <el-icon style="font-size:20px">
                    <QuestionFilled/>
                  </el-icon>
                </el-tooltip>
              </div>
              <el-select
                  v-model="model_config.dataset_query_variable"
                  placeholder="请选择变量"
                  class="query-var-select"
              >
                <el-option
                    v-for="opt in variableOptions"
                    :key="opt.variable"
                    :label="opt.label"
                    :value="opt.variable"
                />
              </el-select>
            </div>
          </el-card>
          <el-card class="var-card" shadow="never" v-if="robotInfo.mode == 'agent-chat'">
            <div class="title">
              <span>工具</span>
              <span class="hoverIcon" @click="showAddTool">+ 添加</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="24" :md="12" v-for="item in model_config.agent_mode.tools"
                      class="knowledgesLine knowledgesLine2">
                <div>
                  <span class="ellipsis">{{ item.provider_name }}</span>
                  <span class="ellipsis" style="color:rgb(103, 111, 131)">{{ item.tool_label }}</span>
                </div>
                <div class="flex items-center">
                  <el-icon class="hoverIcon var-actions" @click="showTool(item)">
                    <SetUp/>
                  </el-icon>
                  <el-icon class="hoverIcon var-actions" @click="deleteTool(item)">
                    <Delete/>
                  </el-icon>
                  <el-switch size="small" v-model="item.enabled"></el-switch>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </div>
      <div class="testInfo">
        <div class="flex justify-between items-center">
          <div class="modelName">
            <span class="mr-2">预览</span>
            <el-form-item label="">
              <el-dropdown trigger="click">
                <!-- 触发器：务必只有一个根元素 -->
                <div class="el-dropdown-link flex items-center gap-2 cursor-pointer">
                  <img
                      src="@/assets/img/bots/deepseek.png"
                      class="w-5 h-5"
                      alt=""
                  />
                  <span>deepseek</span>
                  <el-icon class="ml-1">
                    <arrow-down/>
                  </el-icon>
                </div>
                <!-- 下拉面板 -->
                <template #dropdown>
                  <el-dropdown-menu style="width: 220px">
                    <el-dropdown-item class="flex items-center gap-2">
                      <img
                          src="@/assets/img/bots/deepseek.png"
                          style="width: 15px;height: 15px;"
                          alt=""
                      />
                      <span>deepseek</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-form-item>
            <el-dropdown trigger="click">
              <el-icon class="ml-1 hoverIcon">
                <el-icon style="font-size: 20px;">
                  <SetUp/>
                </el-icon>
              </el-icon>
              <template #dropdown>
                <div class="knowledges">
                  <h3 class="mb-1">参数</h3>
                  <el-form :model="settingForm" label-width="auto" class="settingForm" label-position="top">
                    <el-form-item
                        v-for="item in sliders"
                        :key="item.prop"
                        class="settingLine"
                        :label="item.label"
                        label-position="left"
                    >
                      <template #label>
                        <div class="flex items-center">
                          <el-switch size="small" @click="changeActive(item)" v-model="item.active"
                                     :active-value="true" :inactive-value="false"></el-switch>
                          <span style="margin: 0 10px;">{{ item.label }}</span>
                          <el-tooltip :content="item.tooltip" placement="top">
                            <el-icon style="font-size: 20px">
                              <QuestionFilled/>
                            </el-icon>
                          </el-tooltip>
                        </div>
                      </template>
                      <el-slider
                          v-model="settingForm[item.prop]"
                          :min="item.min"
                          :max="item.max"
                          :step="item.step"
                          style="width: 100px;margin: 0 20px 0 50px;"
                      />
                      <span v-if="settingForm[item.prop]"
                            style="margin: 0 30px;">{{ settingForm[item.prop].toFixed(item.precision) }}</span>
                      <span v-else style="margin: 0 30px;">{{ item.defalutValue }}</span>
                    </el-form-item>
                  </el-form>
                </div>
              </template>
            </el-dropdown>
          </div>
          <div>
            <el-tooltip
                class="box-item"
                effect="dark"
                content="用户输入字段"
                placement="top-start"
            >
              <el-icon class="ml-1 hoverIcon" style="font-size: 20px;" @click="showUserForm = !showUserForm">
                <Operation/>
              </el-icon>
            </el-tooltip>
            <el-tooltip
                v-if="robotInfo.mode != 'completion'"
                class="box-item"
                effect="dark"
                content="管理"
                placement="top-start"
            >
              <el-icon class="ml-1 hoverIcon" style="font-size: 20px;" @click="showAppSetting = !showAppSetting">
                <Expand/>
              </el-icon>
            </el-tooltip>
          </div>
        </div>
        <div class="maininfo">
          <el-card class="var-card var-card2 m-2" v-if="model_config?.user_input_form?.length>0 && showUserForm">
            <div v-if="robotInfo.mode === 'completion'" class="mb-1">
              <h3 class="font-14">用户输入</h3>
              <div class="font-12">填入变量的值，该值将在每次提交问题时自动替换到提示词中</div>
            </div>
            <div v-for="(item,key) in model_config?.user_input_form" class="mb-1">
              <template v-if="item[Object.keys(item)].label">
                {{ item[Object.keys(item)]?.label }} <span class="font-12" v-if="!item[Object.keys(item)]?.required">（选填）</span>
                <el-input v-if="Object.keys(item) != 'select'" v-model="userForm[item[Object.keys(item)]?.label]"
                          :placeholder="item[Object.keys(item)]?.label"
                          :type="Object.keys(item) == 'paragraph'?'textarea':''"
                          :rows="Object.keys(item) == 'paragraph'?5:null"/>
                <el-select v-else v-model="userForm[item[Object.keys(item)]?.label]">
                  <el-option v-for="item in item[Object.keys(item)]?.options" :value="item">{{ item }}</el-option>
                </el-select>
              </template>
            </div>
            <div v-if="robotInfo.mode === 'completion'" class="mb-1 flex justify-between mt-4">
              <el-button>清空</el-button>
              <el-button type="primary" :loading="isLoading" @click="sendMessage2">
                <el-icon>
                  <CaretRight/>
                </el-icon>
                运行
              </el-button>
            </div>
          </el-card>
          <div class="chatLine" v-if="model_config.opening_statement && showOpenStatement">
            <div>
              <div class="assistant-body">
                <div v-html="model_config.opening_statement"></div>
                <div class="mt-1">
                  <el-button @click="quickSendMessage(item)" v-for="item in model_config.suggested_questions">
                    {{ item }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <template
              v-if="(!model_config?.user_input_form?.length>0 && !model_config.opening_statement) || messagesInfo.length > 0">
            <template v-if="messagesInfo.length <= 0">
              <div class="aiinfo">
                <img v-if="robotInfo.icon_type!=='emoji'" :src="robotInfo.icon_url" alt=""/>
                <div v-else>
                  <div class="emoji" :style="{background:robotInfo.icon_background}">{{ robotInfo.icon }}</div>
                </div>
                <span>{{ robotInfo.name }}</span>
              </div>
            </template>
            <template v-else>
              <div class="chats" v-for="(item, index) in messagesInfo" :key="index">
                <!-- 用户消息 -->
                <div class="chatLine chatLine2">
                  <div>
                    <div class="assistant-body">
                      <div v-html="item.query"></div>
                    </div>
                  </div>
                </div>

                <!-- AI 回复消息 -->
                <div class="chatLine">
                  <div>
                    <ThinkBlock
                        v-if="item.isThinking && !item.disable"
                        :content="item.answer"
                        :is-complete="item.hasThinkEnd"
                    />
                    <div class="assistant-body" v-if="extractBodyContent(item.answer)">
                      <!-- 主回答内容 -->
                      <div v-html="extractBodyContent(item.answer)"></div>

                      <!-- 引用文件部分 -->
                      <div
                          v-if="item.metadata?.retriever_resources && item.metadata.retriever_resources.length"
                          class="ref-container"
                      >
                        <el-divider content-position="left">
                          <div class="ref-title">引用</div>
                        </el-divider>
                        <div class="ref-list">
                          <div
                              class="ref-item"
                              v-for="(cite, i) in item.metadata.retriever_resources.slice(0, 1)"
                              :key="i"
                          >
                            <el-icon class="ref-icon">
                              <document/>
                            </el-icon>
                            <span class="ref-name">{{ cite.document_name }}</span>
                          </div>
                          <span
                              v-if="item.metadata.retriever_resources.length > 1"
                              class="ref-more"
                          >
              + {{ item.metadata.retriever_resources.length - 1 }}
            </span>
                        </div>
                      </div>
                    </div>
                    <el-tag class="stop hoverIcon" @click="stopChatMessage" v-if="!item.hasThinkEnd && isLoading">
                      <el-icon>
                        <CircleCloseFilled/>
                      </el-icon>
                      停止响应
                    </el-tag>
                  </div>
                </div>
              </div>

            </template>
          </template>
        </div>
        <div v-if="robotInfo.mode !== 'completion'" class="messageLine" v-loading="isLoading">
          <img
              src="@/assets/img/bots/brush.png"
              alt="新的聊天"
              title="新的聊天"
              class="icon icon-clear"
              @click="clearMessage"
          />
          <!-- 输入框 -->
          <input
              ref="messageRef"
              type="text"
              placeholder="请输入内容..."
              :disabled="isLoading"
              v-model="message"
              @keyup.enter="sendMessage"
          />
          <!-- 发送按钮 放最右 -->
          <img
              src="@/assets/img/bots/send.png"
              alt="发送"
              title="发送"
              class="icon icon-send"
              @click="sendMessage"
          />
          <div v-if="enabledSuggestion && suggestionsBox.length>0 && !isLoading" class="suggestionsBox">
            <div class="flex justify-center">
              <el-divider>
                <span>试着问问</span>
              </el-divider>
            </div>
            <div class="flex justify-center items-center">
              <el-tag class="mr-1 cursor" size="small" v-for="item in suggestionsBox" @click="quickSendMessage(item)">
                {{ item }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
        title="编辑应用"
        v-model="dialogVisible"
        width="480px"
    >
      <el-form :rules="ruleForm.icon_type === 'emoji' ? rules : rules2" :model="ruleForm" label-position="top"
               ref="ruleFormRef">
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="ruleForm.name" placeholder="为应用指定一个唯一的名称"/>
        </el-form-item>
        <el-form-item label="应用功能说明" prop="description">
          <el-input v-model="ruleForm.description" type="textarea" placeholder="介绍应用功能并向应用用户展示"
                    :rows="5"/>
        </el-form-item>
        <el-radio-group v-model="ruleForm.icon_type" class="icon_type">
          <el-radio-button label="emoji">表情图标</el-radio-button>
          <el-radio-button label="image">上传图片</el-radio-button>

        </el-radio-group>
        <el-form-item v-if="ruleForm.icon_type === 'emoji'" label="选择表情" prop="icon">
          <el-popover placement="bottom" width="300" trigger="click">
            <template #reference>
              <div class="showIcon">{{ ruleForm.icon || '🤖' }}</div>
            </template>
            <div class="emoji-grid">
              <span v-for="emoji in emojiList" :key="emoji" @click="ruleForm.icon = emoji">
                {{ emoji }}
              </span>
            </div>
          </el-popover>
        </el-form-item>

        <!-- 上传模式 -->
        <el-form-item v-else label="图标" prop="icon">
          <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeUpload"
              :on-error="handleUploadError"
          >
            <template v-if="!ruleForm.iconImage && ruleForm.icon_type === 'image'">
              <img src="@/assets/img/index/watiUpImg.png" class="watiUpImg"/>
            </template>
            <template v-else>
              <img :src="ruleForm.iconImage" class="watiUpImg"/>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button @click="toUpdateAgents">提交</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="createPrompt" title="提示词生成器" width="1140">
      <div class="promtBox">
        <div>
          <div>提示词生成器使用配置的模型来优化提示词，以获得更高的质量和更好的结构。请写出清晰详细的说明。</div>
          <div style="color:#333">试一试</div>
          <div class="tags">
            <span @click="promptInfo = '一个可以翻译多种语言的翻译器'"><el-icon><Postcard/></el-icon>翻译机器人</span>
            <span @click="promptInfo = '将会议内容提炼总结，包括讨论主题、关键要点和待办事项'"><el-icon><Postcard/></el-icon>总结会议纪要</span>
            <span @click="promptInfo = '用地道的编辑技巧改进我的文章'"><el-icon><Postcard/></el-icon>润色文章</span><br/>
            <span @click="promptInfo = '从长篇报告中提取洞察、识别风险并提炼关键信息'"><el-icon><Postcard/></el-icon>职业分析师</span>
            <span @click="promptInfo = '一个可以让小白用户理解、使用和创建 Excel 公式的对话机器人'"><el-icon><Postcard/></el-icon>Excel 公式专家</span>
            <span @click="promptInfo = '旅行规划助手是一个智能工具，旨在帮助用户轻松规划他们的旅行'"><el-icon><Postcard/></el-icon>旅行规划助手</span>
          </div>
          <h4>指令</h4>
          <el-input v-model="promptInfo" type="textarea"
                    placeholder="写下具体、清晰的说明。"
                    :rows="10" :cols="40"/>
          <el-button class="createBtn" type="primary" @click="toAICreateAgent" :disable="!promptInfo">
            生成
          </el-button>
        </div>
        <div class="showInfo" v-loading="createPromptLoading" element-loading-text="为您编排应用程序中…">
          <div v-if="!resPrompt.prompt" class="tipInfo">
            在左侧描述您的用例，
            编排预览将在此处显示。
          </div>
          <div v-else>
            <div class="promptInfo promptInfo1">
              <div>提示词</div>
              <el-input type="textarea" disabled v-model="resPrompt.prompt" :rows="10" :cols="60"/>
            </div>
            <div class="promptInfo" v-if="resPrompt?.variables?.length>0">
              <div>变量</div>
              <div style="padding: 0 20px 10px;">
                <div v-for="item in resPrompt?.variables" class="tags">
                  {{ item }}
                </div>
              </div>
            </div>
            <div style="margin-bottom: 10px;">聊天增强</div>
            <div class="promptInfo">
              <div>对话开场白</div>
              <div class="text1">
                {{ resPrompt.opening_statement }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer v-if="resPrompt?.variables">
        <div class="dialog-footer">
          <el-button @click="createPrompt = false">取消</el-button>
          <el-button type="primary" @click="toReplacePrompt">应用</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        title="选择引用知识库"
        v-model="dialogSelectKnowledges"
        width="380px"
    >
      <div class="dialogBody">
        <div v-for="item in knowledges" class="knowledgesLine"
             :class="{'active':tempSelectKnowledges.includes(item.id)}"
             @click="addTempKnowledges(item)">
          <div>
            <!--            <Document style="width: 13px"/>-->
            <img src="@/assets/img/svg/bag.svg" alt="" style="width: 13px"/>
            <span class="ellipsis">{{ item.name }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <span class="footerTip">{{ tempSelectKnowledges.length }} 个知识库被选中</span>
          <el-button @click="dialogSelectKnowledges = false">取消</el-button>
          <el-button @click="toAddKnowledge" type="primary">添加</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        title="选择工具"
        v-model="showAddToolDialog"
        width="380px"
    >
      <div class="dialogBody">
        <div v-for="item in allTools">
          <div class="mb-1">
            <img :src="item.icon" alt="" style="width: 13px"/>
            {{ item.label.zh_Hans }}
          </div>
          <div>
            <div v-for="item2 in item.tools" class="knowledgesLine" @click="addMyTools(item,item2)"
                 :class="{'active':model_config.agent_mode.tools.find(item=>item.tool_name === item2.name)}">
              <span class="ellipsis">{{ item2.label.zh_Hans }}</span>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddToolDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        title="对话开场白"
        v-model="showEditOpeningDialog"
        width="800px"
    >
      <div class="dialogBody2">
        <el-input type="textarea" :rows="10" :cols="20" v-model="tempOpeningVal"></el-input>
        <div class="divider"/>

        <!-- 回答列表 -->
        <div class="flex justify-between items-center mb-2">
          <span class="font-medium">回答</span>
          <el-button link type="primary" @click="addAnswer">
            <el-icon>
              <Plus/>
            </el-icon>
            添加
          </el-button>
        </div>

        <!-- 动态行 -->
        <div
            v-for="(ans, idx) in tempAnswers"
            :key="idx"
            class="answer-line"
        >
          <el-input
              v-model="tempAnswers[idx]"
              placeholder="输入回答内容"
              clearable
              class="flex-1"
          />
          <el-icon class="ml-2 hoverIcon" @click="delAnswer(idx)">
            <Delete/>
          </el-icon>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditOpeningDialog = false">取消</el-button>
          <el-button @click="updateOpeningVal" type="primary">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="showCalcDialog"
        title="应用监控"
        fullscreen
        destroy-on-close
    >
      <!-- 头部：预设区间 + 自定义日期 -->
      <div class="monitor-header">
        <el-select v-model="monitorPreset" style="width: 160px" @change="applyMonitorPreset">
          <el-option v-for="p in monitorPresets" :key="p.value" :label="p.label" :value="p.value"/>
        </el-select>

        <el-date-picker
            v-model="monitorRange"
            type="daterange"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="margin-left: 12px"
            @change="fetchMonitorStats"
        />
      </div>

      <!-- 折线图 -->
      <el-row>
        <el-col :span="24" :md="12">
          <h3>{{monitorOption.series[0].name}}</h3>
          <VChart
              class="monitor-chart"
              :option="monitorOption"
              autoresize
          />
        </el-col>
        <el-col :span="24" :md="12">
          <h3>{{monitorOption2.series[0].name}}</h3>
          <VChart
              class="monitor-chart"
              :option="monitorOption2"
              autoresize
          />
        </el-col>
        <el-col :span="24" :md="12">
          <h3>{{monitorOption3.series[0].name}}</h3>
          <VChart
              class="monitor-chart"
              :option="monitorOption3"
              autoresize
          />
        </el-col>
        <el-col :span="24" :md="12">
          <h3>{{monitorOption4.series[0].name}}</h3>
          <VChart
              class="monitor-chart"
              :option="monitorOption4"
              autoresize
          />
        </el-col>
      </el-row>

      <template #footer>
        <el-button @click="showCalcDialog = false">关闭</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogAddArr" width="500" @close="$emit('update:visible', false)">
      <template #header>
        <span class="text-lg font-semibold">编辑变量</span>
      </template>
      <el-form :model="addArrForm" :rules="addArrRules" ref="arrRuleForm" label-width="90px" label-position="top">
        <el-form-item label="字段类型" prop="name">
          <el-radio-group v-model="addArrForm.type" class="type-grid" size="large">
            <el-radio-button
                v-for="item in fieldTypes"
                :key="item.value"
                :label="item.value"
            >
              <template #default>
                <div class="arrItem">
                  <el-tag size="small" class="type" style="color:gray">
                    {{ item.icon }}
                  </el-tag>
                  <span>{{ item.label }}</span>
                </div>
              </template>
            </el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="变量名称" prop="variable">
          <el-input v-model="addArrForm.variable"/>
        </el-form-item>

        <el-form-item label="显示名称" prop="label">
          <el-input v-model="addArrForm.label" @focus="checkVar"/>
        </el-form-item>

        <!-- 最大长度，仅文本/段落可见 -->
        <el-form-item v-if="showMaxLength" label="最大长度" prop="maxLength">
          <el-input-number v-model="addArrForm.max_length" :min="1" style="width: 100%"/>
        </el-form-item>

        <!-- 下拉选项，仅下拉可见 -->
        <el-form-item v-if="addArrForm.type === 'select'" label="选项">
          <div class="w-full">
            <div v-for="(option, idx) in addArrForm.options" :key="idx" class="flex items-center mb-2 gap-2">
              <el-input v-model="addArrForm.options[idx]" class="flex-1"/>
              <el-icon @click="removeOption(idx)" class="hoverIcon">
                <Delete/>
              </el-icon>
            </div>
            <el-button type="primary" plain size="small" @click="addOption">
              <el-icon>
                <Plus/>
              </el-icon>
              添加选项
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="" prop="required">
          <el-checkbox v-model="addArrForm.required">必填</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogAddArr = false">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="submitting">保存</el-button>
      </template>
    </el-dialog>
    <el-drawer v-model="showAppSetting" di rection="rtl" :close-on-click-modal="false" @close="toUpdateAgents2">
      <template #header>
        <div>
          <h4>功能</h4>
          <div style="font-size: 12px">增强 web app 用户体验</div>
        </div>
      </template>
      <template #default>
        <div class="functions">
          <el-card class="var-card" shadow="never" @mouseenter="showEditOpening = true"
                   @mouseleave="showEditOpening = false">
            <div class="mb-1 flex justify-between">
              <div class="flex items-center">
                <img src="@/assets/img/svg/open.svg" alt="" style="width: 30px;height: 30px;"/>
                <span class="ml-1">对话开场白</span>
              </div>
              <el-switch v-model="showOpenStatement"/>
            </div>
            <div class="font-12 opening">
              {{ model_config.opening_statement }}

            </div>
            <div v-if="showEditOpening" class="flex items-center editOpen font-14" @click="toShowEditOpeningDialog">
              <Edit class="edit"/>
              编写开场白
            </div>
          </el-card>
          <el-card class="var-card" shadow="never">
            <div class="mb-1 flex justify-between">
              <div class="flex items-center">
                <img src="@/assets/img/svg/next.svg" alt="" style="width: 30px;height: 30px;"/>
                <span class="ml-1">下一步问题建议</span>
              </div>
              <el-switch v-model="enabledSuggestion" @change="setSuggestionEnable"/>
            </div>
            <div class="font-12 opening">
              设置下一步问题建议可以让用户更好的对话。
            </div>
          </el-card>
          <el-card class="var-card" shadow="never">
            <div class="mb-1 flex justify-between">
              <div class="flex items-center">
                <img src="@/assets/img/svg/quote.svg" alt="" style="width: 30px;height: 30px;"/>
                <span class="ml-1">引用和归属</span>
              </div>
              <el-switch v-model="enabledOrigin" @change="setOriginEnable"/>
            </div>
            <div class="font-12 opening">
              显示源文档和生成内容的归属部分。
            </div>
          </el-card>
        </div>
      </template>
    </el-drawer>
    <el-drawer v-model="showToolsInfo" di rection="rtl">
      <template #header>
        <div>
          <h4>{{ nowTool?.label?.zh_Hans }}</h4>
          <div style="font-size: 12px">{{ nowTool?.description?.zh_Hans }}</div>
        </div>
      </template>
      <template #default>
        <el-tabs v-model="activeName">
          <el-tab-pane label="参数" name="canshu">
            <div class="font-12 mb-2" v-for="item in nowTool?.parameters?.filter(item2=>item2.form === 'llm')">
              <div>
                <span class="font-14">{{ item.label.zh_Hans }}</span>
                <span class="gray ml-1 mr-1" v-if="item.type === 'string'">字符串</span>
                <span class="gray ml-1 mr-1" v-else-if="item.type === 'file'">文件</span>
                <span class="gray ml-1 mr-1" v-else-if="item.type === 'number'">数字</span>
                <span class="gray ml-1 mr-1" v-else>{{ item.type }}</span>
                <span class="orange" v-if="item.required">必填</span>
                <span class="orange" v-else>选填</span>
              </div>
              <div class="mt-1 mb-1 gray">{{ item.human_description.zh_Hans }}</div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="设置" name="shezhi" v-if="nowTool?.parameters?.find(item2=>item2.form === 'form')">
            <div class="font-12">
              <el-form-item label-position="top" :label="item3.label.zh_Hans"
                            v-for="item3 in nowTool?.parameters?.filter(item2=>item2.form === 'form')">
                <el-input v-if="item3.type === 'string'" :value="item3.default"></el-input>
                <el-select v-else-if="item3.type === 'select'" :value="item3.default">
                  <el-option v-for="item4 in item3.options" :label="item4.label.zh_Hans"
                             :value="item4.value"></el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {useRoute, useRouter} from 'vue-router';
import {computed, nextTick, reactive, ref} from 'vue';
import {getAgentApi, updateAgentApi, setModelConfig, getMessagesHistory} from '@/api/agent'
import {getMyKnowledge} from '@/api/knowledge';
import {ElMessage, ElMessageBox, FormRules, UploadProps} from "element-plus";
import {streamTempChat, streamTempChat2, getSuggesstions} from '@/api/chat';
import {generatePrompt, setSiteImage, getTools, stopChat, getToolInfo, getDailyConversations,getDailyEndUser,getDailyAvgSession,getDailyAvgResponseTime,getDailyToken,} from '@/api/agent';
import {getDefaultModel} from '@/api/system';
import {marked} from "marked";
import ThinkBlock from "@/components/chat/ThinkBlock.vue";
import {CircleCloseFilled, Delete, Document, Memo, QuestionFilled} from "@element-plus/icons-vue";
import {useUserStore} from "@/store/user";


import dayjs from 'dayjs'

interface ParamBounds {
  temperature: number;
  top_p: number;
  max_tokens: number;
  stop?: string[];
}

const props = withDefaults(defineProps<{
  /** 每一项的最大最小值由外部传入 */
  maxValues?: ParamBounds;
  minValues?: ParamBounds;
}>(), {
  maxValues: {
    temperature: 1.0,
    top_p: 1.0,
    max_tokens: 16384,
  },
  minValues: {
    temperature: 0.1,
    top_p: 0.1,
    max_tokens: 1,
  }
});
const router = useRouter();
const route = useRoute();
const robotInfo = ref({});
const knowledges = ref([]);
const createPrompt = ref(false);
const createPromptLoading = ref(false);
const showAppSetting = ref(false);
const showEditOpening = ref(false);
const showAddToolDialog = ref(false);
const showEditOpeningDialog = ref(false);
const promptInfo = ref('');
const userForm = ref({});
const quickInfo = ref('');
const activeName = ref('canshu');
const suggestionsBox = ref([]);

interface RuleForm {
  name: string
  icon_type: string
  icon: string
  iconImage: string
  icon_background: string
  mode: string
  description: string
}

const rules = reactive<FormRules<RuleForm>>({
  name: [
    {required: true, message: '为应用指定一个唯一的名称', trigger: 'blur'},
    {min: 3, max: 40, message: '长度在3-40个字符', trigger: 'blur'},
  ],
  description: [
    {required: false, message: '介绍应用功能并向应用用户展示', trigger: 'blur'},
  ],
  icon: [
    {required: true, message: '请选择一个表情', trigger: 'blur'},
  ],
});
const rules2 = reactive<FormRules<RuleForm>>({
  name: [
    {required: true, message: '为应用指定一个唯一的名称', trigger: 'blur'},
    {min: 3, max: 40, message: '长度在3-40个字符', trigger: 'blur'},
  ],
  description: [
    {required: false, message: '介绍应用功能并向应用用户展示', trigger: 'blur'},
  ],
  iconImage: [
    {required: true, message: '请选择一个封面', trigger: 'blur'},
  ],
});
const ruleForm = reactive<RuleForm>({
  name: '',
  icon_type: 'emoji',
  icon: '🤖',
  iconImage: '',
  icon_background: '#FFEAD5',
  mode: 'chat',
  description: '',
});
const settingForm = ref<Record<keyof ParamBounds, number>>({
  temperature: 0,
  top_p: 0,
  max_tokens: 0,
  stop: []
});
const emojiList = [
  // 😀 常规表情
  '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '😍', '🥰', '😘', '😗', '😋', '😜', '🤪',

  // 💬 表达类
  '🤔', '🤨', '😐', '😑', '😶', '🙄', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢',

  // 🎉 活动类
  '🎉', '🥳', '🎂', '🎁', '🎈', '🎊', '🪅', '💌', '💯', '✔️', '✌️', '🤞', '🤟', '👍', '👎', '👏', '🙌', '🙏',

  // 🔥 热门类
  '💥', '🔥', '💦', '🌟', '💫', '✨', '💡', '🎯', '🚀', '🌈',

  // 🧠 AI / 技术
  '🤖', '🧠', '📦', '💻', '📱', '📊', '📈', '🖥️', '🧑‍💻', '🔐', '⚙️',

  // 🐶 动物
  '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🐙',

  // 🌍 地球/世界
  '🌍', '🌎', '🌏', '🌐', '🗺️', '🧭', '📍', '🚩'
];
const messageRef = ref<HTMLInputElement | null>(null);
const userStore = useUserStore()
const model_config = ref({});
const site = ref({});
const loadData = async () => {
  try {
    const [tempRobotInfo, tempknowledges] = await Promise.all([
      getAgentApi(route.query.id),
      getMyKnowledge({
        page: 1,
        limit: 100,
        // include_all: false,
      })
    ]);
    robotInfo.value = tempRobotInfo;
    model_config.value = tempRobotInfo.model_config;
    settingForm.value = tempRobotInfo.model_config.model.completion_params;
    if (tempRobotInfo.icon_type == 'emoji') {
      ruleForm.name = tempRobotInfo.name;
      ruleForm.icon_type = "emoji";
      ruleForm.icon = tempRobotInfo.icon;
      ruleForm.icon_background = "#FFEAD5";
      ruleForm.mode = "chat";
      ruleForm.description = tempRobotInfo.description;
    } else {
      ruleForm.name = tempRobotInfo.name;
      ruleForm.icon_type = "image";
      ruleForm.icon = tempRobotInfo.iconImage;
      ruleForm.mode = "chat";
      ruleForm.description = tempRobotInfo.description;
    }
    sliders.forEach(item => {
      if (item.prop === 'temperature') {
        item.active = settingForm.value.temperature !== undefined;
      } else if (item.prop === 'top_p') {
        item.active = settingForm.value.top_p !== undefined;
      } else if (item.prop === 'max_tokens') {
        item.active = settingForm.value.max_tokens !== undefined;
      }
    });
    site.value = tempRobotInfo.site;
    nowPromtInfo.value = tempRobotInfo.model_config.pre_prompt;
    knowledges.value = tempknowledges.data;
    showOpenStatement.value = !!model_config.value?.opening_statement;
    enabledSuggestion.value = model_config.value?.suggested_questions_after_answer?.enabled;
    enabledOrigin.value = model_config.value?.retriever_resource?.enabled;
    if (model_config.value?.dataset_configs?.datasets?.datasets.length > 0) {
      selectKnowledgesIds.value = model_config.value.dataset_configs.datasets.datasets.map(item => item.dataset.id)
    }
  } catch (err) {
    console.error('加载数据出错：', err);
  }
};
const messagesInfo = ref<any[]>([]);
const message = ref('');
const isLoading = ref(false);
const showOpenStatement = ref(false);
const sliders = reactive([
  {
    prop: 'temperature' as const,
    tooltip: '温度控制随机性。较低的温度会导致较少的随机完成。随着温度接近零，模型将变得确定性和重复性。较高的温度会导致更多的随机完成。',
    label: '温度',
    min: props.minValues.temperature,
    max: props.maxValues.temperature,
    step: 0.1,
    precision: 1,
    defalutValue: 0.6,
    active: false
  },
  {
    prop: 'top_p' as const,
    tooltip: '通过核心采样控制多样性:0.5表示考虑了一半的所有可能性加权选项。',
    label: 'Top P',
    min: props.minValues.top_p,
    max: props.maxValues.top_p,
    step: 0.1,
    precision: 1,
    defalutValue: 0.95,
    active: false
  },
  {
    prop: 'max_tokens' as const,
    tooltip: '指定生成结果长度的上限。如果生成结果截断，可以调大该参数。',
    label: '最大标记',
    min: props.minValues.max_tokens,
    max: props.maxValues.max_tokens,
    step: 100,
    precision: 0,
    defalutValue: 8192,
    active: false
  },
]);
loadData();
// 滚动到最底部
const scrollToBottom = () => {
  nextTick(() => {
    const maininfo = document.querySelector('.maininfo');
    if (maininfo) {
      maininfo.scrollTo({top: maininfo.scrollHeight, behavior: 'smooth'});
    }
  });
};
// 发送消息
const conversation_id = ref('');
const parent_message_id = ref(null);
const quickSendMessage = async (queryInfo) => {
  console.log('queryInfo', queryInfo)
  quickInfo.value = queryInfo;
  await sendMessage();

}
const sendMessage = async () => {
  if ((!message.value || isLoading.value) && !quickInfo.value) return;
  let inputs = {};
  let flag = false;
  let missingRequired = false
  let tooLongField: { label: string; max: number; actual: number } | null = null

  model_config.value?.user_input_form?.forEach(raw => {
    const type = Object.keys(raw)[0]
    const cfg = (raw as any)[type] as {
      variable: string
      label: string
      required: boolean
      max_length?: number
    }
    const val: string = userForm.value[cfg.label] || ''
    if (val) {
      inputs[cfg.variable] = val
    }
    // 必填检查
    if (cfg.required && !val) {
      missingRequired = true
    }
    // 长度检查
    if (cfg.max_length !== undefined && val.length > cfg.max_length) {
      tooLongField = {
        label: cfg.label,
        max: cfg.max_length,
        actual: val.length
      }
    }
  })

  if (missingRequired) {
    ElMessage.error('请先填写所有必填字段')
    return
  }
  if (tooLongField) {
    ElMessage.error(
        `“${tooLongField.label}”长度不能超过 ${tooLongField.max} 字符（当前 ${tooLongField.actual} 字符,可以改为段落并设置新的长度）`
    )
    return
  }
  console.log('inputs', inputs)
  model_config.value.inputs = inputs;
  if (flag) {
    ElMessage.error('请先填入变量值');
    return;
  }

  isLoading.value = true;
  const userMessage = quickInfo.value ? quickInfo.value : message.value;
  message.value = '';
  quickInfo.value = '';

  // 初始化一条记录：放进 messagesInfo
  const newMessage = {
    query: userMessage,
    answer: '',
    metadata: {
      retriever_resources: []
    },
    hasThinkEnd: false,
    isThinking: false
  };
  messagesInfo.value.push(newMessage);
  // 构造激活参数
  const activeParams = {};
  sliders.forEach((item) => {
    if (item.active) {
      activeParams[item.prop] = settingForm.value[item.prop];
    }
  });

  // 构造 model_config
  model_config.value.supportAnnotation = true;
  model_config.value.supportCitationHitInfo = true;
  model_config.value.appId = route.query.id;
  model_config.value.model.completion_params = activeParams;
  model_config.value.pre_prompt = nowPromtInfo.value;

  if (!model_config.value.dataset_configs?.datasets?.datasets) {
    model_config.value.dataset_configs.datasets = {datasets: []};
  }
  model_config.value.dataset_configs.datasets.datasets = knowledges.value
      .filter(item => selectKnowledgesIds.value.includes(item.id))
      .map(item => ({
        dataset: {
          id: item.id,
          enabled: item.enabled || true,
        }
      }));

  let modelConfigTemp = JSON.parse(JSON.stringify(model_config.value));
  if (!showOpenStatement.value) {
    modelConfigTemp['opening_statement'] = '';
  }
  if(robotInfo.value.mode === 'agent-chat'){
    modelConfigTemp.agent_mode.max_iteration = 5;
    modelConfigTemp.agent_mode.strategy = 'react';
    modelConfigTemp.agent_mode.enabled = true;
    modelConfigTemp.dataset_configs.reranking_enable = false;
    modelConfigTemp.dataset_configs.retrieval_model = 'multiple';
    modelConfigTemp.dataset_configs.top_k = 4;
    modelConfigTemp.file_upload = {
      "image": {
        "detail": "high",
        "enabled": false,
        "number_limits": 3,
        "transfer_methods": [
          "remote_url",
          "local_file"
        ]
      },
      "enabled": false,
      "allowed_file_types": [],
      "allowed_file_extensions": [
        ".JPG",
        ".JPEG",
        ".PNG",
        ".GIF",
        ".WEBP",
        ".SVG",
        ".MP4",
        ".MOV",
        ".MPEG",
        ".WEBM"
      ],
      "allowed_file_upload_methods": [
        "remote_url",
        "local_file"
      ],
      "number_limits": 3,
      "fileUploadConfig": {
        "file_size_limit": 15,
        "batch_count_limit": 5,
        "image_file_size_limit": 10,
        "video_file_size_limit": 100,
        "audio_file_size_limit": 50,
        "workflow_file_upload_limit": 10
      }
    };
  }
  try {
    let thinkBuffer = '';
    await streamTempChat({
      conversation_id: conversation_id.value,
      files: [],
      inputs: inputs,
      model_config: modelConfigTemp,
      parent_message_id: parent_message_id.value,
      query: userMessage,
      response_mode: 'streaming',
    }, (chunk) => {
      try {
        const cleaned = chunk.startsWith('data:') ? chunk.replace(/^data:\s*/, '') : chunk;
        const parsed = JSON.parse(cleaned);

        // 拼接 AI 回答
        if (parsed.answer) {

          if(robotInfo.value.mode === 'agent-chat'){
            newMessage.answer += parsed.answer;
            thinkBuffer += parsed.answer;
            // ① 捕获思考结束信号
            if (thinkBuffer.indexOf(`</think>`) > 0) {
              console.log('结束啊')
              thinkBuffer = '';
              newMessage.hasThinkEnd = true;       // 进入正式回答阶段
              newMessage.isThinking = true;        // 仍然展示思考块，但不再追加文字
              return;                              // 不把 "</think>" 写进任何内容里
            }

            // ② 根据是否已结束思考，分别追加
            if (!newMessage.hasThinkEnd) {
              newMessage.isThinking = true;
              newMessage.thinkAnswer += parsed.answer;  // 存到思考缓存
            }
          }else{
            newMessage.answer += parsed.answer;
            // ① 捕获思考结束信号
            if (parsed.answer === '</think>') {
              newMessage.hasThinkEnd = true;       // 进入正式回答阶段
              newMessage.isThinking = true;        // 仍然展示思考块，但不再追加文字
              return;                              // 不把 "</think>" 写进任何内容里
            }

            // ② 根据是否已结束思考，分别追加
            if (!newMessage.hasThinkEnd) {
              newMessage.isThinking = true;
              newMessage.thinkAnswer += parsed.answer;  // 存到思考缓存
            }
          }



          messagesInfo.value = [...messagesInfo.value];   // 强制刷新
          setTimeout(scrollToBottom, 50);
        }

        // 结束时补充引用信息
        if (parsed.event === 'message_end') {
          conversation_id.value = parsed.conversation_id;
          parent_message_id.value = parsed.message_id;

          if (parsed.metadata?.retriever_resources) {
            newMessage.metadata.retriever_resources = parsed.metadata.retriever_resources;
          }

          messagesInfo.value = [...messagesInfo.value]; // 强制刷新
          if(robotInfo.value.mode === 'agent-chat'){
            getMessagesHistory(route.query.id, conversation_id.value).then(res => {
              if (res.data && Array.isArray(res.data)) {
                console.log('res.data',res.data)
                messagesInfo.value = res.data.map(msg => ({
                  query: msg.query,
                  answer: msg.agent_thoughts[0].thought + msg.answer,
                  metadata: msg.metadata || {retriever_resources: []},
                  hasThinkEnd: true,
                  isThinking: true,
                }));
                console.log('messagesInfo.value',messagesInfo.value)
                setTimeout(scrollToBottom, 50);
                console.log('res.dataaaaa', res.data)
                let messageId = res.data[res.data.length - 1]?.id;
                if (enabledSuggestion.value) {
                  getSuggesstions(route.query.id, messageId).then(res => {
                    suggestionsBox.value = res.data;
                  })
                }
              }
            });
          }else{
            getMessagesHistory(route.query.id, conversation_id.value).then(res => {
              if (res.data && Array.isArray(res.data)) {
                messagesInfo.value = res.data.map(msg => ({
                  query: msg.query,
                  answer: msg.answer,
                  metadata: msg.metadata || {retriever_resources: []},
                  hasThinkEnd: msg.answer.includes('</think>'),
                  isThinking: msg.answer.startsWith('<think>')
                }));
                setTimeout(scrollToBottom, 50);
                console.log('res.dataaaaa', res.data)
                let messageId = res.data[res.data.length - 1]?.id;
                if (enabledSuggestion.value) {
                  getSuggesstions(route.query.id, messageId).then(res => {
                    suggestionsBox.value = res.data;
                  })
                }
              }
            });
          }
        }
      } catch (err) {
        console.warn('解析流片段出错:', err);
      }
    });
  } catch (err) {
    ElMessage.error('AI 回复失败，请稍后重试');
    console.error('stream error:', err);
  } finally {
    isLoading.value = false;
    await nextTick();
    messageRef.value?.focus();
  }
};
const sendMessage2 = async () => {
  if (isLoading.value) return;
  let inputs = {};
  let flag = false;
  let missingRequired = false
  let tooLongField: { label: string; max: number; actual: number } | null = null

  model_config.value?.user_input_form?.forEach(raw => {
    const type = Object.keys(raw)[0]
    const cfg = (raw as any)[type] as {
      variable: string
      label: string
      required: boolean
      max_length?: number
    }
    const val: string = userForm.value[cfg.label] || ''
    if (val) {
      inputs[cfg.variable] = val
    }
    // 必填检查
    if (cfg.required && !val) {
      missingRequired = true
    }
    // 长度检查
    if (cfg.max_length !== undefined && val.length > cfg.max_length) {
      tooLongField = {
        label: cfg.label,
        max: cfg.max_length,
        actual: val.length
      }
    }
  })

  if (missingRequired) {
    ElMessage.error('请先填写所有必填字段')
    return
  }
  if (tooLongField) {
    ElMessage.error(
        `“${tooLongField.label}”长度不能超过 ${tooLongField.max} 字符（当前 ${tooLongField.actual} 字符,可以改为段落并设置新的长度）`
    )
    return
  }
  model_config.value.inputs = inputs;
  if (flag) {
    ElMessage.error('请先填入变量值');
    return;
  }
  messagesInfo.value = [];
  isLoading.value = true;
  const userMessage = quickInfo.value ? quickInfo.value : message.value;
  message.value = '';
  quickInfo.value = '';

  // 初始化一条记录：放进 messagesInfo
  const newMessage = {
    query: '结果',
    answer: '',
    metadata: {
      retriever_resources: []
    },
    thinkAnswer: '',
    hasThinkEnd: false,
    isThinking: false
  };

  // 构造激活参数
  const activeParams = {};
  sliders.forEach((item) => {
    if (item.active) {
      activeParams[item.prop] = settingForm.value[item.prop];
    }
  });

  // 构造 model_config
  model_config.value.supportAnnotation = true;
  model_config.value.supportCitationHitInfo = true;
  model_config.value.appId = route.query.id;
  model_config.value.model.completion_params = activeParams;
  model_config.value.pre_prompt = nowPromtInfo.value;

  if (!model_config.value.dataset_configs?.datasets?.datasets) {
    model_config.value.dataset_configs.datasets = {datasets: []};
  }
  model_config.value.dataset_configs.datasets.datasets = knowledges.value
      .filter(item => selectKnowledgesIds.value.includes(item.id))
      .map(item => ({
        dataset: {
          id: item.id,
          enabled: item.enabled || true,
        }
      }));
  if (model_config.value?.dataset_configs?.datasets?.datasets.length > 0 && !model_config.value?.dataset_query_variable) {
    ElMessage.error('无法成功查询知识库，请在上下文部分选择一个上下文查询变量');
    return;
  }
  messagesInfo.value.push(newMessage);
  let modelConfigTemp = JSON.parse(JSON.stringify(model_config.value));
  if (!showOpenStatement.value) {
    modelConfigTemp['opening_statement'] = '';
  }
  try {
    await streamTempChat2({
      inputs: inputs,
      model_config: modelConfigTemp,
      response_mode: 'streaming',
    }, (chunk) => {
      try {
        const cleaned = chunk.startsWith('data:') ? chunk.replace(/^data:\s*/, '') : chunk;
        const parsed = JSON.parse(cleaned);

        // 拼接 AI 回答
        if (parsed.answer) {
          newMessage.answer += parsed.answer;
          // ① 捕获思考结束信号
          if (parsed.answer === `</think>`) {
            newMessage.hasThinkEnd = true;       // 进入正式回答阶段
            newMessage.isThinking = true;        // 仍然展示思考块，但不再追加文字
            return;                              // 不把 "</think>" 写进任何内容里
          }

          // ② 根据是否已结束思考，分别追加
          if (!newMessage.hasThinkEnd) {
            newMessage.isThinking = true;
            newMessage.thinkAnswer += parsed.answer;  // 存到思考缓存
          }
          messagesInfo.value = [...messagesInfo.value];   // 强制刷新
          setTimeout(scrollToBottom, 50);
        }

        // 结束时补充引用信息
        if (parsed.event === 'message_end') {
          conversation_id.value = parsed.conversation_id;
          parent_message_id.value = parsed.message_id;

          if (parsed.metadata?.retriever_resources) {
            newMessage.metadata.retriever_resources = parsed.metadata.retriever_resources;
          }

          messagesInfo.value = [...messagesInfo.value]; // 强制刷新
          // getMessagesHistory(route.query.id, conversation_id.value).then(res => {
          //   if (res.data && Array.isArray(res.data)) {
          //     messagesInfo.value = res.data.map(msg => ({
          //       query: msg.query,
          //       answer: msg.answer,
          //       metadata: msg.metadata || {retriever_resources: []},
          //       hasThinkEnd: msg.answer.includes('</think>'),
          //       isThinking: msg.answer.startsWith('<think>')
          //     }));
          //     setTimeout(scrollToBottom, 50);
          //     console.log('res.dataaaaa',res.data)
          //     let messageId = res.data[res.data.length - 1]?.id;
          //     if(enabledSuggestion.value){
          //       getSuggesstions(route.query.id,messageId).then(res=>{
          //         suggestionsBox.value = res.data;
          //       })
          //     }
          //   }
          // });
        }
      } catch (err) {
        console.warn('解析流片段出错:', err);
      }
    });
  } catch (err) {
    ElMessage.error('AI 回复失败，请稍后重试');
    console.error('stream error:', err);
  } finally {
    isLoading.value = false;
    await nextTick();
    messageRef.value?.focus();
  }
};
// 清除聊天记录
const clearMessage = async () => {
  messagesInfo.value = [];
};
// 提取正文部分并处理换行符
marked.setOptions({
  gfm: true,
  breaks: true,        // 单行换行也渲染成 <br>
  headerIds: false,    // 禁用自动给标题加 id
  mangle: false,       // 禁用自动转义邮箱地址等
});
// 提取正文部分并处理换行符
const extractBodyContent = (content: string) => {
  // 去除思考部分（无论是否闭合）
  const removed = content.replace(/^<think>[\s\S]*?(<\/think>|$)/, '')
  return marked.parse(removed)
}
const nowPromtInfo = ref('');
const goUrl = () => {
  router.push('/myAgents');
}
const dialogVisible = ref(false);
const enabledSuggestion = ref(true);
const enabledOrigin = ref(true);
const showEdit = () => {
  dialogVisible.value = true;
}
const resPrompt = ref({});
const toAICreateAgent = async () => {
  let res = await getDefaultModel();
  console.log('userStore.defaultModel', userStore.defaultModel)
  createPromptLoading.value = true;
  generatePrompt({
    instruction: promptInfo.value,
    no_variable: false,
    model_config: {
      completion_params: {},
      mode: 'chat',
      name: res.data.model,
      provider: res.data.provider.provider,
    }
  }).then(res => {
    // ruleForm.name = res2.data.name;
    // ruleForm.description = res.prompt;
    resPrompt.value = res;
    createPromptLoading.value = false;
  });
}

const uploadUrl = `${import.meta.env.VITE_SERVE_HOST}/console/api/files/upload`;
const getUploadHeaders = () => {
  const token = localStorage.getItem('console_token');
  if (!token) {
    ElMessage.error('未找到有效的 token，请重新登录');
    return null;
  }
  return {
    'Authorization': `Bearer ${token}`,
    // 'X-Requested-With': 'XMLHttpRequest',
    // 'Accept': 'application/json'
  };
};
const uploadHeaders = ref(getUploadHeaders());
const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  if (response) {
    ruleForm.icon = response.id;
    ruleForm.icon_background = '#FFEAD5';
    ruleForm.icon_type = 'image';
    ruleForm.mode = 'chat';
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error('图片上传失败');
  }
};
// 上传失败处理方法
const handleUploadError = (error, uploadFile) => {
  console.error('图片上传失败:', error);
  ElMessage.error('图片上传失败，请检查网络或服务器状态');
};
const beforeUpload = (file) => {
  console.log('上传文件类型:', file.type);
  console.log('上传文件大小:', file.size);
  const allowedTypes = ['image/jpeg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB');
    return false;
  }
  ruleForm.iconImage = URL.createObjectURL(file);
  return true;
};
const toUpdateAgents = async () => {
  let form = {};
  if (ruleForm.icon_type === 'emoji') {
    form.description = ruleForm.description;
    form.icon = ruleForm.icon;
    form.icon_background = ruleForm.icon_background;
    form.icon_type = ruleForm.icon_type;
    form.name = ruleForm.name;
    form.use_icon_as_answer_icon = false;
  } else {
    form.description = ruleForm.description;
    form.icon = ruleForm.icon;
    form.icon_type = ruleForm.icon_type;
    form.name = ruleForm.name;
    form.use_icon_as_answer_icon = false;
  }
  await updateAgentApi(route.query.id, form);
  await setSiteImage(route.query.id, form);
  loadData();
  dialogVisible.value = false;
}
const toUpdateAgents2 = async () => {
  console.log('model_config.value', model_config.value)
  model_config.value.supportAnnotation = true;
  model_config.value.supportCitationHitInfo = true;
  model_config.value.appId = route.query.id;
  model_config.value.pre_prompt = nowPromtInfo.value;
  model_config.value.user_input_form = model_config.value.user_input_form.filter(raw =>
      (Object.values(raw)[0] as any).variable?.toString().trim() !== ''
  );
  const activeParams = {};
  sliders.forEach((item) => {
    if (item.active) {
      activeParams[item.prop] = settingForm.value[item.prop];
    }
  });
  model_config.value.model.completion_params = activeParams;
  if (!model_config.value.dataset_configs.datasets) {
    model_config.value.dataset_configs.datasets = {
      datasets: []
    };
  }
  model_config.value.dataset_configs.datasets.datasets = knowledges.value
      .filter(item => selectKnowledgesIds.value.includes(item.id))
      .map(item => ({
        dataset: {
          id: item.id,
          enabled: item.enabled || true,
        }
      }));
  await setModelConfig(robotInfo.value.id, model_config.value);
  await loadData();
}
const changeActive = (item) => {
  if (item.active) {
    settingForm.value[item.prop] = item.defalutValue;
  }
}
const selectKnowledgesIds = ref([]);

const toReplacePrompt = () => {
  ElMessageBox.confirm(
      '应用此提示将覆盖现有配置。',
      '覆盖现有配置？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(() => {
        model_config.value.suggested_questions = resPrompt.value.variables;
        nowPromtInfo.value = resPrompt.value.prompt;
        model_config.value.opening_statement = resPrompt.value.opening_statement;
        createPrompt.value = false;
      })
      .catch(() => {
      });
}
const dialogSelectKnowledges = ref(false);

const tempSelectKnowledges = ref([]);
const addTempKnowledges = (knowledge) => {
  if (tempSelectKnowledges.value.includes(knowledge.id)) {
    tempSelectKnowledges.value = tempSelectKnowledges.value.filter(item => item !== knowledge.id);
  } else {
    tempSelectKnowledges.value.unshift(knowledge.id);
  }
}
const showAddKnowledge = () => {
  dialogSelectKnowledges.value = true;
  tempSelectKnowledges.value = JSON.parse(JSON.stringify(selectKnowledgesIds.value));
  console.log('tempSelectKnowledges.value', tempSelectKnowledges.value)
}

const toAddKnowledge = () => {
  selectKnowledgesIds.value = JSON.parse(JSON.stringify(tempSelectKnowledges.value));
  dialogSelectKnowledges.value = false;
}

const handleAdd = (item) => {
  if (!model_config.value?.user_input_form) {
    model_config.value.user_input_form = [];
  }
  console.log('itemeee', item)
  let tempItem = {};
  if (item === 'number') {
    tempItem = {
      default: '',
      label: '',
      required: true,
      variable: '',
    }
  } else if (item === 'select') {
    tempItem = {
      default: '',
      label: '',
      required: true,
      variable: '',
      options: []
    }
  } else {
    tempItem = {
      default: '',
      label: '',
      max_length: 48,
      required: true,
      variable: '',
    }
  }
  model_config.value.user_input_form.push({
    [item]: tempItem
  });
  console.log('model_config.value.user_input_form', model_config.value.user_input_form)
}

const handleDelete = (idx) => {
  model_config.value.user_input_form.splice(idx, 1)
}

const delKnowledge = (konwledge) => {
  console.log('ppp', konwledge)
  selectKnowledgesIds.value = selectKnowledgesIds.value.filter(item => item !== konwledge);
}

const dialogAddArr = ref(false);
const openEditor = (idx) => {
  console.log('idx', idx, model_config.value.user_input_form[idx])
  let raw = model_config.value.user_input_form[idx];
  let key = Object.keys(raw)[0];
  const val = raw[key];
  let tempItem = {};
  tempItem = {
    idx: idx,
    type: key,
    default: val.default,
    variable: val.variable,
    label: val.label,
    required: val.required,
  }
  console.log('这里1')
  if (key === 'select') {
    tempItem['options'] = val.options;
  } else if (key === 'number') {
    tempItem['max_length'] = val.max_length;
  }
  console.log('这里2')
  Object.assign(addArrForm, tempItem)
  console.log('这里3')
  console.log('addArrForm', addArrForm)
  dialogAddArr.value = true;
}

const fieldTypes = [
  {value: 'text-input', label: '文本', icon: 'T'},
  {value: 'paragraph', label: '段落', icon: '≡'},
  {value: 'select', label: '下拉选项', icon: '▤'},
  {value: 'number', label: '数字', icon: '#'},
]
const addArrRules = {
  variable: [
    {required: true, message: '请输入变量名称', trigger: 'blur'},
    {pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '仅支持字母、数字、下划线，且不能以数字开头', trigger: 'blur'},
  ],
  label: [
    {required: true, message: '请输入变量名称', trigger: 'blur'},
    {pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '仅支持字母、数字、下划线，且不能以数字开头', trigger: 'blur'},
  ],
  maxLength: [{type: 'number', message: '请输入数字', trigger: 'blur'}],
  options: [
    {
      validator: (_, value: string[]) => {
        if (addArrForm.type !== 'select') return true
        return value.length > 0 && value.every((v) => !!v)
      },
      message: '请至少输入一个有效选项',
      trigger: 'change',
    },
  ],
}
const arrRuleForm = ref();
const submitting = ref(false)
const showUserForm = ref(true)
const addArrForm = reactive({});
const showMaxLength = computed(() => ['text', 'paragraph'].includes(addArrForm.type))

const addOption = () => {
  addArrForm.options.push('')
}
const removeOption = (idx: number) => {
  addArrForm.options.splice(idx, 1)
}

const onSubmit = () => {
  arrRuleForm.value?.validate(async (valid: boolean) => {
    if (!valid) return
    submitting.value = true;
    let tempItem = {};
    tempItem = {
      default: addArrForm.default,
      variable: addArrForm.variable,
      label: addArrForm.label,
      required: addArrForm.required,
    };
    if (addArrForm.type === 'select') {
      tempItem['options'] = addArrForm.options;
    } else if (addArrForm.type === 'number') {
      tempItem['max_length'] = addArrForm.max_length;
    }
    model_config.value.user_input_form[addArrForm.idx] = {[addArrForm.type]: tempItem};
    dialogAddArr.value = false;
    submitting.value = false;
  })
}

const checkVar = () => {
  console.log('addArrForm.label', addArrForm.label)
  if (!addArrForm.label) {
    addArrForm.label = addArrForm.variable;
  }
}

const tempOpeningVal = ref('');
const toShowEditOpeningDialog = () => {
  tempOpeningVal.value = JSON.parse(JSON.stringify(model_config.value.opening_statement))
  tempAnswers.value = JSON.parse(JSON.stringify(model_config.value.suggested_questions || []))  // 没有就空数组
  showEditOpeningDialog.value = true;
}
const updateOpeningVal = () => {
  model_config.value.opening_statement = JSON.parse(JSON.stringify(tempOpeningVal.value));
  model_config.value.suggested_questions = JSON.parse(JSON.stringify(tempAnswers.value));
  showEditOpeningDialog.value = false;
}

// ② 回答数组（每个元素对应一行 <el-input>）
const tempAnswers = ref<string[]>([])   // 例如 ['好的', '明白了']
const setSuggestionEnable = () => {
  model_config.value.suggested_questions_after_answer.enabled = enabledSuggestion.value;
}
const setOriginEnable = () => {
  model_config.value.retriever_resource.enabled = enabledOrigin.value;
}
// ④ 添加 / 删除 行
const addAnswer = () => tempAnswers.value.push('')
const delAnswer = (idx: number) => tempAnswers.value.splice(idx, 1);
const handlePromptBlur = () => {
  // ① 提取所有 {{variable}}，允许空格
  const regex = /{{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*}}/g
  const found = new Set<string>()
  let m: RegExpExecArray | null
  while ((m = regex.exec(nowPromtInfo.value)) !== null) {
    found.add(m[1])
  }

  if (found.size === 0) return            // 没有占位符，直接退出

  // ② 过滤掉已存在的变量
  const existing = new Set(
      (model_config.value?.user_input_form || []).map(
          raw => (Object.values(raw)[0] as any).variable
      )
  )
  const toCreate = [...found].filter(v => !existing.has(v))
  if (toCreate.length === 0) return

  // ③ 弹窗询问
  ElMessageBox.confirm(
      `检测到新变量：${toCreate.join(', ')}。\n是否为它们自动生成「文本」类型的表单字段？`,
      '自动生成变量？',
      {confirmButtonText: '是', cancelButtonText: '否', type: 'warning'}
  )
      .then(() => {
        if (!model_config.value.user_input_form) model_config.value.user_input_form = []

        // ④ 批量追加变量；默认生成 “文本输入” 类型，可按需改成其他
        toCreate.forEach(v => {
          model_config.value.user_input_form.push({
            'text-input': {
              default: '',
              variable: v,
              label: v,
              required: true,
              max_length: 48
            }
          })
        })
        ElMessage.success('已为新占位符生成对应变量')
      })
      .catch(() => {
        /* 用户点击“否”-> 不操作 */
      })
}

const allTools = ref([]);
const showAddTool = async () => {
  allTools.value = await getTools();
  showAddToolDialog.value = true;
}
const addMyTools = (provider, tool) => {
  const tools = model_config.value.agent_mode.tools;
  // 先看是不是已经存在
  const idx = tools.findIndex(t => t.tool_name === tool.name)

  if (idx > -1) {
    // 存在就删掉
    tools.splice(idx, 1)
    console.log(`Tool "${tool.name}" 已移除`)
  } else {
    // 不存在就添加
    const rawParams = tool.parameters ?? tool.tool_parameters ?? []
    const paramsObj = rawParams.reduce((acc, {name}) => {
      acc[name] = ''
      return acc
    }, {} as Record<string, string>)

    tools.push({
      enabled: true,
      notAuthor: false,
      isDeleted: false,
      provider_id: provider.id,
      provider_name: provider.id,
      provider_type: 'builtin',
      tool_label: tool.label.zh_Hans,
      tool_name: tool.name,
      tool_parameters: paramsObj,
    })
    console.log(`Tool "${tool.name}" 已添加`)
  }
}
const deleteTool = (item: { provider_id: string; tool_name: string }) => {
  const tools = model_config.value.agent_mode.tools
  // 根据 provider_id + tool_name 定位索引
  const idx = tools.findIndex(
      t =>
          t.provider_id === item.provider_id &&
          t.tool_name === item.tool_name
  )
  if (idx > -1) {
    tools.splice(idx, 1)
    console.log(`已移除工具: ${item.tool_name}`)
  }
}
const variableOptions = computed(() => {
  return (model_config.value?.user_input_form || []).map(raw => {
    const cfg = Object.values(raw)[0] as any
    return {label: cfg.label || cfg.variable, variable: cfg.variable}
  })
})

const stopChatMessage = async () => {
  await stopChat(route.query.id, parent_message_id.value);
  isLoading.value = false;
}
const nowTool = ref({});
const showToolsInfo = ref(false);
const showTool = async (tool) => {
  let provide = await getToolInfo(tool.provider_id);
  nowTool.value = provide.find(item2 => item2.name === tool.tool_name)
  showToolsInfo.value = true;
  console.log('showToolsInfo.value', nowTool.value)
}

const showCalcDialog = ref(false);
const showCalc = async () => {
  showCalcDialog.value = true;
  await fetchMonitorStats();
}


/** 预设区间 */
const monitorPresets = [
  {label: '今天', value: 'today'},
  {label: '近 7 天', value: 'last7'},
  {label: '近 30 天', value: 'last30'},
] as const
type PresetVal = (typeof monitorPresets)[number]['value']

const monitorPreset = ref<PresetVal>('last7')
const monitorRange = ref<[Date, Date]>([
  dayjs().subtract(6, 'day').toDate(),
  dayjs().endOf('day').toDate()
])

const monitorDates = ref<string[]>([])
const monitorValues = ref<number[]>([])
const monitorDates2 = ref<string[]>([])
const monitorValues2 = ref<number[]>([])
const monitorDates3 = ref<string[]>([])
const monitorValues3 = ref<number[]>([])
const monitorDates4 = ref<string[]>([])
const monitorValues4 = ref<number[]>([])

/** 把预设值转成日期范围 */
function applyMonitorPreset(val: PresetVal) {
  switch (val) {
    case 'today':
      monitorRange.value = [dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()];
      break
    case 'last30':
      monitorRange.value = [dayjs().subtract(29, 'day').toDate(), dayjs().endOf('day').toDate()];
      break
    default: // last7
      monitorRange.value = [dayjs().subtract(6, 'day').toDate(), dayjs().endOf('day').toDate()]
  }
  fetchMonitorStats()
}

/** 获取统计接口（自行替换 API 地址与字段） */
const fetchMonitorStats = async () => {
  console.log('fetchMonitorStats')
  // ① 拿到 Date 对象
  const [rawStart, rawEnd] = monitorRange.value

  // ② 转成 dayjs 并截到整天
  const fmt = 'YYYY-MM-DD HH:mm'
  const start = dayjs(rawStart).startOf('day').format(fmt)   // 2025-06-06 00:00
  const end = dayjs(rawEnd).endOf('day').format(fmt)       // 2025-06-13 23:59
  const appId = route.query.id;
  let res = [];
  if(robotInfo.value.mode === 'completion'){
    res = await Promise.all([
      getDailyConversations({appId, start, end}),
      getDailyEndUser({appId, start, end}),
      getDailyAvgResponseTime({appId, start, end}),
      getDailyToken({appId, start, end}),
    ])
  }else{
    res = await Promise.all([
      getDailyConversations({appId, start, end}),
      getDailyEndUser({appId, start, end}),
      getDailyAvgSession({appId, start, end}),
      getDailyToken({appId, start, end}),
    ])
  }
  console.log('eee',res)

  // ④ 更新图表数据
  monitorDates.value  = res[0].data.map((d: any) => d.date);
  monitorValues.value = res[0].data.map((d: any) => d.conversation_count);
  monitorDates2.value  = res[1].data.map((d: any) => d.date);
  monitorValues2.value = res[1].data.map((d: any) => d.terminal_count);
  monitorDates3.value  = res[2].data.map((d: any) => d.date);
  if(robotInfo.value.mode === 'completion'){
    monitorValues3.value = res[2].data.map((d: any) => d.latency);
  }else{
    monitorValues3.value = res[2].data.map((d: any) => d.interactions);
  }

  monitorDates4.value  = res[3].data.map((d: any) => d.date);
  monitorValues4.value = res[3].data.map((d: any) => d.tps);

}

/** 折线图配置 */
const monitorOption = computed(() => ({
  tooltip: {trigger: 'axis'},
  grid: {left: 40, right: 40, top: 30, bottom: 40,containLabel: true},
  xAxis: {type: 'category', data: monitorDates.value, boundaryGap: false},
  yAxis: {type: 'value'},
  series: [{
    name: '全部会话数',
    type: 'line',
    smooth: true,
    areaStyle: {},
    data: monitorValues.value,
  }]
}))
const monitorOption2 = computed(() => ({
  tooltip: {trigger: 'axis'},
  grid: {left: 40, right: 40, top: 30, bottom: 40,containLabel: true},
  xAxis: {type: 'category', data: monitorDates2.value, boundaryGap: false},
  yAxis: {type: 'value'},
  series: [{
    name: '活跃用户数',
    type: 'line',
    smooth: true,
    areaStyle: {},
    data: monitorValues2.value,
  }]
}))
const monitorOption3 = computed(() => {
  if(robotInfo.value.mode === 'completion'){
    return {
      tooltip: {trigger: 'axis'},
      grid: {left: 40, right: 40, top: 30, bottom: 40,containLabel: true},
      xAxis: {type: 'category', data: monitorDates3.value, boundaryGap: false},
      yAxis: {type: 'value'},
      series: [{
        name: '平均响应时间',
        type: 'line',
        smooth: true,
        areaStyle: {},
        data: monitorValues3.value,
      }]
    }
  }else{
    return {
      tooltip: {trigger: 'axis'},
      grid: {left: 40, right: 40, top: 30, bottom: 40,containLabel: true},
      xAxis: {type: 'category', data: monitorDates3.value, boundaryGap: false},
      yAxis: {type: 'value'},
      series: [{
        name: '平均会话互动数',
        type: 'line',
        smooth: true,
        areaStyle: {},
        data: monitorValues3.value,
      }]
    }
  }
})
const monitorOption4 = computed(() => ({
  tooltip: {trigger: 'axis'},
  grid: {left: 40, right: 40, top: 30, bottom: 40,containLabel: true},
  xAxis: {type: 'category', data: monitorDates4.value, boundaryGap: false},
  yAxis: {type: 'value'},
  series: [{
    name: 'Token 输出速度',
    type: 'line',
    smooth: true,
    areaStyle: {},
    data: monitorValues4.value,
  }]
}))

</script>

<style scoped lang="less">
.main {
  position: relative;
  height: 100vh;

  .top {
    height: 46px;
    border-bottom: 1px solid rgba(29, 28, 35, .08);
    padding: 8px 12px;
    background-color: #F7F7FA;
    display: flex;
    align-items: center;
    justify-content: space-between;


    .info {
      display: flex;
      justify-content: center;

      > div {
        display: flex;
        align-items: center;
      }

      img {
        width: 32px;
        height: 32px;
        margin: 0 10px 0 0;
        border-radius: 8px;
      }

      span {
        font-size: 12px;
        color: rgba(32, 41, 69, 0.62);
        margin-left: 5px;
      }


    }

    .rightInfo {
      display: flex;
      align-items: center;

      .publishBtn {
        padding-right: 10px;
        color: rgba(15, 21, 40, 0.82);
        font-size: 12px;
      }
    }
  }

  .frames {
    display: flex;

    > div {
      flex: 1;
      display: flex;
      flex-direction: column;
      border-right: 1px solid rgba(29, 28, 35, .08);

      > :first-child {
        font-size: 16px;
        line-height: 16px;
        padding: 16px;
        border-bottom: 1px solid rgba(82, 100, 154, 0.13);
      }

      .prompt {
        position: relative;
        font-size: 14px;

        .icon3 {
          position: absolute;
          right: 15px;
          top: 15px;
          width: 13px;
          cursor: pointer;
        }
      }

      > .info {
        padding: 12px 8px;
        font-size: 14px;
      }

      .list {
        padding: 12px;
        overflow-y: auto;
        height: calc(100vh - 145px);
        //height:600vh;

        .promptInfo {
          overflow: hidden;
          border-radius: 15px;
          background-color: rgb(242, 244, 247);
          margin-bottom: 10px;

          > div:first-child {
            background-color: rgb(242, 244, 247);
            padding: 10px 15px;
          }

          .text1 {
            font-size: 12px;
            padding: 10px 15px 10px;
          }

          .tags {
            background-color: #fff;
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 5px;
          }
        }

        .treeRef {
          max-height: 65vh;
          overflow-y: auto;
        }


        .tit2 {
          margin-top: 20px;
          margin-bottom: 10px;
        }
      }
    }
  }

  .modelName {
    height: 16px;
    display: flex;
    font-size: 16px;


    img {
      width: 18px;
      height: 18px;
      vertical-align: middle;
      margin-right: 5px;
    }
  }

  .testInfo {
    position: relative;
  }

  .maininfo {
    overflow-y: auto;
    height: 65vh;
    padding-bottom: 180px;
    background-color: rgba(87, 104, 161, 0.08);

    .aiinfo {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      img {
        width: 64px;
        height: 64px;
        border-radius: 8px;
      }

      span {
        margin-top: 10px;
        font-size: 20px;
      }

      div {
        max-width: 60%;
        padding: 12px 16px;
        border-radius: 10px;
      }

      .emoji {
        width: 38px;
        overflow: hidden;
        font-size: 30px;
      }
    }

    .chatLine {
      display: flex;
      flex-direction: row;
      margin: 20px;

      > img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }

      > div {
        display: flex;
        flex-direction: column;
        margin-left: 10px;

        span {
          font-size: 12px;
          color: rgba(32, 41, 69, 0.62);
        }

        > div {
          background-color: #fff;
          //background-color: rgba(87, 104, 161, 0.08);
          padding: 5px 30px;
          border-radius: 10px;
          font-size: 14px;
          line-height: 33px;
        }
      }
    }

    .chatLine2 {
      > div {
        > div {
          background-color: rgba(150, 159, 255, 0.45);
        }
      }
    }

    .thinkBtn {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100px !important;
      padding: 4px 15px !important;
      background-color: rgb(241, 245, 249) !important;
      color: rgb(30, 41, 59) !important;
      margin-bottom: 15px;
      font-size: 14px;

      &:hover {
        background-color: rgb(240, 248, 255) !important;
      }
    }

    .assistant-thought {
      background-color: unset !important;
      border-radius: unset !important;
      border-left: 1px solid #e5e7eb;

      margin-bottom: 15px;

      div {
        font-size: 14px;
        color: rgb(100, 116, 139);
      }
    }
  }

  .messageLine {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;

    /* 关键：flex 布局 */
    display: flex;
    align-items: center;

    /* 整体框的样式 */
    padding: 0 12px;
    height: 48px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* 输入框拉伸，占满剩余空间 */

    input {
      flex: 1;
      height: 100%;
      border: none;
      outline: none;
      padding: 0 12px;
      font-size: 14px;
      background: transparent;
    }

    /* 图标样式统一 */

    .icon {
      width: 24px;
      height: 24px;
      margin-left: 12px;
      cursor: pointer;
    }

    /* 可以给第二个图标一个额外颜色或 hover 效果 */

    .icon:last-child:hover {
      opacity: 0.7;
    }
  }
}

.knowledges {
  padding: 20px;
  border-top: 1px solid rgba(29, 28, 35, .08);
  border-bottom: 1px solid rgba(29, 28, 35, .08);

  .knowledge {
    line-height: 40px;
    display: block;
  }

}

.el-progress {
  width: 60%;
  margin: 0 20px;
}

:deep(.el-progress__text) {
  display: none !important;
}

:deep(.el-checkbox__input) {
  vertical-align: middle;
}

.botIcon {
  margin-left: 10px;
  cursor: pointer;
}

.avatar-uploader {
  width: 64px;
  height: 64px;
}

.watiUpImg {
  width: 64px;
  height: 64px;
  border-radius: 14px;
}

.assistant-body {
  div {
    font-size: 16px;
  }
}

.assistant-body ::v-deep pre {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: Consolas, Menlo, Monaco, monospace;
  font-size: 13px;
  line-height: 1.5;
  margin: 8px 0;

}

:deep(.el-divider__text) {
  background-color: unset !important;
}

.assistant-body ::v-deep code {
  background: rgba(27, 31, 35, 0.05);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: Consolas, Menlo, Monaco, monospace;
}

.emoji {
  width: 27px;
  overflow: hidden;
  margin-right: 5px;
  font-size: 20px;
  border-radius: 5px;
}

.ref-container {
  margin-top: 10px;
  padding-top: 8px;
  font-size: 12px;
  color: #444;

  .ref-title {
    font-weight: bold;
    color: #999;
    font-size: 14px;
    margin-bottom: 5px;
  }

  .ref-list {
    display: flex;
    align-items: center;
    gap: 10px;

    .ref-item {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 6px;
      color: #333;
      font-size: 12px;

      .ref-icon {
        margin-right: 4px;
        color: #909399;
      }

      .ref-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
      }
    }

    .ref-more {
      font-size: 12px;
      color: #666;
    }
  }
}

.showIcon {
  padding: 20px 0 27px;
  border-radius: 10px;
  font-size: 60px;
  background-color: #FFEAD5;
}

.promtBox {
  display: flex;
  justify-content: space-between;

  > div {
    flex: 1;
  }

  > :first-child {
    > div:first-child {
      margin-bottom: 20px;
      font-size: 13px;
      color: rgb(103, 111, 131);
    }

    h4 {
      margin: 10px 0;
    }

    .tags {
      margin-top: 10px;

      span {
        margin-right: 15px;
        cursor: pointer;
        color: rgb(103, 111, 131);
        font-size: 13px;
      }
    }

    .createBtn {
      float: right;
      margin-top: 20px;
    }
  }

  > :last-child {
    display: flex;
    justify-content: center;
  }

  .tipInfo {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .showInfo {
    padding: 0 20px;
    height: 630px;
    overflow-y: auto;
  }

  .promptInfo {
    overflow: hidden;
    border-radius: 15px;
    background-color: rgb(242, 244, 247);
    margin-bottom: 10px;

    > div:first-child {
      background-color: rgb(242, 244, 247);
      padding: 10px 15px;
    }

    .text1 {
      font-size: 12px;
      padding: 0 15px 10px;
    }

    .tags {
      background-color: #fff;
      margin-bottom: 5px;
      padding: 5px;
      border-radius: 5px;
    }
  }

  .promptInfo1 {
    border: 2px solid rgb(11, 165, 236);
  }
}

.questions {
  margin-bottom: 10px;
}

.modelList {
  display: flex;
  align-items: center;
}

.var-card {
  position: relative;
  background: rgb(242, 244, 247); /* 与中间列灰底一致 */
  border-radius: 8px;
  margin-bottom: 10px;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    > :first-child {
      font-size: 15px;
    }

    > :last-child {
      font-size: 13px;
    }
  }

}

/* 单行变量 */
.var-line {
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 5px;
  padding: 5px 10px;

  > div {
    box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  &:hover {
    background-color: #F9FAFB;
  }
}

.var-line:last-child {
  margin-bottom: 0;
}

.var-actions {
  display: flex;
  gap: 8px;
  opacity: 0; /* 初始透明 */
  transition: opacity .15s ease; /* 平滑出现 */

  .icon-btn {
    cursor: pointer;

    &:hover {
      background-color: rgb(234, 236, 240);
      padding: 1px;
    }
  }
}

.var-tags {
  transition: opacity .15s ease;
  font-size: 10px;
}

.var-actions {
  opacity: 0;
  display: none;
  transition: .15s;
}

.var-key {
  font-size: 13px;
  margin: 0 5px;
}

.var-label {
  font-size: 12px;
  color: rgb(103, 111, 131);
  margin-right: 5px;
}

/* 悬停时切换可见性 */
.var-line:hover .var-tags {
  opacity: 0;
  display: none;
}

.var-line:hover .var-actions {
  opacity: 1;
  display: inline-block;
}

.type {
  font-size: 12px;
  float: right;
}

.tip {
  font-size: 12px;
  color: rgb(103, 111, 131);
}

.knowledgesLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  border: 1px solid rgba(16, 24, 40, 0.08);
  margin-bottom: 5px;
  border-radius: 5px;
  cursor: pointer;
  background-color: #fff;
  font-size: 13px;

  &:hover {
    background-color: #F9FAFB;

    .var-actions {
      opacity: 1;
      display: inline-block;
    }
  }

  span {
    margin-left: 5px;
  }
}

.active {
  border-color: #0000EE;
}

.var-card2 {
  background-color: #fff;
}

:deep(.el-card__body) {
  padding: 10px 12px;
}

.dialogBody {
  max-height: 300px;
  overflow-y: auto;
}

.dialogBody {
  max-height: 300px;
  overflow-y: auto;
}

.footerTip {
  font-size: 14px;
  float: left;
  color: #354052;
}

.el-radio-group :deep(.el-radio-button__inner) {
  padding: 12px !important;
}

:deep(.el-radio-button__inner:not(:first-child)) {
  border-left: 1px solid rgb(220, 223, 230);
  border-radius: 5px;
  width: 130px;
}

.arrItem {
  display: flex;
  flex-direction: column;
  align-items: center;

  span {
    margin-top: 5px;
  }
}

:deep(.el-radio-button.is-active .el-radio-button__original-radio:not(:disabled)+.el-radio-button__inner) {
  background-color: unset !important;
  color: rgb(81, 71, 255);
}

.opening {
  position: relative;
}

.editOpen {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding: 5px 20px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  justify-content: center;

  .edit {
    width: 20px;
  }
}

.answer-line {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.divider {
  height: 1px;
  background: #e5e7eb;
  margin: 16px 0;
}

.suggestionsBox {
  position: fixed;
  bottom: 80px;
  left: 50%;
  width: 100%;
  transform: translateX(-50%);
}

.functions {
  :deep(.el-card__body) {
    min-height: 80px;
  }
}

.query-var-row {
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* 背景 & 内边距 */
  background: #fff4d6; /* screenshot 中的淡黄色 */
  border: 1px solid #faecd8; /* 稍深的描边，不想要可注释掉 */
  border-radius: 8px;
  padding: 6px 10px;
  margin-top: 12px; /* 拉开与上面内容的距离 */

  /* 左侧图标 + 文本 */

  .var-mark {
    margin-right: 2px;
    color: #409eff; /* 蓝色的变量图标 */
  }

  span {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
  }

  /* 右侧 el-select */

  .query-var-select {
    width: 100px;
    --el-select-border-color: #ff9500; /* 边框橙色（Element Plus 2.5+ 变量写法） */
    --el-select-hover-border-color: #ffaa33;
    --el-select-focus-border-color: #ff9500;

    /* 触发框里文字 */

    .el-input__inner {
      color: #ff7f00; /* 文字橙色 */
      font-weight: 600;
    }

    /* 下拉箭头也染色 */

    .el-input__suffix .el-icon {
      color: #ff9500;
    }
  }
}

.searchVar {
  width: 60px;
}

.type-grid {
  display: flex !important;
}

.knowledgesLine2 {
  padding: 5px 20px;
}

.stop {
  position: absolute;
  bottom: 120px;
  right: 50%;
  padding: 5px;
  //width: 200px;
}

.monitor-header {
  //display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px
}

.monitor-chart {
  height: 320px;
  width: 100%
}
</style>