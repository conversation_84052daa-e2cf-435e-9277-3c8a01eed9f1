<template>
  <div class="layout-header">
    <span>用户管理</span>
    <el-input clearable style="width: 200px;" placeholder="姓名" @input="getData2" v-model="username" :prefix-icon="Search"></el-input>
    <el-input clearable style="width: 200px;" placeholder="账号" @input="getData2" v-model="useremail" :prefix-icon="Search"></el-input>
    <div>
      <el-button type="primary" @click="dialogFormVisible = true">+ 用户</el-button>
    </div>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column fixed label="序号" type="index" width="150"/>
    <el-table-column prop="Name" label="姓名" sortable/>
    <el-table-column prop="Email" label="账号" sortable/>
    <el-table-column prop="unit" label="单位" sortable/>
    <el-table-column prop="Department" label="部门" sortable/>
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <div class="flex items-center">
          <el-link @click="showUpPwd(scope.row)">修改密码</el-link>
          <el-tooltip
              class="box-item"
              effect="dark"
              content="编辑"
              placement="top-start"
          >
            <el-icon class="backIcon" @click="editUser(scope.row)">
              <Edit/>
            </el-icon>
          </el-tooltip>
          <el-tooltip
              class="box-item"
              effect="dark"
              content="删除用户"
              placement="top-start"
          >
            <el-icon class="backIcon" @click="deleteUser(scope.row.ID)">
              <Delete/>
            </el-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination style="margin:20px auto" background layout="prev, pager, next" :total="total"
                 @change="changePage" :page-count="totalPage"/>
  <el-dialog v-model="dialogFormVisible" title="添加用户" width="500">
    <el-form :model="addUserForm" :rules="rules" ref="ruleFormRef">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="addUserForm.name" placeholder=""/>
      </el-form-item>
      <el-form-item label="账号" prop="email">
        <el-input v-model="addUserForm.email" placeholder=""/>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="addUserForm.password" placeholder="8位以上包含字母和数字"/>
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-input v-model="addUserForm.unit" placeholder=""/>
      </el-form-item>
      <el-form-item label="部门" prop="Department">
        <el-input v-model="addUserForm.Department" placeholder=""/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="addUser">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="showEditUserDialog" title="编辑用户" width="500">
    <el-form :model="editUserForm" :rules="rules2" ref="editFormRef">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="editUserForm.name" placeholder=""/>
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-input v-model="editUserForm.unit" placeholder=""/>
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input v-model="editUserForm.department" placeholder=""/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showEditUserDialog = false">取消</el-button>
        <el-button type="primary" @click="updateUser">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="upPwdDialog" title="修改密码" width="400">
    <el-form :model="upPwdForm" :rules="rules3" ref="upPwdFormRef">
      <el-form-item label="新密码" prop="password">
        <el-input v-model="upPwdForm.password" placeholder="至少8位且包含字母和数字" type="password" show-password/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="upPwdDialog = false">取消</el-button>
        <el-button type="primary" @click="updateUserPwd">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup lang="ts" name="system-user">
import {ref, reactive} from 'vue';
import {ElMessage, ElMessageBox, FormInstance} from 'element-plus';
import {User} from '@/types/user';
import {getUsersApi, getUserApi, registerApi, updateUserApi, deleteUserApi, upUserPwd} from '@/api/user';
import {useUserStore} from "@/store/user";
import {addRelationApi} from '@/api/workspace';
import {Delete, Search} from "@element-plus/icons-vue";

const userStore = useUserStore();

const page = ref(1);
const total = ref(0);
const totalPage = ref(0);
const rules = {
  name: [
    {required: true, message: '请输入姓名', trigger: 'blur'},
    {max: 255, message: '姓名不能超过255个字符', trigger: 'blur'}
  ],
  email: [
    {required: true, message: '请输入账号', trigger: ['blur', 'change']},
    {
      pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
      message: '请输入正确的邮箱地址',
      trigger: ['blur', 'change']
    },
    {max: 255, message: '账号不能超过255个字符', trigger: ['blur', 'change']}
  ],
  password: [
    {required: true, message: '请输入密码', trigger: 'blur'},
    {pattern: /^(?=.*[A-Za-z])(?=.*\d).{8,}$/, message: '密码必须至少8位，且包含字母和数字', trigger: 'blur'}
  ],
  unit: [
    {required: true, message: '请输入单位', trigger: 'blur'}
  ],
  Department: [
    {required: true, message: '请输入部门', trigger: 'blur'}
  ]
};
const rules2 = {
  name: [
    {required: true, message: '请输入姓名', trigger: 'blur'},
    {max: 255, message: '姓名不能超过255个字符', trigger: 'blur'}
  ],
  unit: [
    {required: true, message: '请输入单位', trigger: 'blur'}
  ],
  department: [
    {required: true, message: '请输入部门', trigger: 'blur'}
  ]
};
const rules3 = {
  password: [
    {required: true, message: '请输入密码', trigger: 'blur'},
    {pattern: /^(?=.*[A-Za-z])(?=.*\d).{8,}$/, message: '密码必须至少8位，且包含字母和数字', trigger: 'blur'}
  ],
};
const ruleFormRef = ref<FormInstance>();
const editFormRef = ref<FormInstance>();
const upPwdFormRef = ref<FormInstance>();
const tableData = ref<User[]>([]);
const getData = async () => {
  const res = await getUsersApi({
    page: page.value,
  });
  tableData.value = res.data.data;
  total.value = res.data.total;
  totalPage.value = res.data.total_pages;
};
const username = ref('');
const useremail = ref('');
const getData2 = async () => {
  const res = await getUsersApi({
    page: page.value,
    name:username.value,
    email:useremail.value,
  });
  tableData.value = res.data.data;
  total.value = res.data.total;
  totalPage.value = res.data.total_pages;
};
getData();
const dialogFormVisible = ref(false);
const showEditUserDialog = ref(false);
const addUserForm = ref({});
const deleteUser = (userId) => {
  ElMessageBox.confirm(
      '',
      '是否删除该用户？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(async () => {
        await deleteUserApi(userId);
        await getData();
      })
      .catch(() => {
      });
}
const addUser = async () => {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      addUserForm.value['tenant_id'] = userStore.workspace.id;
      console.log('submit!', addUserForm.value)
      await registerApi(addUserForm.value);
      dialogFormVisible.value = false;
      await getData();
    } else {
      console.log('error submit!', fields)
    }
  })
}
const changePage = (newPage) => {
  page.value = newPage;
  getData();
}

const editUserForm = ref({});
const editUser = (user) => {
  showEditUserDialog.value = true;
  editUserForm.value = {
    id: user.ID,
    name: user.Name,
    unit: user.unit,
    department: user.Department,
  };
}
const updateUser = async () => {
  if (!editFormRef.value) return
  await editFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      await updateUserApi(editUserForm.value);
      showEditUserDialog.value = false;
      await getData();
    } else {
      console.log('error submit!', fields)
    }
  })

}

const upPwdDialog = ref(false);
const upPwdForm = reactive({
  id: '',        // 建议用小写 id，与后端字段保持一致
  password: ''
})
const showUpPwd = (user) => {
  upPwdDialog.value = true;
  upPwdForm.id = user.ID;
  upPwdForm.password = '';
}
const updateUserPwd = async ()=>{
  if (!upPwdFormRef.value) return
  await upPwdFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      await upUserPwd(upPwdForm);
      upPwdDialog.value = false;
      await getData();
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style scoped></style>