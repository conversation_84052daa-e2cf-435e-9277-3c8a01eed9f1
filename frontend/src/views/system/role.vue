<template>
  <div class="layout-header">
    <span>角色管理</span>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column fixed label="序号" type="index" width="80"/>
    <el-table-column prop="name" label="角色名称" sortable width="150"/>
    <el-table-column prop="description" label="角色描述" sortable/>
  </el-table>
</template>

<script setup lang="ts" name="tenants">
import {ref} from 'vue';
import {getRolesApi} from "@/api/user";

const tableData = ref([]);
const getData = async () => {
  const res = await getRolesApi();
  tableData.value = res;
};
getData();

</script>

<style scoped></style>