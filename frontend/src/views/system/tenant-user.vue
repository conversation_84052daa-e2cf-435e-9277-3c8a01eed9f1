<template>
  <div class="layout-header">
    <span>团员管理</span>
    <div>
      <el-button type="primary" @click="showAddDialog">+ 邀请用户</el-button>
    </div>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column fixed label="序号" type="index" width="150"/>
    <el-table-column prop="AccountName" label="姓名" sortable/>
    <el-table-column label="角色" width="150" prop="Role" sortable>
      <template #default="scope">
        <el-select v-model="scope.row.Role" :disabled="scope.row.Role === 'owner'"
                   @change="changeRole(scope.row)">
          <el-option value="owner" label="超管" v-if="userStore.userRole === 'owner'" disabled></el-option>
          <el-option value="admin" label="管理员" v-if="userStore.userRole === 'owner'"
                     :disabled="userStore.userRole !== 'owner'"></el-option>
          <el-option value="editor" label="编辑者"></el-option>
          <el-option value="normal" label="普通员工"></el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column prop="unit" label="单位" sortable/>
    <el-table-column prop="department" label="部门" sortable/>
    <el-table-column label="操作">
      <template #default="scope">
        <div class="flex items-center">
          <el-tooltip
              class="box-item"
              effect="dark"
              content="踢出团队"
              placement="top-start"
          >
            <img src="@/assets/img/svg/relationOver.svg" alt="" class="w-15 backIcon" @click="delRelation(scope.row)"
                 v-if="scope.row.Role !== 'owner' && scope.row.AccountID !== userStore?.user?.id"/>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination style="margin:20px auto" background layout="prev, pager, next" :total="total"
                 @change="changePage" :page-count="totalPage"/>
  <el-dialog v-model="dialogFormVisible" title="添加用户" width="500">
    <el-form :model="addUserForm" :rules="rules" ref="ruleFormRef">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="addUserForm.name" placeholder=""/>
      </el-form-item>
      <el-form-item label="账号" prop="email">
        <el-input v-model="addUserForm.email" placeholder=""/>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="addUserForm.password" placeholder="8位以上包含字母和数字"/>
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-input v-model="addUserForm.unit" placeholder=""/>
      </el-form-item>
      <el-form-item label="部门" prop="Department">
        <el-input v-model="addUserForm.Department" placeholder=""/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="addUser">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="InviteVisible" title="邀请用户" width="500">
    <el-form :model="addRelationForm" :rules="rules2" ref="ruleFormRef2">
      <el-form-item label="用户" prop="Name">
        <el-select v-model="addRelationForm.account_id" filterable>
          <el-option v-for="item in canJoinAccountsOptions" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="角色" prop="Name">
        <el-select v-model="addRelationForm.role">
          <el-option v-for="item in roleOptions" :value="item.value" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="InviteVisible = false">取消</el-button>
        <el-button type="primary" @click="addRelation">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="system-user">
import {ref, reactive} from 'vue';
import {ElMessage, ElMessageBox, FormInstance} from 'element-plus';
import {User} from '@/types/user';
import {getUsersApi, getUserApi, registerApi, updateUserApi, deleteUserApi, changeRoleApi} from '@/api/user';
import {useUserStore} from "@/store/user";
import {
  getTenantAccountList,
  getCanjoinTenant,
  delRelationApi,
  getCanjoinAccount,
  addRelationApi
} from '@/api/workspace';
import {getCurrentWorkspaces} from '@/api/system';
import {Delete} from "@element-plus/icons-vue";

const userStore = useUserStore();

const page = ref(1);
const total = ref(0);
const totalPage = ref(0);
const rules = {
  name: [
    {required: true, message: '请输入姓名', trigger: 'blur'},
    {max: 255, message: '姓名不能超过255个字符', trigger: 'blur'}
  ],
  email: [
    {required: true, message: '请输入账号', trigger: ['blur', 'change']},
    {
      pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
      message: '请输入正确的邮箱地址',
      trigger: ['blur', 'change']
    },
    {max: 255, message: '账号不能超过255个字符', trigger: ['blur', 'change']}
  ],
  password: [
    {required: true, message: '请输入密码', trigger: 'blur'},
    {pattern: /^(?=.*[A-Za-z])(?=.*\d).{8,}$/, message: '密码必须至少8位，且包含字母和数字', trigger: 'blur'}
  ],
  unit: [
    {required: true, message: '请输入单位', trigger: 'blur'}
  ],
  Department: [
    {required: true, message: '请输入部门', trigger: 'blur'}
  ]
};
const rules2 = {
  account_id: [
    {required: true, message: '请选择用户', trigger: 'blur'},
  ],
  role: [
    {required: true, message: '请选择角色', trigger: 'blur'},
  ],
};
const ruleFormRef = ref<FormInstance>();
const ruleFormRef2 = ref<FormInstance>();
const tableData = ref<User[]>([]);
const getData = async () => {
  let teamId = null;
  if (!userStore.workspace.id) {
    let res = await getCurrentWorkspaces();
    teamId = res.id
  }
  const res = await getTenantAccountList({
    page: page.value,
    tenant_id: userStore.workspace.id ?? teamId
  });
  tableData.value = res.data.data;
  total.value = res.data.total;
  totalPage.value = res.data.total_pages;
};
getData();
const dialogFormVisible = ref(false);
const InviteVisible = ref(false);
const addUserForm = ref({});
const addRelationForm = ref({});
const deleteUser = (userId) => {
  ElMessageBox.confirm(
      '',
      '是否删除该用户？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(async () => {
        await deleteUserApi(userId);
        await getData();
      })
      .catch(() => {
      });
}
const addUser = async () => {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      let teamId = null;
      if (!userStore.workspace.id) {
        let res = await getCurrentWorkspaces();
        teamId = res.id
      }
      addUserForm.value['tenant_id'] = userStore.workspace.id ?? teamId;
      console.log('submit!', addUserForm.value)
      await registerApi(addUserForm.value);
      dialogFormVisible.value = false;
      await getData();
    } else {
      console.log('error submit!', fields)
    }
  })
}
const changeRole = async (user) => {
  console.log('user', user)
  await changeRoleApi({
    id: user.ID,
    role: user.Role
  });
}
const changePage = (newPage) => {
  page.value = newPage;
  getData();
}
const roleOptions = ref([]);
const canJoinAccountsOptions = ref([]);
const canJoinWorkspacesOptions = ref([]);
const showAddDialog = async () => {
  let res1 = await getCanjoinTenant();
  let res2 = await getCanjoinAccount(userStore.workspace.id);
  roleOptions.value = [
    {
      "name": "管理者",
      "value": "admin",
      "description": "负责日常管理工作，可以管理成员、配置项目设置，但无法删除所有者。"
    },
    {
      "name": "编辑者",
      "value": "editor",
      "description": "可以编辑内容和资源，但不能修改权限和删除成员。"
    },
    {
      "name": "普通员工",
      "value": "normal",
      "description": "仅限访问和查看权限，不能修改内容或管理其他成员。"
    }];
  console.log('roleOptions.value', roleOptions.value)
  canJoinWorkspacesOptions.value = res1.data;
  canJoinAccountsOptions.value = res2.data;
  InviteVisible.value = true;
}
const delRelation = async (row) => {
  ElMessageBox.confirm(
      '',
      '是否将该用户踢出团队？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(async () => {
        await delRelationApi(row.ID);
        await getData();
      })
      .catch(() => {
      });

}

const addRelation = async () => {

  if (!ruleFormRef2.value) return
  await ruleFormRef2.value.validate(async (valid, fields) => {
    if (valid) {
      let teamId = null;
      if (!userStore.workspace.id) {
        teamId = await getCurrentWorkspaces();
      }
      addRelationForm.value['tenant_id'] = userStore.workspace.id ?? teamId;
      await addRelationApi(addRelationForm.value);
      InviteVisible.value = false;
      await getData();
      addRelationForm.value = [];
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style scoped></style>