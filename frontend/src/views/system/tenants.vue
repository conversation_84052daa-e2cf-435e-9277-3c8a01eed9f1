<template>
  <div class="layout-header">
    <span>团队管理</span>
    <el-button type="primary" @click="showAddTenant">+ 团队</el-button>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column fixed label="序号" type="index" width="150"/>
    <el-table-column prop="Name" label="名称" sortable/>
    <el-table-column prop="owner_name" label="团长" sortable/>
    <el-table-column prop="owner_email" label="团长账号" sortable/>
    <el-table-column prop="CreatedAt" label="创建时间" sortable>
      <template #default="scope">
        {{ $moment(scope.row.CreatedAt).format('YYYY/MM/DD HH:mm') }}
      </template>
    </el-table-column>
    <el-table-column prop="UpdatedAt" label="更新时间" sortable>
      <template #default="scope">
        {{ $moment(scope.row.UpdatedAt).format('YYYY/MM/DD HH:mm') }}
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <div class="flex items-center">
          <el-tooltip
              class="box-item"
              effect="dark"
              content="删除团队"
              placement="top-start"
          >
            <el-icon class="backIcon" @click="deleteTenant(scope.row.ID)" v-if="!scope.row.default_team">
              <Delete/>
            </el-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination style="margin:20px auto" background layout="prev, pager, next" :total="total"
                 @change="changePage" :page-count="totalPage"/>
  <el-dialog v-model="dialogFormVisible" title="添加团队" width="500">
    <el-form :model="addUserForm" :rules="rules" ref="ruleFormRef">

      <el-form-item label="团队名称" prop="name">
        <el-input v-model="addUserForm.name" placeholder=""/>
      </el-form-item>
      <el-form-item label="指定团长" prop="user_id">
        <el-select v-model="addUserForm.user_id">
          <el-option v-for="item in canInviteUsers" :label="item.Name" :value="item.ID"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="addUser">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="tenants">
import {ref, reactive} from 'vue';
import {ElMessage, ElMessageBox, FormInstance} from 'element-plus';
import {User} from '@/types/user';
import {getWorkspacesList, addWorkspaces, delTenantApi, getCanjoinAccount} from '@/api/workspace';
import {useUserStore} from "@/store/user";
import {getUsersApi} from "@/api/user";
import $moment from 'moment';
import {Delete} from "@element-plus/icons-vue";

const userStore = useUserStore();

const page = ref(1);
const total = ref(0);
const totalPage = ref(0);
const rules = {
  name: [
    {required: true, message: '请输入团队名称', trigger: 'blur'},
    {max: 255, message: '名称不能超过255个字符', trigger: 'blur'}
  ],
  user_id: [
    {required: true, message: '请指定一名团长', trigger: 'blur'},
  ],
};
const ruleFormRef = ref<FormInstance>();
const tableData = ref<User[]>([]);
const getData = async () => {
  const res = await getWorkspacesList({
    page: page.value,
  });
  tableData.value = res.data.data;
  total.value = res.data.total;
  totalPage.value = res.data.total_pages;
};
getData();
const dialogFormVisible = ref(false);
const addUserForm = ref({});

const addUser = async () => {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      await addWorkspaces(addUserForm.value);
      dialogFormVisible.value = false;
      await getData();
    } else {
      console.log('error submit!', fields)
    }
  })
}
const changePage = (newPage) => {
  page.value = newPage;
  getData();
}
const deleteTenant = async (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    type: 'warning'
  })
      .then(async () => {
        await delTenantApi(id);
        await getData();
      })
      .catch(() => {
      });

}
const canInviteUsers = ref([]);
const showAddTenant = async () => {
  let res = await getUsersApi({
    page: 1,
    size: 999
  });
  canInviteUsers.value = res.data.data;
  dialogFormVisible.value = true;
}
</script>

<style scoped></style>