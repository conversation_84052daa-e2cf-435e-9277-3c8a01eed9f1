<template>
  <div class="login-bg">
    <div class="logoBar">
      <img src="../../assets/img/logo.png" alt=""/>
      <span>商洛市烟草专卖局(公司)AI平台</span>
    </div>
    <div class="login-container">
      <div class="reset-title">重置密码</div>
      <p class="reset-text">输入你的邮箱，我们将发送重置密码邮件</p>
      <el-form :model="param" :rules="rules" ref="register" size="large">
        <el-form-item prop="email">
          <el-input v-model="param.email" placeholder="邮箱">
            <template #prepend>
              <el-icon>
                <Message/>
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-button class="login-btn" type="primary" size="large" @click="submitForm(register)"
        >发送邮件
        </el-button
        >
        <p class="login-text">
          <el-link type="primary" @click="$router.push('/login')">返回登录</el-link>
        </p>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue';
import {ElMessage, type FormInstance, type FormRules} from 'element-plus';

const param = ref({
  email: '',
});

const rules: FormRules = {
  email: [
    {required: true, message: '请输入邮箱', trigger: 'blur'},
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入正确的邮箱格式',
      trigger: 'blur',
    },
  ],
};
const register = ref<FormInstance>();
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('邮件已发送，请注意查收');
    } else {
      return false;
    }
  });
};
</script>

<style scoped>
.login-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: linear-gradient(173deg, #ecf4ff -.79%, #d3e1ff 94.5%);
}

.reset-title {
  text-align: center;
  font-size: 22px;
  color: #333;
  font-weight: bold;
  margin-bottom: 10px;
}

.reset-text {
  text-align: center;
  font-size: 14px;
  color: #787878;
  margin-bottom: 40px;
}

.login-container {
  width: 480px;
  background: #fff;
  padding:40px 64px 120px;
  box-sizing: border-box;
  border-radius: 12px;
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, .1), 0 0 1px 0 rgba(0, 0, 0, .3);
}

.login-btn {
  display: block;
  width: 100%;
  background-color: rgba(77, 83, 232);
  border: none;
}

.login-text {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  font-size: 14px;
  color: #333;
}

.logoBar {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  align-items: center;
}

.logoBar img {
  width: 42px;
  height: 42px;
  margin-right: 10px;
}
</style>
