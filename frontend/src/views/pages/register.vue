<template>
  <div class="login-bg">
    <div class="login-container">
      <div class="logoBar">
        <img src="../../assets/img/logo.png" alt=""/>
        <span>商洛市烟草专卖局(公司)AI平台</span>
      </div>
      <div class="login-header">
        <img class="logo mr10" src="../../assets/img/logo.png" alt=""/>
        <div class="login-title">商洛市烟草专卖局(公司)AI平台</div>
      </div>
      <el-form :model="param" :rules="rules" ref="register" size="large">
        <el-form-item prop="username">
          <el-input v-model="param.username" placeholder="用户名">
          </el-input>
        </el-form-item>
        <el-form-item prop="email">
          <el-input v-model="param.email" placeholder="邮箱">
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
              type="password"
              placeholder="密码"
              v-model="param.password"
              @keyup.enter="submitForm(register)"
          >
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
              type="password"
              placeholder="确认密码"
              v-model="param.password2"
              @keyup.enter="submitForm(register)"
          >
          </el-input>
        </el-form-item>
        <el-button class="login-btn" type="primary" size="large" @click="submitForm(register)">注册</el-button>
        <p class="login-text">
          已有账号，
          <el-link type="primary" @click="$router.push('/login')">立即登录</el-link>
        </p>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from 'vue';
import {useRouter} from 'vue-router';
import {ElMessage, type FormInstance, type FormRules} from 'element-plus';
import {Register} from '@/types/user';
import {registerApi} from '@/api/user';

const router = useRouter();
const param = reactive<Register>({
  username: '',
  password: '',
  password2: '',
  email: '',
});

const rules: FormRules = {
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur',
    },
    {
      min: 3,
      max: 20,
      message: '用户名长度在 3 到 20 个字符之间',
      trigger: 'blur',
    },
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: 'blur'
    },
    {
      min: 6,
      message: '密码长度至少为 6 个字符',
      trigger: 'blur'
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱',
      trigger: 'blur'
    },
    {
      type: 'email',
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    },
  ],
};

const register = ref<FormInstance>();
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  if (param.password !== param.password2) {
    ElMessage.error('密码不一致');
    return;
  }
  formEl.validate((valid: boolean) => {
    if (valid) {
      registerApi({
        name: param.username,
        email: param.email,
        password: param.password,
        password_confirmation: param.password2,
      }).then(res => {
        if(res.success){
          ElMessage.success('注册成功，请登录');
          router.push('/login');
        }else{
          ElMessage.error(res.data.message);
        }
      });

    } else {
      return false;
    }
  });
};
</script>

<style scoped>
.login-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: linear-gradient(173deg, #ecf4ff -.79%, #d3e1ff 94.5%);
}

.login-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
}

.logo {
  width: 35px;
}

.login-title {
  font-size: 22px;
  color: #333;
  font-weight: bold;
}

.login-container {
  width: 480px;
  background: #fff;
  padding:40px 64px 120px;
  box-sizing: border-box;
  border-radius: 12px;
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, .1), 0 0 1px 0 rgba(0, 0, 0, .3);
}

.login-btn {
  display: block;
  background-color: rgba(77, 83, 232);
  border:none;
}

.login-text {
  display: flex;
  align-items: center;
  margin-top: 20px;
  font-size: 14px;
  color: #787878;
}

.logoBar{
  position: absolute;
  width: 100%;
  top:10px;
  left:10px;
  display: flex;
  align-items: center;
}
.logoBar img{
  width: 42px;
  height: 42px;
  margin-right: 10px;
}
</style>
