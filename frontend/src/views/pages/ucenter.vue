<template>
  <div>
    <div class="user-container">
      <el-card class="user-profile" shadow="hover" :body-style="{ padding: '0px' }">
        <div class="user-profile-bg"></div>
        <div class="user-avatar-wrap">
          <el-avatar
              class="user-avatar"
              :size="120"
              :src="userAvatar"
              :style="!userAvatar ? { backgroundColor: '#006CFF', color: '#fff', fontSize: '36px' } : {}"
          >
            <!-- 头像为空时显示名字首字 -->
            <template v-if="!userAvatar">
              {{ form.name?.charAt(0) || '无' }}
            </template>
          </el-avatar>
        </div>
        <div class="user-info">
          <div class="info-name">{{ form.name }}</div>
          <div class="info-desc">
            <span>账号</span>
            <el-divider direction="vertical"/>
            <el-link>{{ form.email }}</el-link>
          </div>
          <div class="info-desc">最近登录时间：{{ $moment.unix(form.last_login_at).format('YYYY-MM-DD HH:mm') }}</div>
          <!--          <div class="info-icon">-->
          <!--            <a href="https://github.com/lin-xin" target="_blank"> <i class="el-icon-lx-github-fill"></i></a>-->
          <!--            <i class="el-icon-lx-qq-fill"></i>-->
          <!--            <i class="el-icon-lx-facebook-fill"></i>-->
          <!--            <i class="el-icon-lx-twitter-fill"></i>-->
          <!--          </div>-->
        </div>
        <div class="user-footer">
          <div class="user-footer-item">
            <el-statistic title="关注" :value="888"/>
          </div>
          <div class="user-footer-item">
            <el-statistic title="粉丝" :value="888"/>
          </div>
          <div class="user-footer-item">
            <el-statistic title="收藏" :value="888"/>
          </div>
        </div>
      </el-card>
      <el-card
          class="user-content"
          shadow="hover"
          :body-style="{ padding: '20px 20px', height: '100%', boxSizing: 'border-box' }"
      >
        <el-tabs tab-position="left" v-model="activeName">
          <!--                    <el-tab-pane name="label1" label="消息通知" class="user-tabpane">-->
          <!--                        <TabsComp />-->
          <!--                    </el-tab-pane>-->
          <el-tab-pane name="label2" label="我的头像" class="user-tabpane">
            <!--                        <div class="crop-wrap" v-if="activeName === 'label2'">-->
            <!--                            <vueCropper-->
            <!--                                ref="cropper"-->
            <!--                                :img="imgSrc"-->
            <!--                                :autoCrop="true"-->
            <!--                                :centerBox="true"-->
            <!--                                :full="true"-->
            <!--                                mode="contain"-->
            <!--                            >-->
            <!--                            </vueCropper>-->
            <!--                        </div>-->
            <!--                        <el-button class="crop-demo-btn" type="primary"-->
            <!--                            >选择图片-->
            <!--                            <input class="crop-input" type="file" name="image" accept="image/*" @change="setImage" />-->
            <!--                        </el-button>-->
            <!--                        <el-button type="success" @click="saveAvatar">上传并保存</el-button>-->
            <!--            <el-form-item label="姓名">-->
            <!--              <el-input v-model="form.name" size="small"/>-->
            <!--            </el-form-item>-->
            <el-upload class="avatar-uploader"
                       :action="uploadUrl"
                       :headers="uploadHeaders"
                       :show-file-list="false"
                       :multiple="false"
                       :on-success="handleAvatarSuccess"
                       :before-upload="beforeUpload"
                       :on-error="handleUploadError">
              <!--              <img v-if="form.avatar" :src="form.avatar" class="avatar"/>-->
              <!--              <el-avatar-->
              <!--                  class="user-avatar"-->
              <!--                  :size="120"-->
              <!--                  :src="form.avatar_url"-->
              <!--                  :style="!form.avatar ? { backgroundColor: '#006CFF', color: '#fff', fontSize: '36px' } : {}"-->
              <!--              >-->
              <!--                &lt;!&ndash; 头像为空时显示名字首字 &ndash;&gt;-->
              <!--                <template v-if="!form.avatar">-->
              <!--                  {{ form.name?.charAt(0) || '无' }}-->
              <!--                </template>-->
              <!--              </el-avatar>-->
              <el-icon class="avatar-uploader-icon">
                <Plus/>
              </el-icon>
            </el-upload>
          </el-tab-pane>
          <el-tab-pane name="label3" label="修改密码" class="user-tabpane">
            <el-form class="w500" label-position="top" :rules="upPassRule" ref="formRef" :model="form2">
              <el-form-item label="旧密码：" prop="password">
                <el-input type="password" v-model="form2.password"></el-input>
              </el-form-item>
              <el-form-item label="新密码：" prop="new_password">
                <el-input type="password" v-model="form2.new_password"></el-input>
              </el-form-item>
              <el-form-item label="确认新密码：" prop="repeat_new_password">
                <el-input type="password" v-model="form2.repeat_new_password"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="changePasswordFunc">保存</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <!--                    <el-tab-pane name="label4" label="赞赏作者" class="user-tabpane">-->
          <!--                        <div class="plugins-tips">-->
          <!--                            如果该框架-->
          <!--                            <el-link href="https://github.com/lin-xin/vue-manage-system" target="_blank"-->
          <!--                                >vue-manage-system</el-link-->
          <!--                            >-->
          <!--                            对你有帮助，那就请作者喝杯饮料吧！<el-icon>-->
          <!--                                <ColdDrink />-->
          <!--                            </el-icon>-->
          <!--                            加微信号 linxin_20 探讨问题。-->
          <!--                        </div>-->
          <!--                        <div>-->
          <!--                            <img src="https://lin-xin.gitee.io/images/weixin.jpg" />-->
          <!--                        </div>-->
          <!--                    </el-tab-pane>-->
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts" name="ucenter">
import {computed, reactive, ref} from 'vue';
import 'vue-cropper/dist/index.css';
import {UploadProps} from 'element-plus';
import {Plus} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import {getNowUser, changePassword, setAvatar} from '@/api/user';
import {useUserStore} from '@/store/user'
import $moment from 'moment';

const userStore = useUserStore()

// ② 你的头像 computed，强依赖一下 lsVersion
const userAvatar = computed(() => {
  return userStore.avatar !== '' ? userStore.avatar_url : '';
})
const form = ref({
  avatar: '',
  avatar_url: '',
  name: '',
  email:'',
  last_login_at:'',
});

const activeName = ref('label2');

const uploadUrl = `${import.meta.env.VITE_SERVE_HOST}/console/api/files/upload`;
const getUploadHeaders = () => {
  const token = localStorage.getItem('console_token');
  if (!token) {
    ElMessage.error('未找到有效的 token，请重新登录');
    return null;
  }
  return {
    'Authorization': `Bearer ${token}`,
    // 'X-Requested-With': 'XMLHttpRequest',
    // 'Accept': 'application/json'
  };
};
const uploadHeaders = ref(getUploadHeaders());
// 上传前的文件验证方法
const beforeUpload = (file) => {
  console.log('上传文件类型:', file.type);
  console.log('上传文件大小:', file.size);
  const allowedTypes = ['image/jpeg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB');
    return false;
  }
  return true;
};

// 上传成功后的处理方法
const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  if (response) {
    console.log('fileUrlaaaaaaa', response)
    let fileId = response.id;
    setAvatar({avatar: fileId}).then(res => {
      console.log('图片', res.avatar_url)
      loadData();
    });

  } else {
    ElMessage.error('图片上传失败');
  }
};

// 上传失败处理方法
const handleUploadError = (error, uploadFile) => {
  console.error('图片上传失败:', error);
  ElMessage.error('图片上传失败，请检查网络或服务器状态');
};

const loadData = () => {
  getNowUser().then(res => {
    userStore.setUser(res);
    form.value = res;
  });
}
loadData();

const form2 = reactive({
  password: '',
  new_password: '',
  repeat_new_password: ''
});

const upPassRule = reactive({
  password: [
    {required: true, message: '请输入旧密码', trigger: 'blur'},
  ],
  new_password: [
    {required: true, message: '请输入新密码', trigger: 'blur'},
    {min: 8, max: 20, message: '密码长度在 8 到 20 个字符之间', trigger: 'blur'},
    {
      pattern: /^(?=.*[A-Za-z])(?=.*\d).{8,20}$/,
      message: '密码必须包含字母和数字',
      trigger: 'blur'
    },
  ],
  repeat_new_password: [
    {required: true, message: '请再次输入新密码', trigger: 'blur'},
    {
      validator(rule, value, callback) {
        if (value !== form2.new_password) {
          callback(new Error('两次输入的新密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

const formRef = ref(null);

const changePasswordFunc = async () => {
  try {
    await formRef.value.validate();
  } catch (error) {
    ElMessage.warning('请检查表单填写是否正确');
    return;
  }

  const res = await changePassword(form2);
  if (res) {
    ElMessage.success('修改成功');
    formRef.value.resetFields();
    form2.password = '';
    form2.new_password = '';
    form2.repeat_new_password = '';
  } else {
    ElMessage.error(res.message || '修改失败');
  }
};

</script>

<style scoped>
.user-container {
  display: flex;
}

.user-profile {
  position: relative;
}

.user-profile-bg {
  width: 100%;
  height: 200px;
  background-image: url('../../assets/img/ucenter-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.user-profile {
  width: 500px;
  margin-right: 20px;
  flex: 0 0 auto;
  align-self: flex-start;
}

.user-avatar-wrap {
  position: absolute;
  top: 135px;
  width: 100%;
  text-align: center;
}

.user-avatar {
  border: 5px solid #fff;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 7px 12px 0 rgba(62, 57, 107, 0.16);
}

.user-info {
  text-align: center;
  padding: 80px 0 30px;
}

.info-name {
  margin: 0 0 20px;
  font-size: 22px;
  font-weight: 500;
  color: #373a3c;
}

.info-desc {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
  font-size: 12px !important;
}

.info-desc,
.info-desc a {
  font-size: 18px;
  color: #55595c;
}

.info-icon {
  margin-top: 10px;
}

.info-icon i {
  font-size: 30px;
  margin: 0 10px;
  color: #343434;
}

.user-content {
  flex: 1;
}

.user-tabpane {
  padding: 10px 20px;
}

.w500 {
  width: 500px;
}

.user-footer {
  display: flex;
  border-top: 1px solid rgba(83, 70, 134, 0.1);
}

.user-footer-item {
  padding: 20px 0;
  width: 33.3333333333%;
  text-align: center;
}

.user-footer > div + div {
  border-left: 1px solid rgba(83, 70, 134, 0.1);
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 8px;
}
</style>

<style>
.el-tabs.el-tabs--left {
  height: 100%;
}
</style>
