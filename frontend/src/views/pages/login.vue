<template>
  <div class="login-bg">
    <div class="login-container">
      <div class="logoBar">
        <img src="../../assets/img/logo.png" alt="" />
        <span>商洛市烟草专卖局(公司)AI平台</span>
      </div>
      <div class="login-header">
        <img class="logo mr10" src="../../assets/img/logo.png" alt=""/>
        <div class="login-title">商洛市烟草专卖局(公司)AI平台</div>
      </div>
      <el-form :model="param" :rules="rules" ref="login" size="large">
        <el-form-item prop="email">
          <el-input v-model="param.email" placeholder="邮箱">
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
              type="password"
              placeholder="密码"
              show-password
              v-model="param.password"
              @keyup.enter="submitForm(login)"
          >
          </el-input>
        </el-form-item>
        <div class="pwd-tips">
          <el-checkbox class="pwd-checkbox" v-model="checked" label="记住密码"/>
<!--          <el-link type="primary" @click="$router.push('/reset-pwd')">忘记密码</el-link>-->
        </div>
        <el-button class="login-btn" type="primary" size="large" @click="submitForm(login)">登录</el-button>
<!--        <p class="login-text">-->
<!--          没有账号？-->
<!--          <el-link type="primary" @click="$router.push('/register')">立即注册</el-link>-->
<!--        </p>-->
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from 'vue';
import {useTabsStore} from '@/store/tabs';
import {usePermissStore} from '@/store/permiss';
import {useRouter} from 'vue-router';
import {ElMessage} from 'element-plus';
import type {FormInstance, FormRules} from 'element-plus';
import {loginApi} from '@/api/user'
import {getWorkspaces} from '@/api/workspace'
import { useUserStore } from '@/store/user'
import { getProfile,workspacesCurrent } from '@/api/system'
import {useWorkspaceStore} from "@/store/workspace";
const userStore = useUserStore()
const workspaceStore = useWorkspaceStore()
const router = useRouter();
interface LoginInfo {
  email: string;
  password: string;
}

const lgStr = localStorage.getItem('login-param');
const defParam = lgStr ? JSON.parse(lgStr) : null;
const checked = ref(lgStr ? true : false);


const param = reactive<LoginInfo>({
  email: defParam ? defParam.email : '',
  password: defParam ? defParam.password : '',
});

const rules: FormRules = {
  email: [
    {
      required: true,
      message: '请输入邮箱',
      trigger: 'blur',
    },
  ],
  password: [{required: true, message: '请输入密码', trigger: 'blur'}],
};
const login = ref<FormInstance>();
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async (valid: any) => {
    if (valid) {

      // localStorage.setItem('vuems_name', param.email);
      // const keys = permiss.defaultList[param.email == 'admin' ? 'admin' : 'user'];
      // permiss.handleSet(keys);
      let res = await loginApi({
        email: param.email,
        password: param.password
      });
      if(!res.data.access_token){
        return;
      }else{
        router.push('/');
        localStorage.setItem('console_token', res.data.access_token);
        localStorage.setItem('refresh_token', res.data.refresh_token);

        if (checked.value) {
          localStorage.setItem('login-param', JSON.stringify(param));
        } else {
          localStorage.removeItem('login-param');
        }
        // let res4 = await getWorkspaces();
        // workspaceStore.setList(res4.workspaces);
        let res2 = await getProfile();
        localStorage.setItem('user', JSON.stringify(res2));
        userStore.setUser(res2);
        location.reload();
      }
    } else {
      ElMessage.error('登录失败');
      return false;
    }
  });
};

const tabs = useTabsStore();
tabs.clearTabs();
</script>

<style scoped>
.login-bg {

  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: linear-gradient(173deg, #ecf4ff -.79%, #d3e1ff 94.5%);
}
.logoBar{
  position: absolute;
  top:10px;
  left:10px;
  display: flex;
  align-items: center;
}
.logoBar img{
  width: 42px;
  height: 42px;
  margin-right: 10px;
}
.login-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
}

.logo {
  width: 35px;
}

.login-title {
  font-size: 22px;
  color: #333;
  font-weight: bold;
}

.login-container {
  width: 480px;
  background: #fff;
  padding:40px 64px 120px;
  box-sizing: border-box;
  border-radius: 12px;
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, .1), 0 0 1px 0 rgba(0, 0, 0, .3);
}

.pwd-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  margin: -10px 0 10px;
  color: #787878;
}

.pwd-checkbox {
  height: auto;
}

.login-btn {
  display: block;
  width: 100%;
  background-color: rgba(77, 83, 232);
  border:none;
}

.login-tips {
  font-size: 12px;
  color: #999;
}

.login-text {
  display: flex;
  align-items: center;
  margin-top: 60px;
  font-size: 14px;
  color: #787878;
}

:deep(.el-form-item){
  margin-bottom: 40px;
}
</style>
