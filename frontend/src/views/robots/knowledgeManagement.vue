<template>
  <div class="layout-header">
    <span>知识库管理</span>
    <el-input clearable style="width: 200px;" placeholder="搜索" @input="getData2" v-model="keyword" :prefix-icon="Search"></el-input>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column fixed label="序号" type="index" width="150"/>
    <el-table-column prop="name" label="名称" sortable/>
    <el-table-column prop="description" label="描述" sortable>
      <template #default="{ row }">
        <span class="table-ellipsis">{{ row.description }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="CreatedAt" label="创建时间" sortable>
      <template #default="scope">
        {{ $moment(scope.row.CreatedAt).format('YYYY/MM/DD HH:mm') }}
      </template>
    </el-table-column>
    <el-table-column prop="UpdatedAt" label="更新时间" sortable>
      <template #default="scope">
        {{ $moment(scope.row.UpdatedAt).format('YYYY/MM/DD HH:mm') }}
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <div class="flex items-center">
          <el-tooltip
              class="box-item"
              effect="dark"
              content="删除团队"
              placement="top-start"
          >
            <el-icon class="backIcon" @click="deleteTenant(scope.row.ID)" v-if="!scope.row.default_team">
              <Delete/>
            </el-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination style="margin:20px auto" background layout="prev, pager, next" :total="total"
                 @change="changePage" />
</template>

<script setup lang="ts" name="tenants">
import {ref, reactive} from 'vue';
import {ElMessage, ElMessageBox, FormInstance} from 'element-plus';
import {User} from '@/types/user';
import {
  getKnowledgeBaseListApi,
  deleteKnowledgeBaseApi
} from '@/api/knowledge';
import $moment from 'moment';
import {Delete, Search} from "@element-plus/icons-vue";

const page = ref(1);
const total = ref(0);
const totalPage = ref(0);
const rules = {
  name: [
    {required: true, message: '请输入团队名称', trigger: 'blur'},
    {max: 255, message: '名称不能超过255个字符', trigger: 'blur'}
  ],
  user_id: [
    {required: true, message: '请指定一名团长', trigger: 'blur'},
  ],
};
const tableData = ref<User[]>([]);
const getData = async () => {
  const res = await getKnowledgeBaseListApi({
    page: page.value,
    limit:10,
    include_all:true
  });
  tableData.value = res.data;
  total.value = res.total;
};
const keyword = ref('');
const getData2 = async () => {
  const res = await getKnowledgeBaseListApi({
    page: page.value,
    limit:10,
    keyword:keyword.value,
    include_all:true
  });
  tableData.value = res.data;
  total.value = res.total;
};
getData();

const changePage = (newPage) => {
  page.value = newPage;
  getData();
}
const deleteTenant = async (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    type: 'warning'
  })
      .then(async () => {
        await deleteKnowledgeBaseApi(id);
        await getData();
      })
      .catch(() => {
      });
}

</script>

<style scoped>
.table-ellipsis {
  display: inline-block; /* 或 block, 取决于你的布局 */
  max-width: 100%; /* 不能超出列宽 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>