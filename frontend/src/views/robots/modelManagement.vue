<template>
  <div>
<!--    <TableSearch :query="query" :options="searchOpt" :search="handleSearch"/>-->
    <div class="container">
      <TableCustom
          :columns="columns"
          :tableData="tableData"
          :currentPage="page.index"
          :total="page.total"
          :page-size="page.size"
          :viewFunc="handleView"
          :delFunc="handleDelete"
          :change-page="changePage"
          :editFunc="handleEdit">
<!--        <template #toolbarBtn>-->
<!--          <el-button type="warning" :icon="CirclePlusFilled" @click="visible = true">新增模型</el-button>-->
<!--        </template>-->
      </TableCustom>
    </div>

    <el-dialog
        :title="isEdit ? '编辑模型' : '新增模型'"
        v-model="visible"
        width="700px"
        destroy-on-close
        :close-on-click-modal="false"
        @close="closeDialog">
      <TableEdit
          :form-data="rowData"
          :options="options"
          :edit="isEdit"
          :update="updateData"/>
    </el-dialog>

    <el-dialog title="模型详情" v-model="visible1" width="700px" destroy-on-close>
      <TableDetail :data="viewData"></TableDetail>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="model-management">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { CirclePlusFilled } from '@element-plus/icons-vue';
import { Model } from '@/types/model';
import {
  getModelsApi,
  getModelApi,
  createModelApi,
  updateModelApi,
  deleteModelApi
} from '@/api/model';
import TableCustom from '@/components/table-custom.vue';
import TableDetail from '@/components/table-detail.vue';
import TableSearch from '@/components/table-search.vue';
import { FormOption, FormOptionList } from '@/types/form-option';
import dayjs from 'dayjs';
// 查询相关
const query = reactive({
  name: '',
  version: ''
});
const formatDateTime = (dateTime: string) => {
  console.log('来了')
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};
const searchOpt = ref<FormOptionList[]>([
  { type: 'input', label: '模型名称：', prop: 'name' },
  { type: 'input', label: '版本号：', prop: 'version' }
]);

const handleSearch = () => {
  changePage(1);
};

// 表格相关
const columns = ref([
  { type: 'index', label: '序号', width: 55, align: 'center' },
  { prop: 'name', label: '模型名称' },
  { prop: 'version', label: '版本号' },
  { prop: 'description', label: '描述' },
  { prop: 'created_at', label: '创建时间',formatter: (row: Model) => formatDateTime(row.created_at) },
  { prop: 'operator', label: '操作', width: 250 },
]);

const page = reactive({
  index: 1,
  size: 10,
  total: 0,
});

const tableData = ref<Model[]>([]);

const getData = async () => {
  const params = {
    page: page.index,
    per_page: page.size,
    ...query
  };

  const res = await getModelsApi(params);
  tableData.value = res.data.list;
  page.total = res.data.pageTotal;
};

getData();

const changePage = (val: number) => {
  page.index = val;
  getData();
};

// 新增/编辑弹窗相关
const options = ref<FormOption>({
  labelWidth: '100px',
  span: 12,
  list: [
    { type: 'input', label: '模型名称', prop: 'name', required: true },
    { type: 'input', label: '版本号', prop: 'version', required: true },
    { type: 'textarea', label: '描述', prop: 'description' }
  ]
});

const visible = ref(false);
const isEdit = ref(false);
const rowData = ref({});

const handleEdit = (row: Model) => {
  rowData.value = { ...row };
  isEdit.value = true;
  visible.value = true;
};

const updateData = async (submitData) => {
  try {
    if (isEdit.value) {
      await updateModelApi(submitData.id, submitData);
    } else {
      await createModelApi(submitData);
    }
    closeDialog();
    getData();
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
};

const closeDialog = () => {
  visible.value = false;
  isEdit.value = false;
};

// 查看详情弹窗相关
const visible1 = ref(false);
const viewData = ref({
  row: {},
  list: [
    { prop: 'id', label: '模型ID' },
    { prop: 'name', label: '模型名称' },
    { prop: 'version', label: '版本号' },
    { prop: 'description', label: '描述' },
    { prop: 'created_at', label: '创建时间' },
    { prop: 'updated_at', label: '更新时间' }
  ]
});

const handleView = (row: Model) => {
  viewData.value.row = { ...row };
  visible1.value = true;
};

// 删除相关
const handleDelete = async (row: Model) => {
  try {
    await deleteModelApi(row.id);
    ElMessage.success('删除成功');
    getData();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};
</script>

<style scoped></style>