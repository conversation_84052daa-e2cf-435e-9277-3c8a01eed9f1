<template>
  <div class="layout-header" >
    <span>知识库</span>
    <el-input clearable style="width: 200px;" placeholder="搜索" @input="loadData2" v-model="keyword" :prefix-icon="Search"></el-input>
    <el-button type="primary" @click="dialogVisible = true">+ 资源</el-button>
  </div>
  <el-row :gutter="20" @scroll="onScroll"
          style="max-height: calc(100vh - 100px); overflow-y: scroll">
    <el-col :sm="12" :md="8" :lg="8" v-for="item in dataList" :key="item.id">
      <div class="modelf" @click="goItem(item)">
        <div class="model">
          <img src="@/assets/img/defaults/dataset_text.png" alt=""/>
          <div class="info2">
            <span :title="item.name">{{ item.name }}</span>
            <span
                title="作者：商小烟">{{ item.document_count }}文档·{{
                parseInt(item.word_count / 1000)
              }}千字符·{{ item.app_count }}关联应用</span>
            <span class="info"
                  :title="item.description">
              {{ item.description }}
            </span>
          </div>
        </div>
        <div class="info3" @click.stop>
          <div>
          </div>
          <div>
            <el-dropdown trigger="hover">
              <el-icon class="more">
                <MoreFilled/>
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <!--                  <el-dropdown-item @click.stop="editknowledge(item)">设置</el-dropdown-item>-->
                  <el-dropdown-item @click.stop="delknowledge(item.id)">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <template v-if="dataList.length<=0">
    <el-empty description="暂无数据" />
  </template>
  <!-- 新增资源 -->
  <el-dialog
      title="创建知识库"
      v-model="dialogVisible"
      width="480px"
  >
    <el-form :model="formData" :rules="formRules" ref="formRef" label-position="top">
      <el-form-item label="知识库名称" prop="name">
        <el-input v-model="formData.name" placeholder="输入知识名称"/>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input type="textarea" rows="5" v-model="formData.description" placeholder="输入数据集的简介"/>
      </el-form-item>
      <el-form-item label="导入类型" prop="">
        <div class="typeInfo">已支持 TXT、 MARKDOWN、 MDX、 PDF、 HTML、 XLSX、 XLS、 DOCX、 CSV、 VTT、 PROPERTIES、 MD、
          HTM，每个文件不超过 15MB。
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="" @click="createKnowledge1">
          完成创建
        </el-button>
        <el-button type="primary" @click="createKnowledge2">
          创建并导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {getMyKnowledge, createKnowledgeBase, delKnowledgeBase, dbUseCheck} from '@/api/knowledge'
import $moment from 'moment';
import {ElMessage, ElMessageBox, UploadProps} from "element-plus";
import {useRouter} from "vue-router";
import {Search} from "@element-plus/icons-vue";


const dataList = ref([]);
const dialogVisible = ref(false);
const formData = ref({
  name: '',
  description: '',
  // provider: "external",
  // external_knowledge_id: "66",
  // external_knowledge_api_id: "66"
});

const page = ref(1);
const limit = 20; // 每页条数
const hasMore = ref(true);
const loading = ref(false);

const formRules = {
  name: [
    {required: true, message: '请输入知识库名称', trigger: 'blur'},
    {min: 3, max: 20, message: '名称长度在 3 到 20 个字符之间', trigger: 'blur'}
  ]
};
const loadData = async (reset = false) => {
  if (loading.value) {
    console.log('loading中，阻止重复请求');
    return;
  }
  if (reset) {
    page.value = 1;
    hasMore.value = true;
    dataList.value = [];
  }
  if (!hasMore.value) {
    console.log('没有更多数据了');
    return;
  }

  loading.value = true;
  try {
    console.log('page.value',page.value)
    const res = await getMyKnowledge({
      page: page.value,
      limit,
      include_all: false,
    });
    console.log('加载数据', res);
    if (reset) {
      dataList.value = res.data;
    } else {
      dataList.value = dataList.value.concat(res.data);
    }
    hasMore.value = res.has_more;
    if (hasMore.value) {
      page.value++;
    }
  } catch (error) {
    ElMessage.error("加载失败");
  } finally {
    loading.value = false;
  }
};
const keyword = ref('');
const loadData2 = async (reset = false) => {
  try {
    const res = await getMyKnowledge({
      page: 1,
      limit,
      keyword:keyword.value,
      include_all: false,
    });
    dataList.value = res.data;
    hasMore.value = res.has_more;
    if (hasMore.value) {
      page.value++;
    }
  } catch (error) {
    ElMessage.error("加载失败");
  } finally {
    loading.value = false;
  }
};

const onScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 50) {
    loadData();
  }
};

const delknowledge = async (id) => {
  let res = await dbUseCheck(id);
  let text1 = '删除知识库是不可逆的。用户将无法再访问您的知识库,所有的提示配置和日志将被永久删除。';
  let text2 = '某些应用正在使用该知识库。应用将无法再使用该知识库,所有的提示配置和日志将被永久删除。';
  ElMessageBox.confirm(
      res.isUsing ? text1 : text2,
      '要删除知识库吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(() => {
        delKnowledgeBase(id).then(res => {
          if (res) {
            ElMessage.success('删除成功');
          }
          loadData(true);
        });
      })
      .catch(() => {
      });
};
const router = useRouter();
const createKnowledge1 = () => {
  createKnowledgeBase(formData.value).then(res => {
    ElMessage.success('创建成功');
    dialogVisible.value = false;
    loadData(true);
    router.push('/knowledgeManagementItem?id=' + encodeURIComponent(res.id));
  });
};
const createKnowledge2 = () => {
  createKnowledgeBase(formData.value).then(res => {
    ElMessage.success('创建成功');
    dialogVisible.value = false;
    loadData(true);
    router.push('/knowledgeUpload?id=' + encodeURIComponent(res.id));
  });
};
const goItem = (row) => {
  router.push('/knowledgeManagementItem?id=' + encodeURIComponent(row.id));
};

loadData(true);
</script>

<style scoped lang="less">

.source {
  display: flex;

  .file-img {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    margin-right: 12px;
  }

  > div {
    display: flex;
    flex-direction: column;

    .file-name {
      font-size: 14px;
    }

    .info {
      display: flex;
      align-items: center;

      .el-tag {
        height: 14px;
        margin-right: 5px;
      }

      .tags {
        background-color: rgba(171, 181, 255, 0.3);
        color: rgb(81, 71, 255);
        font-size: 10px;
        padding: 1px 5px;
      }

      font-size: 12px;
      color: rgba(32, 41, 69, 0.62);
    }
  }
}

.avatar-uploader {
  width: 64px;
  height: 64px;
}

.watiUpImg {
  width: 64px;
  height: 64px;
  border-radius: 14px;
}

.types {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  img {
    width: 24px;
    height: 24px;
    margin-bottom: 5px;
  }

  gap: 8px;

  > div {
    display: flex;
    flex: 1;

    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    cursor: pointer;
    text-align: center;
    color: rgba(32, 41, 69, 0.62);

    padding: 10px 16px;
    border: 1px solid rgba(68, 83, 130, 0.25);
    border-radius: 8px;
    font-size: 14px;

    &:hover {
      color: #000;
      background-color: rgba(171, 181, 255, 0.3);
    }
  }

  .active {
    border: 1px solid rgba(81, 71, 255, 1);
  }
}

.typeInfo {
  padding: 6px 8px;
  border: 1px solid rgba(81, 71, 255, 1);
  border-radius: 8px;
  font-size: 12px;
}

:deep(.el-table__row) {
  cursor: pointer !important;
}


.modelf {
  margin-bottom: 20px;
  border-radius: 10px;
  padding: 20px 20px 16px;
  border-color: rgba(82, 100, 154, 0.13);
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  cursor: pointer;
  background-color: #fff;
}

.model {
  display: flex;

  .emoji {
    font-size: 42px;
    border-radius: 14px;
    padding-bottom: 2px;
  }
}

.model img {
  width: 42px;
  height: 42px;
  border-radius: 8px;
}

.model span {
  color: #333333;
  font-size: 14px;
}

.model span:first-child {
  font-size: 18px;
}

.model span:last-child {
  color: rgba(56, 55, 67, .8);
}

.model span:nth-child(2) {
  color: rgba(56, 55, 67, .8);
  margin-bottom: 5px;
}

.modelf:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, .08), 0 8px 24px 0 rgba(0, 0, 0, .04);
  border-color: rgba(82, 100, 154, 0.13);
}

.info {
  height: 37px;
}

.info2 {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
}

.info2 > :last-child {
  font-size: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info2 > :nth-child(2) {
  font-size: 12px;
  color: rgb(103, 111, 131) !important;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  margin-bottom: 10px;
  text-overflow: ellipsis;
}

.info2 > :first-child {
  font-size: 14px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info3 {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgb(240, 240, 245);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: gray;
  font-size: 12px;

  > div {
    display: flex;
    align-items: center;
  }
}

.info3 i {
  margin-top: 3px;
}

.info3 span {
  margin-left: 3px;
}

.info3 > div > :nth-child(2n+1) {
  margin-left: 10px;
}
</style>