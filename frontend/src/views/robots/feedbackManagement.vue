<template>
  <div>
    <TableSearch :query="query" :options="searchOpt" :search="handleSearch"/>
    <div class="container">
      <TableCustom
          :columns="columns"
          :tableData="tableData"
          :currentPage="page.index"
          :total="page.total"
          :page-size="page.size"
          :viewFunc="handleView"
          :delFunc="handleDelete"
          :change-page="changePage">
      </TableCustom>
    </div>

    <el-dialog title="反馈详情" v-model="visible1" width="700px" destroy-on-close>
      <TableDetail :data="viewData"></TableDetail>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="feedback-management">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Feedback } from '@/types/feedback';
import {
  getFeedbackApi,
} from '@/api/feedback';
import TableCustom from '@/components/table-custom.vue';
import TableDetail from '@/components/table-detail.vue';
import TableSearch from '@/components/table-search.vue';
import { FormOptionList } from '@/types/form-option';
import dayjs from 'dayjs';
// 查询相关
const query = reactive({
  conversationId: '',
  minRating: '',
  maxRating: ''
});
const formatDateTime = (dateTime: string) => {
  console.log('来了')
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};
const searchOpt = ref<FormOptionList[]>([
  { type: 'input', label: '对话ID：', prop: 'conversationId' },
  { type: 'input', label: '最低评分：', prop: 'minRating' },
  { type: 'input', label: '最高评分：', prop: 'maxRating' }
]);

const handleSearch = () => {
  changePage(1);
};

// 表格相关
const columns = ref([
  { type: 'index', label: '序号', width: 55, align: 'center' },
  { prop: 'id', label: '反馈ID' },
  { prop: 'conversation_id', label: '对话ID' },
  { prop: 'rating', label: '评分', width: 100 },
  {
    prop: 'feedback',
    label: '反馈内容',
    showOverflowsystem_prompt: true
  },
  { prop: 'created_at', label: '创建时间',formatter: (row: Feedback) => formatDateTime(row.created_at) },
  { prop: 'operator', label: '操作', width: 180 },
]);

const page = reactive({
  index: 1,
  size: 10,
  total: 0,
});

const tableData = ref<Feedback[]>([]);

const getData = async () => {
  // const params = {
  //   page: page.index,
  //   per_page: page.size,
  //   ...query
  // };
  //
  // const res = await getFeedbacksApi(params);
  // tableData.value = res.data.list;
  // page.total = res.data.pageTotal;
};

getData();

const changePage = (val: number) => {
  page.index = val;
  getData();
};

// 查看详情弹窗相关
const visible1 = ref(false);
const viewData = ref({
  row: {},
  list: [
    { prop: 'id', label: '反馈ID' },
    { prop: 'conversation_id', label: '对话ID' },
    { prop: 'rating', label: '评分' },
    { prop: 'feedback', label: '反馈内容' },
    { prop: 'created_at', label: '创建时间' },
    { prop: 'updated_at', label: '更新时间' }
  ]
});

const handleView = (row: Feedback) => {
  viewData.value.row = { ...row };
  visible1.value = true;
};

// 删除相关
const handleDelete = async (row: Feedback) => {
  // try {
  //   await deleteFeedbackApi(row.conversation_id, row.id);
  //   ElMessage.success('删除成功');
  //   getData();
  // } catch (error) {
  //   ElMessage.error('删除失败');
  // }
};
</script>

<style scoped></style>