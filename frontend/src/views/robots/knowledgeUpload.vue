<template>
  <div class="layout-header">
    <div class="info">
      <el-icon @click="goItems" class="backIcon">
        <ArrowLeft/>
      </el-icon>
      <span>新增内容</span>
    </div>
    <el-steps style="width: 700px;margin-right: 300px" :active="active" finish-status="success" simple>
      <el-step title="上传"/>
      <el-step title="文本分段与清洗"/>
      <el-step title="处理并完成"/>
    </el-steps>
  </div>
  <div class="main">

    <div v-show="active === 0" class="" style="width: 600px;margin-left: 64px;">
      <el-upload
          class="upload-demo"
          drag
          :action="uploadUrl"
          :headers="uploadHeaders"
          multiple
          :limit="300"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-error="handleError"
          :file-list="fileList"
          :auto-upload="true"
          accept=".txt,.md,.mdx,.markdown,.html,.htm,.properties,.vtt,.pdf,.docx,.xlsx,.xls,.csv"
          :on-remove="handleRemove"
      >
        <el-icon class="el-icon--upload">
          <upload-filled/>
        </el-icon>
        <div class="el-upload__text">单击或将文件拖到此处上传</div>
        <div class="el-upload__tip">
          支持文件格式：TXT、MARKDOWN、MDX、MD、HTML、HTM、PROPERTIES、VTT、PDF、DOCX、XLSX、XLS、CSV，单文件最大 15MB
        </div>
      </el-upload>
      <el-button class="float-right" type="primary" @click="goStep(1)" :disabled="fileList.length<=0">
        下一步
        <el-icon>
          <arrow-right/>
        </el-icon>
      </el-button>
    </div>
    <div v-show="active === 1" class="main2">
      <div class="settingmain">
        <div class="knowledge-settings">
          <div class="section-title">分段设置</div>
          <div class="blockDiv" :class="{'active':chunkSetting.doc_form==='text_model'}" v-if="!nowDatabase.indexing_technique || chunkSetting.doc_form==='text_model'">
            <div class="flex sectionHead items-center" @click="chunkSetting.doc_form = 'text_model'">
              <div>
                <img src="@/assets/img/svg/setting-gear-mod.svg" alt="" class="settingSvg"/>
              </div>
              <div>
                <div class="section-title font-14">通用</div>
                <div class="titInfo font-12">通用文本分块模式，检索和召回的块是相同的</div>
              </div>
            </div>
            <el-card shadow="never" class="section-card" v-if="chunkSetting.doc_form==='text_model'">
              <el-row :gutter="16" class="mb-4 font-13">
                <el-col :span="8">
                  <el-form-item label="分段标识符" label-position="top">
                    <el-input v-model="process_rule.rules.segmentation.delimiter"
                              placeholder="\n\n 用于分段；\n用于分行"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="分段最大长度" label-position="top">
                    <el-input-number v-model="process_rule.rules.segmentation.max_tokens" :min="50"
                                     :max="process_rule.limits.indexing_max_segmentation_tokens_length"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="分段重叠长度" label-position="top">
                    <el-input-number v-model="process_rule.rules.segmentation.chunk_overlap" :min="1"
                                     :max="process_rule.rules.segmentation.max_tokens-1"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="section-title font-13">文本预处理规则</div>
              <el-checkbox-group v-model="selectedRules">
                <el-checkbox label="remove_extra_spaces">替换掉连续的空格、换行符和制表符</el-checkbox>
                <br/>
                <el-checkbox label="remove_urls_emails">删除所有 URL 和电子邮件地址</el-checkbox>
              </el-checkbox-group>

              <div class="btn-group" style="margin-top: 12px;">
                <el-button @click="previewChunks" type="primary">
                  <el-icon>
                    <Search/>
                  </el-icon>
                  预览块
                </el-button>
                <el-button @click="resetChunkSettings">重置</el-button>
              </div>
            </el-card>
          </div>
          <div class="blockDiv mt-2" :class="{'active':chunkSetting.doc_form==='hierarchical_model'}" v-if="!nowDatabase.indexing_technique || chunkSetting.doc_form==='hierarchical_model'">
            <div class="flex sectionHead items-center" @click="chunkSetting.doc_form = 'hierarchical_model'">
              <div>
                <img src="@/assets/img/svg/fs.svg" alt="" class="settingSvg w-20"/>
              </div>
              <div>
                <div class="section-title font-14">父子分段</div>
                <div class="titInfo font-12">使用父子模式时，子块用于检索，父块用作上下文</div>
              </div>
            </div>
            <el-card shadow="never" class="section-card" v-if="chunkSetting.doc_form==='hierarchical_model'">
              <div class="font-13">父块用作上下文</div>
              <div class="allselect allselect2" :class="{'active':process_rule.rules.parent_mode==='paragraph'}"
                   @click="setParentModel('paragraph')">
                <div>
                  <img src="@/assets/img/svg/note-mod.334e50fd.svg" alt=""/>
                  <div>
                    <div class="font-13">段落</div>
                    <div>此模式根据分隔符和最大块长度将文本拆分为段落，使用拆分文本作为检索的父块</div>
                  </div>
                </div>
                <el-row class="font-13 mt-2" v-if="process_rule.rules.parent_mode==='paragraph'">
                  <el-col :span="12">
                    <el-form-item label="分段标识符" label-position="top">
                      <el-input v-model="process_rule.rules.segmentation.delimiter"
                                placeholder="\n\n 用于分段；\n用于分行"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="分段最大长度" label-position="top">
                      <el-input-number v-model="process_rule.rules.segmentation.max_tokens" :min="50"
                                       :max="process_rule.limits.indexing_max_segmentation_tokens_length"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="allselect" :class="{'active':process_rule.rules.parent_mode==='full-doc'}"
                   @click="setParentModel('full-doc')">
                <img src="@/assets/img/svg/file-list-3-fill.57beb31b.svg" alt=""/>
                <div>
                  <div>全文</div>
                  <div>整个文档用作父块并直接检索。请注意，出于性能原因，超过10000个标记的文本将被自动截断。</div>
                </div>
              </div>
              <div class="font-13 mt-2">子块用于检索</div>
              <el-row :gutter="16" class="mb-4 font-13">
                <el-col :span="12">
                  <el-form-item label="分段标识符" label-position="top">
                    <el-input v-model="process_rule.rules.subchunk_segmentation.delimiter"
                              placeholder="\n\n 用于分段；\n用于分行"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分段最大长度" label-position="top">
                    <el-input-number v-model="process_rule.rules.subchunk_segmentation.max_tokens" :min="50"
                                     :max="process_rule.limits.indexing_max_segmentation_tokens_length"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="section-title font-13">文本预处理规则</div>
              <el-checkbox-group v-model="selectedRules">
                <el-checkbox label="remove_extra_spaces">替换掉连续的空格、换行符和制表符</el-checkbox>
                <br/>
                <el-checkbox label="remove_urls_emails">删除所有 URL 和电子邮件地址</el-checkbox>
              </el-checkbox-group>

              <div class="btn-group" style="margin-top: 12px;">
                <el-button @click="previewChunks" type="primary">
                  <el-icon>
                    <Search/>
                  </el-icon>
                  预览块
                </el-button>
                <el-button @click="resetChunkSettings">重置</el-button>
              </div>
            </el-card>
          </div>
          <div class="section-title font-14 mt-2">索引方式</div>
          <div class="index-mode-wrapper">
            <div
                v-if="!nowDatabase.indexing_technique || nowDatabase.indexing_technique === 'high_quality'"
                class="index-option"
                :class="{ active: chunkSetting.indexing_technique === 'high_quality' }"
                @click="chunkSetting.indexing_technique = 'high_quality'"
            >
              <div class="icon high"><img src="@/assets/img/piggy.svg" alt=""/></div>
              <div class="info2">
                <div class="title">
                  高质量
                  <el-tag type="success" size="small" style="margin-left: 6px;">推荐</el-tag>
                </div>
                <div class="desc">调用嵌入模型处理文档以实现更精确的检索，可以帮助LLM生成高质量的答案。</div>
              </div>
            </div>
            <div
                v-if="!nowDatabase.indexing_technique || nowDatabase.indexing_technique === 'economy'"
                class="index-option"
                :class="{ active: chunkSetting.indexing_technique === 'economy' }"
                @click="chunkSetting.indexing_technique = 'economy'"
            >
              <div class="icon eco"><img src="@/assets/img/gold.svg" alt=""/></div>
              <div class="info2">
                <div class="title">经济</div>
                <div class="desc">每个数据块使用前10个关键词进行检索，不会消耗任何tokens，但会以降低检索准确性为代价。
                </div>
              </div>
            </div>
            <div
                v-if="nowDatabase.indexing_technique"
                class="option-mask"
            ></div>
          </div>
          <el-alert
              type="warning"
              :closable="false"
              show-icon
              style="margin-top: 12px;font-size: 12px;border-radius: 10px"
              description="使用高质量模式进行嵌入后，无法切换回经济模式。"
          />
          <div class="section-title mb-2 mt-1">Embedding 模型</div>
          <div class="embedding-model">
            <el-select
                v-model="nowEmbeddingModel"
                placeholder="Select"
                style="width: 100%"
            >
              <el-option
                  v-for="item in embeddings[0]?.models"
                  :key="item.model"
                  :label="item.model"
                  :value="item.model"
              />
            </el-select>
          </div>
          <div class="section-title">检索设置</div>
          <div class="blockDiv mt-2" :class="{'active':activeCollapse === 'semantic_search'}">
            <div
                v-if="nowDatabase.indexing_technique"
                class="option-mask"
            ></div>
            <div class="flex sectionHead items-center" @click="activeCollapse = 'semantic_search'">
              <div>
                <img src="@/assets/img/svg/selection-mod.e28687c9.svg" alt="" class="settingSvg"/>
              </div>
              <div>
                <div class="section-title font-14">向量检索</div>
                <div class="titInfo font-12">通过生成查询嵌入并查询与其向量表示最相似的文本分段</div>
              </div>
            </div>
            <el-card shadow="never" class="section-card" v-if="activeCollapse === 'semantic_search'">
              <el-switch v-model="rerankEnabled" active-text="Rerank 模型" :active-value="true"
                         :inactive-value="false"/>
              <el-select v-model="nowRank" placeholder="选择模型" :disabled="canSetRank" v-if="rerankEnabled"
                         class="model-select">
                <el-option v-for="item in reranks" :label="item.model" :value="item.model"/>
              </el-select>
              <div class="flex items-center space-x-4">
                <el-form-item label="Top K" label-position="top">
                  <div class="flex">
                    <el-input-number v-model="topK" :min="1" :max="10" size="small" :disabled="canSetRank"/>
                    <el-slider v-model="topK" :min="1" :max="10" style="width: 100px" :disabled="canSetRank"/>
                  </div>
                </el-form-item>
                <el-form-item label-position="top">
                  <template #label>
                    <el-switch v-model="scoreEnabled" :disabled="canSetRank" size="small" :active-value="true"
                               :inactive-value="false"></el-switch>
                    Score 闭值
                  </template>
                  <div class="flex">
                    <el-input-number
                        v-model="score"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        size="small"
                        :disabled="canSetRank"
                    />
                    <el-slider
                        v-model="score"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        style="width: 100px"
                        :disabled="canSetRank"
                    />
                  </div>
                </el-form-item>
              </div>
            </el-card>
          </div>
          <div class="blockDiv mt-2" :class="{'active':activeCollapse === 'full_text_search'}">
            <div
                v-if="nowDatabase.indexing_technique"
                class="option-mask"
            ></div>
            <div class="flex sectionHead items-center" @click="activeCollapse = 'full_text_search'">
              <div>
                <img src="@/assets/img/svg/research-mod.286ce029.svg" alt="" class="settingSvg"/>
              </div>
              <div>
                <div class="section-title font-14">全文检索</div>
                <div class="titInfo font-12">
                  索引文档中的所有词汇，从而允许用户查询任意词汇，并返回包含这些词汇的文本片段
                </div>
              </div>
            </div>
            <el-card shadow="never" class="section-card" v-if="activeCollapse === 'full_text_search'">
              <el-switch v-model="rerankEnabled" active-text="Rerank 模型" :active-value="true"
                         :inactive-value="false"/>
              <el-select v-model="nowRank" placeholder="选择模型" :disabled="canSetRank" v-if="rerankEnabled"
                         class="model-select">
                <el-option v-for="item in reranks" :label="item.model" :value="item.model"/>
              </el-select>
              <div class="flex items-center space-x-4">
                <el-form-item label="Top K" label-position="top">
                  <div class="flex">
                    <el-input-number v-model="topK" :min="1" :max="10" size="small" :disabled="canSetRank"/>
                    <el-slider v-model="topK" :min="1" :max="10" style="width: 100px" :disabled="canSetRank"/>
                  </div>
                </el-form-item>
                <el-form-item label-position="top">
                  <template #label>
                    <el-switch v-model="scoreEnabled" :disabled="canSetRank" size="small" :active-value="true"
                               :inactive-value="false"></el-switch>
                    Score 闭值
                  </template>
                  <div class="flex">
                    <el-input-number
                        v-model="score"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        size="small"
                        :disabled="canSetRank"
                    />
                    <el-slider
                        v-model="score"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        style="width: 100px"
                        :disabled="canSetRank"
                    />
                  </div>
                </el-form-item>
              </div>
            </el-card>
          </div>
          <div class="blockDiv mt-2" :class="{'active':activeCollapse === 'hybrid_search'}">
            <div
                v-if="nowDatabase.indexing_technique"
                class="option-mask"
            ></div>
            <div class="flex sectionHead items-center" @click="activeCollapse = 'hybrid_search'">
              <div>
                <img src="@/assets/img/svg/hunhe.svg" alt="" class="settingSvg"/>
              </div>
              <div>
                <div class="section-title font-14">混合检索</div>
                <div class="titInfo font-12">
                  同时执行全文检索和向量检索，并应用重排序步骤，从两类查询结果中选择匹配用户问题的最佳结果，用户可以选择设置权重或配置重新排序模型。
                </div>
              </div>
            </div>
            <el-card shadow="never" class="section-card" v-if="activeCollapse === 'hybrid_search'">
              <div class="index-mode-wrapper">
                <div
                    class="index-option"
                    :class="{ active: reranking_mode === 'weighted_score' }"
                    @click="reranking_mode = 'weighted_score'"
                >
                  <div class="icon high"><img src="@/assets/img/piggy.svg" alt=""/></div>
                  <div class="info2">
                    <div class="title">
                      权重设置
                    </div>
                    <div class="desc">通过调整分配的权重，重新排序策略确定是优先进行语义匹配还是关键字匹配。</div>
                  </div>
                </div>
                <div
                    class="index-option"
                    :class="{ active: reranking_mode === 'reranking_model' }"
                    @click="reranking_mode = 'reranking_model'"
                >
                  <div class="icon eco"><img src="@/assets/img/gold.svg" alt=""/></div>
                  <div class="info2">
                    <div class="title">Remark 模型</div>
                    <div class="desc">重排序模型将根据候选文档列表与用户问题语义匹配度进行重新排序，从而改进语义排序的结果
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="reranking_mode ==='weighted_score'" style="display: flex; align-items: center; width: 100%;"
                   class="mt-1 mb-1">
                <span style="color: #007AFF; margin-right: 8px;">语义 {{ nowKeywordWeight }}</span>
                <el-slider
                    v-model="nowKeywordWeight"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    show-tooltip="false"
                    style="flex: 1;"
                />
                <span style="color: #008000; margin-left: 8px;">{{
                    parseFloat(1 - nowKeywordWeight).toFixed(2)
                  }} 关键词</span>
              </div>
              <el-select v-if="reranking_mode ==='reranking_model'" v-model="nowRank" placeholder="选择模型"
                         :disabled="canSetRank"
                         class="model-select" style="margin: 20px 0 10px;">
                <el-option v-for="item in reranks" :label="item.model" :value="item.model"/>
              </el-select>
              <div class="flex items-center space-x-4">
                <el-form-item label="Top K" label-position="top">
                  <div class="flex">
                    <el-input-number v-model="topK" :min="1" :max="10" size="small" :disabled="canSetRank"/>
                    <el-slider v-model="topK" :min="1" :max="10" style="width: 100px" :disabled="canSetRank"/>
                  </div>
                </el-form-item>
                <el-form-item label-position="top">
                  <template #label>
                    <el-switch v-model="scoreEnabled" :disabled="canSetRank" size="small" :active-value="true"
                               :inactive-value="false"></el-switch>
                    Score 闭值
                  </template>
                  <div class="flex">
                    <el-input-number
                        v-model="score"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        size="small"
                        :disabled="canSetRank"
                    />
                    <el-slider
                        v-model="score"
                        :min="0"
                        :max="1"
                        :step="0.01"
                        style="width: 100px"
                        :disabled="canSetRank"
                    />
                  </div>
                </el-form-item>
              </div>
            </el-card>
          </div>
          <div class="nextBtn">
            <el-button @click="goStep(0)">
              <el-icon>
                <ArrowLeft/>
              </el-icon>
              上一步
            </el-button>
            <el-button type="primary" @click="goChunk">保存并处理</el-button>
          </div>
        </div>
        <div class="preview mr-2">
          <div class="preview-header">
            预览
            <el-select v-model="currentDocId" style="width: 30%;" @change="previewChunks">
              <el-option v-for="item in fileList" :label="item?.response?.name" :value="item?.response?.id"></el-option>
            </el-select>
            <el-tag class="chunk-count" size="small">{{ chunks.total_segments || 0 }} 预估块</el-tag>
          </div>
          <div
              v-for="(chunk, idx) in chunks.preview"
              :key="idx"
              class="chunk-box"
          >
            <div class="chunk-title">Chunk-{{ idx + 1 }} · {{ chunk.content.length }} characters</div>
            <div class="chunk-text" v-html="formatContent(chunk.content)"></div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="active === 2" class="upload-complete-container">
      <div class="left-panel">
        <div class="header">
          <span class="emoji">🎉</span>
          <span class="title">文档已上传</span>
        </div>
        <div class="description">
          文档已上传至知识库：
          <strong>{{ knowLedgeInfo.name }}</strong> ，你可以在知识库的文档列表中找到它。
        </div>

        <div class="embedding-status">{{ embeddingStatusText }}</div>

        <el-divider></el-divider>

        <div class="file-list">
          <div
              v-for="(task, idx) in embeddingTasks"
              :key="task.id"
              class="file-item"
          >
            <el-icon class="file-icon" style="color: #2f9e44;">
              <i class="el-icon-document"></i>
            </el-icon>
            <div class="file-info">
              <p class="file-name">
                {{ documentsInfo[idx].name || `文档 ${idx + 1}` }}
                <span v-if="task.indexing_status === 'error'" class="error-icon">
                <el-tooltip effect="dark" :content="task.error" placement="top">
                  ❌
                </el-tooltip>
              </span>
              </p>
              <el-progress
                  :percentage="getProgress(task)"
                  :status="
                task.indexing_status === 'completed'
                  ? 'success'
                  : task.indexing_status === 'error'
                  ? 'exception'
                  : 'active'
              "
                  style="width: 100%; max-width: 400px;"
                  stroke-width="16"
              />
            </div>
          </div>
        </div>

        <el-button class="nextBtn" type="primary" @click="goItems">
          前往文档 →
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useRoute} from 'vue-router';
import {ElMessage, UploadProps} from "element-plus";
import {ref, computed, watch, reactive, onMounted} from "vue";
import {ArrowLeft, ArrowRight, Document, Notebook, Search} from "@element-plus/icons-vue";
import router from "@/router";
import {
  getProcessRules,
  getReranks,
  getIndexingEstimate,
  getDefaultModel,
  indexingStatus,
  getKnowledgeBase,
  postKnowledgeBaseDocument,
  textEmbedding,
  getKnowledgeBaseDocument
} from '@/api/knowledge';

const route = useRoute();
const uploadUrl = `${import.meta.env.VITE_SERVE_HOST}/console/api/files/upload?source=datasets`;

const allowedExtensions = ['txt', 'markdown', 'mdx', 'md', 'html', 'htm', 'properties', 'vtt', 'pdf', 'docx', 'xlsx', 'xls', 'csv']

const getUploadHeaders = () => {
  const token = localStorage.getItem('console_token');
  if (!token) {
    ElMessage.error('未找到有效的 token，请重新登录');
    return null;
  }
  return {
    'Authorization': `Bearer ${token}`,
    // 'X-Requested-With': 'XMLHttpRequest',
    // 'Accept': 'application/json'
  };
};
const uploadHeaders = ref(getUploadHeaders());
const fileList = ref([])

function beforeUpload(file: File) {
  const ext = file.name.split('.').pop()?.toLowerCase() || ''
  if (!allowedExtensions.includes(ext)) {
    ElMessage.error(`不支持的文件格式：${ext}`)
    return false
  }
  const isLt15M = file.size / 1024 / 1024 < 15
  if (!isLt15M) {
    ElMessage.error('文件大小不能超过 15MB!')
    return false
  }
  return true
}

function handleSuccess(response, file, fileList_) {
  ElMessage.success(`文件 ${file.name} 上传成功`)
  fileList.value = fileList_;
  console.log('fileList.value', fileList.value)
  currentDocId.value = fileList.value[0].response.id;
}

function handleError(err, file, fileList_) {
  ElMessage.error(`文件 ${file.name} 上传失败`)
  fileList.value = fileList_
}

function handleRemove(file, fileList_) {
  fileList.value = fileList_
}

const active = ref(0);
const chunkSetting = ref({});
const baseChunkSetting = ref({});
const reranks = ref([]);
const embeddings = ref([]);
const nowRank = ref('');
const rerankEnabled = ref(false);
const reranking_mode = ref('quanzhong')
const process_rule = ref({
  rules: {
    parent_mode: 'full-doc',
    segmentation: {
      delimiter: '',
      max_tokens: 4000,
      chunk_overlap: 0
    },
    pre_processing_rules: [
      {
        id: "remove_extra_spaces",
        enabled: true
      },
      {
        id: "remove_urls_emails",
        enabled: false
      }
    ],
    subchunk_segmentation: {       // 子块 / hierarchical 模式
      delimiter: '\n\n',
      max_tokens: 100,
    },
  },
  limits: {
    indexing_max_segmentation_tokens_length: 4000
  },
  mode: 'custom'
})
const goStep = async (num) => {
  active.value = num;
  if (num === 1) {
    let res = await getProcessRules();
    console.log('ressss', res)
    res.rules.segmentation.delimiter = res.rules.segmentation.delimiter.replace(/\n/g, '\\n\\n');
    process_rule.value = JSON.parse(JSON.stringify(res));
    process_rule.value.rules.parent_mode = 'full-doc';
    process_rule.value.rules.subchunk_segmentation = {
      delimiter: '\\n',
      max_tokens: 512,
    };
    console.log('process_rule', process_rule.value)
    let res2 = await getReranks();
    console.log('reranks.value', res2)
    reranks.value = res2.data[0].models;
    let res3 = await getDefaultModel();
    nowRank.value = res3.data.model;
    console.log('nowRank.value', nowRank.value)
    let res4 = await textEmbedding(route.query.id);
    embeddings.value = res4.data;
    nowEmbeddingModel.value = res4.data[0].models[0].model;
  }
};
const resetChunkSettings = () => {
  chunkSetting.value = JSON.parse(JSON.stringify(baseChunkSetting.value));
};
const chunks = ref([]);
//预览切片
const previewChunks = async () => {
  console.log('fileList.value', fileList.value);
  let tempChunkSetting = JSON.parse(JSON.stringify(chunkSetting.value));
  const idSet = new Set(selectedRules.value);
  process_rule.value.rules.pre_processing_rules.forEach(rule => {
    rule.enabled = idSet.has(rule.id);
  });
  tempChunkSetting.process_rule = JSON.parse(JSON.stringify(process_rule.value));
  console.log('tempChunkSetting.process_rule', tempChunkSetting.process_rule)
  tempChunkSetting.process_rule.rules.segmentation.separator = JSON.parse(JSON.stringify(process_rule.value.rules.segmentation.delimiter.replace(/\\n/g, '\n')));
  delete tempChunkSetting.process_rule.rules.segmentation.delimiter;
  delete tempChunkSetting.process_rule.limits;
  delete tempChunkSetting.process_rule.rules.segmentation.chunk_overlap;
  if (tempChunkSetting.doc_form !== 'hierarchical_model') {
    delete tempChunkSetting.process_rule.rules.parent_mode;
    delete tempChunkSetting.process_rule.rules.subchunk_segmentation;
  } else {
    tempChunkSetting.process_rule.rules.subchunk_segmentation.separator = JSON.parse(JSON.stringify(process_rule.value.rules.subchunk_segmentation.delimiter.replace(/\\n/g, '\n')));
  }

  tempChunkSetting.info_list = {
    data_source_type: "upload_file",
    file_info_list: {
      file_ids: [currentDocId.value]
    }
  };
  console.log('preChunkSetting', tempChunkSetting)
  try {
    let res = await getIndexingEstimate(tempChunkSetting);
    chunks.value = res; // 视接口返回数据结构调整
    console.log('索引估算结果:', res);
  } catch (error) {
    console.error('索引估算失败:', error);
  }
}

function formatContent(text) {
  if (!text) return ''
  return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n/g, '<br/>')
}

const activeCollapse = ref('semantic_search');

const currentDocId = ref('');
const documentsInfo = ref({});
const knowLedgeInfo = ref({});
const loading = ref({});
const nowEmbeddingModel = ref({});
const embeddingTasks = ref([])
const currentTaskIndex = ref(0)
const batchId = ref('')
const goChunk = async () => {
  active.value = 2;
  let tempChunkSetting = JSON.parse(JSON.stringify(chunkSetting.value));
  // process_rule.value.rules.pre_processing_rules.forEach(rule => {
  //   rule.enabled = idSet.has(rule.id);
  // });
  tempChunkSetting.process_rule = JSON.parse(JSON.stringify(process_rule.value));
  console.log('tempChunkSetting.process_rule', tempChunkSetting.process_rule)
  tempChunkSetting.process_rule.rules.segmentation.separator = JSON.parse(JSON.stringify(process_rule.value.rules.segmentation.delimiter.replace(/\\n/g, '\n')));
  delete tempChunkSetting.process_rule.rules.segmentation.delimiter;
  delete tempChunkSetting.process_rule.limits;
  delete tempChunkSetting.process_rule.rules.segmentation.chunk_overlap;
  if (tempChunkSetting.doc_form !== 'hierarchical_model') {
    delete tempChunkSetting.process_rule.rules.parent_mode;
    delete tempChunkSetting.process_rule.rules.subchunk_segmentation;
  } else {
    tempChunkSetting.process_rule.rules.subchunk_segmentation.separator = JSON.parse(JSON.stringify(process_rule.value.rules.subchunk_segmentation.delimiter.replace(/\\n/g, '\n')));
  }
  const file_ids = fileList.value
      .filter(file => file.response && file.response.id)
      .map(file => file.response.id);
  tempChunkSetting.info_list = {
    data_source_type: "upload_file",
    file_info_list: {
      file_ids: file_ids
    }
  };

  let retrieval_model = {};
  if (activeCollapse.value === 'semantic_search') {
    retrieval_model = {
      reranking_enable: rerankEnabled.value,
      reranking_mode: nowRank.value,
      reranking_model: {
        reranking_model_name: nowRank.value,
        reranking_provider_name: embeddings.value[0].provider
      },
      score_threshold: score.value,
      score_threshold_enabled: rerankEnabled.value,
      search_method: activeCollapse.value,
      top_k: topK.value,
      weights: null
    }
  } else if (activeCollapse.value === 'semantic_search') {
    retrieval_model = {
      reranking_enable: rerankEnabled.value,
      reranking_mode: nowRank.value,
      reranking_model: {
        reranking_model_name: nowRank.value,
        reranking_provider_name: embeddings.value[0].provider
      },
      score_threshold: score.value,
      score_threshold_enabled: rerankEnabled.value,
      search_method: activeCollapse.value,
      top_k: topK.value,
      weights: null
    }
  } else {
    if (reranking_mode.value === 'quanzhong') {
      retrieval_model = {
        reranking_enable: rerankEnabled.value,
        reranking_mode: nowRank.value,
        reranking_model: {
          reranking_model_name: nowRank.value,
          reranking_provider_name: embeddings.value[0].provider
        },
        score_threshold: score.value,
        score_threshold_enabled: rerankEnabled.value,
        search_method: activeCollapse.value,
        top_k: topK.value,
        weights: {
          keyword_setting: {keyword_weight: 1 - nowKeywordWeight.value},
          vector_setting: {
            embedding_model_name: '',
            embedding_provider_name: '',
            vector_weight: nowKeywordWeight.value,
          },
          weight_type: 'customized'
        }
      }
    } else {
      retrieval_model = {
        reranking_enable: rerankEnabled.value,
        reranking_mode: nowRank.value,
        reranking_model: {
          reranking_model_name: nowRank.value,
          reranking_provider_name: embeddings.value[0].provider
        },
        score_threshold: score.value,
        score_threshold_enabled: rerankEnabled.value,
        search_method: activeCollapse.value,
        top_k: topK.value,
        weights: {
          keyword_setting: {keyword_weight: 1 - nowKeywordWeight.value},
          vector_setting: {
            embedding_model_name: '',
            embedding_provider_name: '',
            vector_weight: nowKeywordWeight.value,
          },
          weight_type: 'customized'
        }
      }
    }
  }

  let res = await postKnowledgeBaseDocument({
    data_source: {
      info_list: {
        data_source_type: 'upload_file',
        file_info_list: {file_ids}
      },
      type: 'upload_file'
    },
    doc_form: tempChunkSetting.doc_form,
    doc_language: tempChunkSetting.doc_language,
    embedding_model: nowEmbeddingModel.value,
    embedding_model_provider: embeddings.value[0].provider,
    indexing_technique: chunkSetting.value.indexing_technique,
    process_rule: {
      mode: tempChunkSetting.process_rule.mode,
      rules: tempChunkSetting.process_rule.rules,
    },
    retrieval_model: retrieval_model
  }, route.query.id)

  documentsInfo.value = res.documents
  batchId.value = res.batch
  let res2 = await getKnowledgeBase(route.query.id)
  knowLedgeInfo.value = res2
  loading.value = true
  const res3 = await indexingStatus(route.query.id, batchId.value)
  embeddingTasks.value = res3.data || []

  await pollAllTasks()
}
// 轮询当前任务直到完成
// 轮询计时器
let pollTimer = null
const pollCurrentTask = async () => {
  if (currentTaskIndex.value >= embeddingTasks.value.length) {
    ElMessage.success('所有任务已完成！')
    loading.value = false
    return
  }

  const task = embeddingTasks.value[currentTaskIndex.value]
  if (task.indexing_status === 'completed') {
    currentTaskIndex.value++
    await pollCurrentTask()
    return
  }

  // 每2秒轮询更新当前任务
  pollTimer = setInterval(async () => {
    const res = await indexingStatus(route.query.id, batchId.value)
    embeddingTasks.value = res.data || []
    const current = embeddingTasks.value[currentTaskIndex.value]

    if (current.indexing_status === 'completed') {
      clearInterval(pollTimer)
      currentTaskIndex.value++
      await pollCurrentTask()
    }
  }, 2000)
}

const goItems = () => {
  router.push('/knowledgeManagementItem?id=' + route.query.id);
}

const getProgress = (task) => {
  if (!task || !task.total_segments) return 0;
  return Math.floor((task.completed_segments / task.total_segments) * 100);
}

// 替换原有 pollCurrentTask 方法为轮询所有任务
const pollAllTasks = async () => {
  pollTimer = setInterval(async () => {
    const res = await indexingStatus(route.query.id, batchId.value)
    embeddingTasks.value = res.data || []

    // 检查是否有任何任务出错
    const hasError = embeddingTasks.value.some(task => task.indexing_status === 'error')
    if (hasError) {
      clearInterval(pollTimer)
      // 标记出错文件
      for (const task of embeddingTasks.value) {
        if (task.indexing_status === 'error') {
          const failedDoc = fileList.value.find(file => file.response?.id === task.id)
          if (failedDoc) {
            failedDoc.display_status = 'error'
            failedDoc.error_reason = task.error || '解析失败'
          }
        }
      }

      loading.value = false
      return
    }

    // 检查是否全部完成
    const allCompleted = embeddingTasks.value.every(task => task.indexing_status === 'completed')
    if (allCompleted) {
      clearInterval(pollTimer)
      loading.value = false
    }
  }, 2000)
}


const embeddingStatusText = computed(() => {
  if (embeddingTasks.value.length === 0) return '暂无嵌入任务';

  const allFinished = embeddingTasks.value.every(task =>
      ['completed', 'error'].includes(task.indexing_status)
  );

  return allFinished ? '🎉 嵌入已完成' : '🚀 嵌入处理中...';
});

const selectedRules = ref<string[]>([]);

const topK = ref(2);
const score = ref(0.5);
const scoreEnabled = ref(false);

const nowDatabase = ref({});
const canSetRank = ref(false);
const external_retrieval_model = ref(null);

const nowKeywordWeight = ref(0.3);
const retrieval_model_dict = ref({});
const setParentModel = (type) => {
  process_rule.value.rules.parent_mode = type;
}
onMounted(async () => {
  nowDatabase.value = await getKnowledgeBase(route.query.id);
  chunkSetting.value = {
    dataset_id: nowDatabase.value.id,
    doc_form: nowDatabase.value.doc_form || 'text_model',
    doc_language: 'Chinese Simplified',
    indexing_technique: nowDatabase.value.indexing_technique || 'high_quality',
  };
  activeCollapse.value = nowDatabase.value.retrieval_model_dict.search_method;
  reranking_mode.value = nowDatabase.value.retrieval_model_dict.reranking_mode;
  external_retrieval_model.value = nowDatabase.value.external_retrieval_model;

  retrieval_model_dict.value = nowDatabase.value.retrieval_model_dict;
  canSetRank.value = !(external_retrieval_model.value.score_threshold_enabled === null);
  nowKeywordWeight.value = nowDatabase.value?.retrieval_model_dict?.weights?.keyword_setting?.keyword_weight || 0.5;
  selectedRules.value = process_rule.value.rules.pre_processing_rules
      .filter(rule => rule.enabled)
      .map(rule => rule.id);
});
</script>

<style scoped lang="less">
.info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  span {
    font-size: 16px;
    margin-left: 10px;
  }
}

.main3 {

  border: 1px solid rgba(82, 100, 154, 0.13);
  border-radius: 8px;
  display: flex;
  padding: 0;
  height: 70vh;

  .left {
    width: 300px;
    padding: 12px;
    overflow-y: auto;
    border-right: .5px solid rgba(82, 100, 154, 0.13);

    .input-with-select {
      border-radius: 8px;
    }

    .tit {
      margin: 16px 0 4px;
      font-size: 12px;
      color: rgba(32, 41, 69, 0.62);
      line-height: 1rem;
    }

    .documents {
      color: rgb(56, 55, 67);
      flex: 1 1 auto;
      min-height: 0;

      .item {
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 6px .5rem;
        cursor: pointer;

        span {
          margin-left: 8px;
          font-size: 14px;
        }

        &:hover {
          background-color: rgba(87, 104, 161, 0.08);
        }
      }

      .active {
        background-color: rgba(87, 104, 161, 0.08);
      }
    }
  }

  .right {
    width: 100%;

    .top {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      color: rgb(56, 55, 67);
      border-bottom: 1px solid rgba(82, 100, 154, 0.13);

      span {
        margin-left: 10px;
        font-size: 14px;
      }
    }

    .chunkDiv {
      max-height: calc(100% - 100px);
      min-height: 55vh;
      overflow-y: scroll;

      .chunks {
        height: calc(100% - 130px);
        overflow-y: auto;
        padding: 16px 16px 0;

        .item {
          word-break: break-word;
          margin-bottom: 8px;
          padding: 8px;
          border: 1px solid rgba(82, 100, 154, 0.13);
          border-radius: 8px;
          background-color: rgba(87, 104, 161, 0.08);
          color: rgba(15, 21, 40, 0.82);
          font-size: 14px;
        }
      }
    }

  }

}

.upload-demo {
  margin-top: 32px;
}

.main {
  position: relative;
  height: calc(100vh - 78px);
}

.nextBtn {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.main2 {
  font-size: 14px;
  height: calc(100vh - 78px);

  .tit {
    line-height: 60px;
  }

  .items {
    background-color: rgba(255, 255, 255, 1);
    padding: 11.5px;
    border-radius: 8px;
    border: 1.5px solid rgba(81, 71, 255, 1);

    .des {
      font-size: 12px;
      color: rgba(32, 41, 69, 0.62);
    }
  }
}

.top {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: rgb(56, 55, 67);
  border-bottom: 1px solid rgba(82, 100, 154, 0.13);

  span {
    margin-left: 10px;
    font-size: 14px;
  }
}

.chunks {

  .item {
    word-break: break-word;
    margin-bottom: 8px;
    padding: 8px;
    border: 1px solid rgba(82, 100, 154, 0.13);
    border-radius: 8px;
    background-color: rgba(87, 104, 161, 0.08);
    color: rgba(15, 21, 40, 0.82);
    font-size: 14px;
  }
}

.settingmain {
  display: flex;
  gap: 24px;

  .knowledge-settings {
    flex: 1;
    height: calc(100vh - 130px);
    overflow-y: auto;
  }

  .knowledge-settings {
    max-width: 900px;
    margin: 0 auto;
    padding: 24px 48px;

    .section-title {
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 4px;
    }

    /* 容器整体上下间距 */

    .el-row.mb-4 {
      margin-bottom: 16px;
    }

    /* 每一列内元素垂直居中，且有一定的左右内边距 */

    .el-col {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 8px;
      padding-right: 8px;
    }

    /* 标签标题样式 */

    .el-form-item__label {
      font-weight: 600;
      color: #202945; /* 深灰色 */
      font-size: 14px;
      line-height: 22px;
      padding-bottom: 6px;
    }

    /* 输入框宽度撑满 */

    .el-input,
    .el-input-number {
      width: 100%;
    }

    /* 单位字符样式，放在右侧，字体颜色稍淡 */

    .unit {
      display: inline-block;
      margin-left: 6px;
      color: #8c919f; /* 较淡灰色 */
      font-size: 13px;
      line-height: 22px;
      vertical-align: middle;
    }

    /* 禁用的输入框样式（如果有的话）*/

    .el-input.is-disabled,
    .el-input-number.is-disabled {
      background-color: #f5f7fa;
      color: #c0c4cc;
      cursor: not-allowed;
    }

    /* 使整个表单项高度适中 */

    .el-form-item {
      margin-bottom: 0;
      padding-bottom: 12px;
    }

    .embedding-model {
      display: flex;
      align-items: center;
      gap: 12px;
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 8px;
    }

    .model-icon {
      width: 20px;
      height: 20px;
    }

    .label-text {
      font-weight: 600;
      font-size: 14px;
    }

    .index-mode-wrapper {
      position: relative;
      display: flex;
      gap: 16px;
      margin-top: 12px;
    }

    .index-option {
      display: flex;
      align-items: flex-start;
      padding: 12px;
      background-color: #FCFCFD;
      border-radius: 8px;
      border: 1px solid #2c2c2c;
      cursor: pointer;
      width: 48%;
      transition: border 0.2s ease;
    }

    .index-option.active {
      border-color: #409eff;
    }

    .icon {
      font-size: 24px;
      margin-right: 10px;
      padding: 5px;
      border-radius: 5px;
      background-color: #fff;
      border: 1px solid #FCFCFD;
    }

    .info2 .title {
      font-weight: 600;
      display: flex;
    }

    .info2 .desc {
      font-size: 12px;
      color: #a0a0a0;
      margin-top: 4px;
      line-height: 1.4;
    }
  }

  .preview {
    width: 50%;
    height: calc(100vh - 128px);
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 16px;
    background-color: #fff;

    .chunk-title {
      color: gray;
      margin: 10px 0 5px;
      font-size: 12px;
    }
  }

  .preview-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
    font-size: 16px;
    color: #33475b;
  }

  .preview-title {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .chunk-count {
    color: #8c8c8c;
    font-size: 14px;
  }

  .chunk-text {
    white-space: pre-wrap;
    line-height: 1.5;
    color: #444;
    font-size: 14px;
    margin-bottom: 20px;
  }
}

.upload-complete-container {
  display: flex;
  gap: 24px;
  max-width: 1200px;
  margin: 40px auto;
  padding: 0 16px;

  .left-panel,
  .right-panel {
    background: transparent;
  }

  .left-panel {
    flex: 1 1 65%;
  }

  .right-panel {
    flex: 1 1 35%;
    display: flex;
    flex-direction: column;
  }

  .header {
    display: flex;
    align-items: center;
    font-size: 24px;

    .emoji {
      margin-right: 12px;
      font-size: 32px;
    }

    .title {
      font-weight: 700;
    }
  }

  .description {
    font-size: 13px;
    margin-bottom: 16px;
    color: #676f83;

    strong {
      color: #3a3a3a;
    }
  }

  .embedding-status {
    font-size: 14px;
    color: #57606a;
  }

  .file-list {
    max-height: 260px;
    overflow-y: auto;
    margin-bottom: 32px;

    .file-item {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 20px;

      .file-icon {
        font-size: 28px;
        color: #52c41a;
        min-width: 28px;
      }

      .file-info {
        flex: 1;

        .file-name {
          font-weight: 600;
          font-size: 14px;
          margin-bottom: 6px;
          color: #33475b;
          display: flex;
          align-items: center;

          .error-icon {
            margin-left: 10px;
            color: #f5222d;
            cursor: pointer;
          }
        }
      }
    }
  }

  .go-docs-btn {
    display: block;
    width: 160px;
    margin: 0 auto;
  }

  .next-steps-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 0.05);
    font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #33475b;
    background-color: #f9fafb;

    display: flex;
    flex-direction: column;
    gap: 16px;

    .el-icon {
      align-self: flex-start;
    }

    .card-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 10px;
    }

    .card-desc {
      font-size: 14px;
      color: #5a6270;
      line-height: 1.6;
    }
  }
}

:deep(.el-steps--simple) {
  background: none;
}


.space-x-4 > * + * {
  margin-left: 1rem;
}

.sectionHead {
  background-color: #F7F8FA;
  padding: 10px 0;
  cursor: pointer;

  .settingSvg {
    padding: 6px;
    margin: 0 10px;
    border-radius: 5px;
    background-color: #fff;
    width: 20px;
  }

  .titInfo {
    color: rgb(103, 111, 131);
  }

}

:deep(.el-form-item__label) {
  color: rgb(53, 64, 82);
}

.blockDiv {
  position: relative;
  border: 1px solid rgb(233, 235, 240);
  border-radius: 10px;
  overflow: hidden;
}

.active {
  border: 1px solid rgb(41, 109, 255) !important;
}

.allselect {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid rgb(233, 235, 240);
  border-radius: 10px;
  margin-top: 5px;

  img {
    margin-right: 10px;
    padding: 5px;
    border-radius: 5px;
    background: rgb(233, 235, 240);
  }

  > div {
    cursor: pointer;

    > :first-child {
      font-size: 13px;
      color: rgb(53, 64, 82);
    }

    > :nth-child(2) {
      font-size: 12px;
      color: rgb(103, 111, 131);
    }
  }
}

.allselect2 {
  flex-direction: column;
  align-items: unset;

  > div {
    display: flex;
  }
}

.option-mask {
  position: absolute;
  inset: 0;                 /* top:0 right:0 bottom:0 left:0 的缩写 */
  background: rgba(255, 255, 255, 0.5); /* 半透明白，保留底下内容可见 */
  cursor: not-allowed;      /* 鼠标样式 */
  z-index: 1;               /* 确保盖在 index-option 之上 */
}
</style>