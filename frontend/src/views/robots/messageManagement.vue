<template>
  <div>
    <TableSearch :query="query" :options="searchOpt" :search="handleSearch"/>
    <div class="container">
      <TableCustom
          :columns="columns"
          :tableData="tableData"
          :currentPage="page.index"
          :total="page.total"
          :page-size="page.size"
          :viewFunc="handleView"
          :delFunc="handleDelete"
          :change-page="changePage">
      </TableCustom>
    </div>

    <el-dialog title="消息详情" v-model="visible1" width="700px" destroy-on-close>
      <TableDetail :data="viewData"></TableDetail>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="message-management">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Message } from '@/types/message';
import {
  getMessagesApi,
  deleteMessageApi
} from '@/api/message';
import TableCustom from '@/components/table-custom.vue';
import TableDetail from '@/components/table-detail.vue';
import TableSearch from '@/components/table-search.vue';
import { FormOptionList } from '@/types/form-option';

// 查询相关
const query = reactive({
  conversationId: '',
  role: '',
  content: ''
});

const searchOpt = ref<FormOptionList[]>([
  { type: 'input', label: '对话ID：', prop: 'conversationId' },
  {
    type: 'select',
    label: '角色：',
    prop: 'role',
    options: [
      { label: '全部', value: '' },
      { label: '用户', value: 'user' },
      { label: '助手', value: 'assistant' }
    ]
  },
  { type: 'input', label: '内容：', prop: 'content' }
]);

const handleSearch = () => {
  changePage(1);
};

// 表格相关
const columns = ref([
  { type: 'index', label: '序号', width: 55, align: 'center' },
  { prop: 'id', label: '消息ID' },
  { prop: 'conversation_id', label: '对话ID' },
  { prop: 'role', label: '角色', width: 100 },
  {
    prop: 'content',
    label: '内容',
    showOverflowsystem_prompt: true
  },
  { prop: 'timestamp', label: '时间' },
  { prop: 'operator', label: '操作', width: 180 },
]);

const page = reactive({
  index: 1,
  size: 10,
  total: 0,
});

const tableData = ref<Message[]>([]);

const getData = async () => {
  const params = {
    page: page.index,
    per_page: page.size,
    ...query
  };

  const res = await getMessagesApi(params.conversationId, params);
  tableData.value = res.data.list;
  page.total = res.data.pageTotal;
};

getData();

const changePage = (val: number) => {
  page.index = val;
  getData();
};

// 查看详情弹窗相关
const visible1 = ref(false);
const viewData = ref({
  row: {},
  list: [
    { prop: 'id', label: '消息ID' },
    { prop: 'conversation_id', label: '对话ID' },
    { prop: 'role', label: '角色' },
    { prop: 'content', label: '内容' },
    { prop: 'reasoning_content', label: '推理内容' },
    { prop: 'timestamp', label: '时间' }
  ]
});

const handleView = (row: Message) => {
  viewData.value.row = { ...row };
  visible1.value = true;
};

// 删除相关
const handleDelete = async (row: Message) => {
  try {
    await deleteMessageApi(row.conversation_id, row.id);
    ElMessage.success('删除成功');
    getData();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};
</script>

<style scoped></style>