<template>
  <div class="box">
    <div class="layout-header">
      <div class="info">
        <el-icon @click="goList" class="backIcon">
          <ArrowLeft/>
        </el-icon>
        <img src="@/assets/img/defaults/dataset_text.png" alt="" class="file-img">
        <div class="documentInfo">
          <div>
            <span>{{ knowledgeInfo.name }}</span>
            <el-icon @click="showEditForm">
              <Edit/>
            </el-icon>
          </div>
          <div>
            <span>{{ formatFileSize(knowledgeInfo.word_count) }}</span>
            <span>{{ documentList.length }} 个文件</span>
          </div>
        </div>
      </div>
      <el-button type="primary" @click="goUpload">+ 知识</el-button>
    </div>
    <div class="main">
      <div class="left">
        <el-input
            v-model="searchDocument"
            placeholder="搜索"
            class="input-with-select"
            @input="filterDocuments"
        >
          <template #prepend>
            <el-button :icon="Search"/>
          </template>
        </el-input>
        <div class="tit">文件列表</div>
        <div class="documents">
          <div class="item" :class="{'active':nowDocumentId === item.id}" v-for="(item, index) in filteredDocuments"
               :key="index" @click="showChunks(item)">
            <el-icon :style="{color:getDocumentColorInfo(item).color}">
              <Document/>
            </el-icon>
            <span class="ellipsis">{{ item.name }}</span>
          </div>
        </div>
        <el-pagination style="margin:20px auto;" class="justify-center" background layout="prev, next" :total="dtotal" :current-page="dpage"
                       @current-change="changeDocumentPage" />
      </div>
      <div class="right">
        <div class="top">
          <div class="toptop">
            <div class="topl">
              <el-icon>
                <Document/>
              </el-icon>
              <span>{{ nowDocument.name }}</span>
            </div>
            <div>
              <span>状态：</span>
              <span
                  :style="{color:getDocumentColorInfo(nowDocument).color}">{{
                  getDocumentColorInfo(nowDocument).text
                }}</span>
              <!--              <el-tooltip effect="dark" content="编辑" placement="top">-->
              <!--                <el-icon @click="editDocumentName(item)" style="margin-left: 20px;">-->
              <!--                  <Edit/>-->
              <!--                </el-icon>-->
              <!--              </el-tooltip>-->
              <el-icon class="del" @click="deleteDocument">
                <Delete/>
              </el-icon>
            </div>
          </div>
          <!--          <div>-->
          <!--            <div>文档信息</div>-->
          <!--            <span class="word_count">原始文件大小：{{ nowDocument?.data_source_detail_dict?.upload_file?.size }}</span>-->
          <!--            <span class="word_count">上传日期：{{ $moment.unix(nowDocument.created_at).format('YYYY-MM-DD HH:mm') }}</span>-->
          <!--            <span class="word_count">最后更新日期：{{ $moment.unix(nowDocument?.data_source_detail_dict?.upload_file?.created_at).format('YYYY-MM-DD HH:mm') }}</span>-->
          <!--          </div>-->
          <!--          <div>-->
          <!--            <div>技术参数</div>-->
          <!--            <span class="word_count">分段规则：{{ nowDocument.word_count }}</span>-->
          <!--            <span class="word_count">段落长度：{{ nowDocument.word_count }}</span>-->
          <!--            <span class="word_count">平均段落长度：{{ nowDocument.word_count }}</span>-->
          <!--            <span class="word_count">召回次数：{{ nowDocument.word_count }}</span>-->
          <!--            <span class="word_count">嵌入时间：{{ nowDocument.word_count }}</span>-->
          <!--            <span class="word_count">嵌入花费：{{ nowDocument.word_count }}</span>-->
          <!--          </div>-->
        </div>
        <div class="chunkDiv">
          <div class="chunks" v-for="(item, index) in chunksList" :key="item.chunk_index">
            <div class="item" @mouseenter="item.hover = true" @mouseleave="item.hover = false">
              <div v-html="item.sign_content">
              </div>
              <div class="keywords" v-if="item.keywords && item.keywords?.length>0">
                关键词：<span v-for="item2 in item.keywords">#{{ item2 }}</span>
              </div>
              <div class="edit edit2">
                <span style="color:green;" v-if="item.enabled === true">已启用</span>
                <span style="color:red;" v-else>已禁用</span>
              </div>
              <div class="edit" v-if="item.hover">
                <el-tooltip effect="dark" content="编辑" placement="top">
                  <el-icon @click="editChunk(item)">
                    <Edit/>
                  </el-icon>
                </el-tooltip>
                <el-tooltip effect="dark" content="删除" placement="top">
                  <el-icon @click="delChunk(item)">
                    <Delete/>
                  </el-icon>
                </el-tooltip>
                <el-tooltip effect="dark" content="开启" placement="top">
                  <el-switch v-model="item.enabled" @change="changeChunkStatus(item)"></el-switch>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
        <el-pagination style="margin:20px auto" background layout="prev, pager, next" :total="total"
                       @change="changeChunks" :page-count="pageCount"/>
      </div>
    </div>
    <el-dialog
        title="编辑分段"
        v-model="editChunkVisible"
        width="800px"
    >
      <el-form>
        <el-form-item prop="text">
          <el-input type="textarea" rows="17" v-model="nowChunkInfo.sign_content"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editChunkVisible = false">取消</el-button>
          <el-button type="primary" @click="toEditChunk">提交</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        title="编辑知识库"
        v-model="dialogVisible"
        width="480px"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-position="top">
        <el-form-item label="知识库名称" prop="name">
          <el-input v-model="formData.name" placeholder="输入知识名称"/>
        </el-form-item>
        <el-form-item label="描述" prop="system_prompt">
          <el-input type="textarea" rows="5" v-model="formData.description" placeholder="输入数据集的简介"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button @click="toUpdateKnowledgeBase">提交</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, watch, computed} from 'vue';
import {useRoute} from 'vue-router';
import {getKnowledgeBase, getKnowledgeBaseDocument, updateKnowledgeBase, delDocument} from '@/api/knowledge';
import {getChunksByDocumentId, editChunks, delChunks, changeChunksStatus,changeChunksStatusTrue} from '@/api/chunks';
import {Delete, Refresh, Search} from '@element-plus/icons-vue'
import router from "@/router";
import {ElMessage, UploadProps, ElMessageBox} from "element-plus";
import $moment from 'moment';

// 使用 Vue Router 来访问当前路由的 query 参数
const route = useRoute();
const knowledgeInfo = ref({});
const nowDocumentId = ref('');
const searchDocument = ref('');
const documentList = ref([]);
const chunksList = ref([]);
const dialogVisible = ref(false);
const editChunkVisible = ref(false);
// 初次加载时获取数据
onMounted(() => {
  loadData();
});
const nowDocument = ref({});

const page = ref(1);
const limit = ref(20);
const total = ref(0);
const pageCount = ref(0);

const dpage = ref(1);
const dlimit = ref(20);
const dtotal = ref(0);
const changeDocumentPage = (page)=>{
  dpage.value = page;
  loadData();
}
// 模拟数据加载函数
const loadData = async () => {
  chunksList.value = [];
  let res = await getKnowledgeBase(route.query.id);
  knowledgeInfo.value = res;
  console.log('knowledgeInfo', knowledgeInfo.value)
  let res2 = await getKnowledgeBaseDocument({
    page: dpage.value,
    limit: dlimit.value
  }, route.query.id);
  documentList.value = res2.data;
  dtotal.value = res2.total;
  if (documentList.value.length > 0) {
    nowDocument.value = documentList.value[0];
    showChunks(nowDocument.value);
  } else {
    nowDocument.value = {};
  }
};
const formatFileSize = (bytes) => {
  if (bytes < 1024) {
    return bytes + ' B';  // 字节（B）
  } else if (bytes < 1024 * 1024) {
    return Math.floor(bytes / 1024) + ' KB';  // 千字节（KB）
  } else if (bytes < 1024 * 1024 * 1024) {
    return (bytes / 1024 / 1024).toFixed(2) + ' MB';  // 兆字节（MB）
  } else {
    return (bytes / 1024 / 1024 / 1024).toFixed(2) + ' GB';  // 千兆字节（GB）
  }
};
const showChunks = (document) => {
  nowDocument.value = document;
  page.value = 1;
  limit.value = 10;
  getChunksByDocumentId(route.query.id, document.id, {
    page: page.value,
    limit: limit.value,
  }).then(res => {
    chunksList.value = res.data;
    total.value = res.total;
    pageCount.value = res.total_pages;
  });
}
const changeChunks = (currentPage, pageSize) => {
  page.value = currentPage;
  getChunksByDocumentId(route.query.id, nowDocument.value.id, {
    page: page.value,
    limit: pageSize,
  }).then(res => {
    chunksList.value = res.data;
    total.value = res.total;
    pageCount.value = res.total_pages;
  });
}
const goUpload = () => {
  router.push('/knowledgeUpload?id=' + route.query.id);
}
const goList = () => {
  router.push('/knowledges');
}
const formData = ref({
  name: '',
  description: ''
});
const formRules = {
  name: [
    {required: true, message: '请输入知识库名称', trigger: 'blur'},
    {min: 3, max: 20, message: '名称长度在 3 到 20 个字符之间', trigger: 'blur'}
  ]
};

const toUpdateKnowledgeBase = async () => {
  let res = await updateKnowledgeBase(route.query.id, formData.value);
  if (res) {
    ElMessage.success('修改成功');
  } else {
    ElMessage.error(res.message);
  }
  loadData();
  dialogVisible.value = false;
}

const nowChunkInfo = ref({});
const editChunk = (chunk) => {
  editChunkVisible.value = true;
  nowChunkInfo.value = chunk;
}
const toEditChunk = async () => {
  console.log('nowChunkInfo.value', nowChunkInfo.value)
  if (!nowChunkInfo.value.enabled) {
    ElMessage.error('无法更新已禁用的片段');
    editChunkVisible.value = false;
    return;
  }
  let res = await editChunks({
    content: nowChunkInfo.value.sign_content,
    keywords: nowChunkInfo.value.keywords,
  }, route.query.id, nowDocument.value.id, nowChunkInfo.value.id);
  if (res) {
    ElMessage.success('修改成功');
  } else {
    ElMessage.error(res.message);
  }
  editChunkVisible.value = false;
}
const filterDocuments = () => {
  // 通过计算属性会自动更新，无需手动操作
};
const filteredDocuments = computed(() => {
  if (!searchDocument.value) {
    return documentList.value;
  }
  return documentList.value.filter(item =>
      item.name.toLowerCase().includes(searchDocument.value.toLowerCase())
  );
});
const deleteDocument = () => {
  if (!nowDocument.value?.id) {
    return;
  }
  ElMessageBox.confirm(
      '此操作将永久删除该文档及其所有切片，是否继续？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(() => {
        // 用户点击“确定”后再执行删除
        delDocument(route.query.id, nowDocument.value.id).then(res => {
          loadData();
        }).catch(() => {
          ElMessage.error('删除请求失败，请稍后重试');
        });
      })
      .catch(() => {
      });
};
const showEditForm = () => {
  dialogVisible.value = true;
  formData.value.name = knowledgeInfo.value.name;
  formData.value.description = knowledgeInfo.value.description;
};
const editDocumentName = (item) => {

}
const getDocumentColorInfo = (document) => {
  const colorNameList = [
    {status: 'queuing', color: '#36bffa', text: '排队'},
    {status: 'indexing', color: '#36bffa', text: '索引中'},
    {status: 'splitting', color: '#36bffa', text: '索引中'},
    {status: 'paused', color: '#c8ceda99', text: '已暂停'},
    {status: 'error', color: 'red', text: '错误'},
    {status: 'available', color: '#47cd89', text: '可用'},
    {status: 'completed', color: '#47cd89', text: '可用'},
    {status: 'enabled', color: '#47cd89', text: '已启用'},
    {status: 'disabled', color: 'gray', text: '已禁用'},
    {status: 'archived', color: 'gray', text: '已归档'},
    {status: 'waiting', color: 'orange', text: '排队中'},
    {status: 'parsing', color: 'orange', text: '暂停中'},
  ];

  // 如果被归档，优先判断归档状态
  if (document.archived) {
    if (document.indexing_status === 'completed') {
      return {status: 'archived', color: 'gray', text: '已归档'};
    }
  }

  // 优先判断 display_status（如果你项目中有）
  if (document.display_status) {
    const byDisplay = colorNameList.find(item => item.status === document.display_status);
    if (byDisplay) return byDisplay;
  }

  // 否则根据 indexing_status 查找
  const byIndexing = colorNameList.find(item => item.status === document.indexing_status);
  if (byIndexing) return byIndexing;

  // fallback 默认返回值
  return {status: 'unknown', color: '#999', text: '无'};
};
const delChunk = (item) => {
  ElMessageBox.confirm(
      '删除这个分段？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(() => {
        // 用户点击“确定”后再执行删除
        delChunks(route.query.id, nowDocument.value.id, item.id).then(res => {
          getChunksByDocumentId(route.query.id, nowDocument.value.id, {
            page: page.value,
            limit: limit.value,
          }).then(res2 => {
            chunksList.value = res2.data;
            total.value = res2.total;
            pageCount.value = res2.total_pages;
          });
        }).catch(() => {
          ElMessage.error('删除请求失败，请稍后重试');
        });
      })
      .catch(() => {
      });
}
const changeChunkStatus = async (chunk) => {
  if(chunk.enabled){
    await changeChunksStatusTrue(route.query.id, nowDocument.value.id, chunk.id);
  }else{
    await changeChunksStatus(route.query.id, nowDocument.value.id, chunk.id);
  }
}

</script>

<style scoped lang="less">
.info {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .file-img {
    margin: 0 10px;
    width: 34px;
    height: 34px;
    border-radius: 9px;
    border: .5px solid rgba(82, 100, 154, 0.13);
  }

  span {
    font-size: 14px;
  }

  .documentInfo {
    > :first-child {
      display: flex;
      align-items: center;
      font-size: 14px;

      span {
        margin-right: 2px;
      }

      i {
        cursor: pointer;
      }
    }

    > :last-child {
      display: flex;

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-color: rgba(87, 104, 161, 0.08);
        border-bottom-color: rgba(211, 211, 216, 0);
        margin-right: 5px;
        font-size: 10px;
        color: rgba(15, 21, 40, 0.82);
      }
    }
  }
}

.box {
  height: 100%;
}

.main {
  height: 87vh;
  border: 1px solid rgba(82, 100, 154, 0.13);
  border-radius: 8px;
  display: flex;


  .left {
    width: 300px;
    padding: 12px;
    border-right: .5px solid rgba(82, 100, 154, 0.13);
    overflow-y: scroll;

    .input-with-select {
      border-radius: 8px;
    }

    .tit {
      margin: 16px 0 4px;
      font-size: 12px;
      color: rgba(32, 41, 69, 0.62);
      line-height: 1rem;
    }

    .documents {
      color: rgb(56, 55, 67);

      .item {
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 6px .5rem;
        cursor: pointer;

        span {
          margin-left: 8px;
          font-size: 14px;
        }

        &:hover {
          background-color: rgba(87, 104, 161, 0.08);
        }
      }

      .active {
        background-color: rgba(87, 104, 161, 0.08);
      }
    }
  }

  .right {
    width: 100%;

    display: flex;
    flex-direction: column;


    .top {
      //display: flex;
      //align-items: center;
      //justify-content: space-between;
      padding: 12px 16px;
      color: rgb(56, 55, 67);
      border-bottom: 1px solid rgba(82, 100, 154, 0.13);

      .toptop {
        display: flex;
        align-items: center;
        justify-content: space-between;
        //border-bottom:1px solid gray;
      }

      .topl {
        display: flex;
        align-items: center;
      }

      span {
        font-size: 14px;
      }

      .del {
        cursor: pointer;
        margin-left: 20px;
      }
    }

    .chunkDiv {
      position: relative; /* 必要：让 loading 遮罩定位到这里 */
      flex: 1; /* 占满剩余高度 */
      overflow-y: auto; /* 内部滚动 */
      padding: 16px; /* 可选，保持样式一致 */
    }

    .chunks {

      padding: 16px 16px 0;

      .item {
        position: relative;
        word-break: break-word;
        margin-bottom: 8px;
        padding: 8px;
        border: 1px solid rgba(82, 100, 154, 0.13);
        border-radius: 8px;
        background-color: rgba(90, 108, 167, 0.04);
        color: rgba(15, 21, 40, 0.82);
        font-size: 14px;
        cursor: pointer;

        &:hover {
          background-color: rgba(87, 104, 161, 0.08);

        }

        .edit {
          width: 100px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: absolute;
          top: 2px;
          right: 10px;
          background-color: #fff;
          padding: 2px 5px 0;
          border-radius: 4px;
        }

        .edit2 {
          width: unset;
        }
      }
    }

    .keywords {
      margin-top: 10px;
      width: 100%;
      font-size: 12px;

      span {
        font-size: 12px;
        margin-right: 5px;
      }
    }
  }

}

.main2 {
  overflow: auto;
}

.watiUpImg {
  width: 50px;
  height: 50px;
  border-radius: 8px;
}

.line {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.word_count {
  font-size: 12px !important;
  margin-left: 30px !important;
}
</style>