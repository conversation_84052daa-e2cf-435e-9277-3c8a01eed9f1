<template>
  <div>
    <TableSearch :query="query" :options="searchOpt" :search="handleSearch"/>
    <div class="container">
      <TableCustom
          :columns="columns"
          :tableData="tableData"
          :currentPage="page.index"
          :total="page.total"
          :page-size="page.size"
          :viewFunc="handleView"
          :delFunc="handleDelete"
          :change-page="changePage"
          :editFunc="handleEdit">
        <template #toolbarBtn>
          <el-button type="warning" :icon="CirclePlusFilled" @click="visible = true">新增对话</el-button>
        </template>
      </TableCustom>
    </div>

    <el-dialog
        :title="isEdit ? '编辑对话' : '新增对话'"
        v-model="visible"
        width="700px"
        destroy-on-close
        :close-on-click-modal="false"
        @close="closeDialog">
      <TableEdit
          :form-data="rowData"
          :options="options"
          :edit="isEdit"
          :update="updateData"/>
    </el-dialog>

    <el-dialog title="对话详情" v-model="visible1" width="700px" destroy-on-close>
      <TableDetail :data="viewData"></TableDetail>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="conversation-management">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { CirclePlusFilled } from '@element-plus/icons-vue';
import { Conversation } from '@/types/conversation';
import {
  getConversationsApi,
  getConversationApi,
  createConversationApi,
  updateConversationApi,
  deleteConversationApi,
  endConversationApi
} from '@/api/conversation';
import TableCustom from '@/components/table-custom.vue';
import TableDetail from '@/components/table-detail.vue';
import TableSearch from '@/components/table-search.vue';
import { FormOption, FormOptionList } from '@/types/form-option';

// 查询相关
const query = reactive({
  userId: '',
  status: '' // active/ended
});

const searchOpt = ref<FormOptionList[]>([
  { type: 'input', label: '用户ID：', prop: 'userId' },
  {
    type: 'select',
    label: '状态：',
    prop: 'status',
    options: [
      { label: '全部', value: '' },
      { label: '进行中', value: 'active' },
      { label: '已结束', value: 'ended' }
    ]
  }
]);

const handleSearch = () => {
  changePage(1);
};

// 表格相关
const columns = ref([
  { type: 'index', label: '序号', width: 55, align: 'center' },
  { prop: 'id', label: '对话ID' },
  { prop: 'user_id', label: '用户ID' },
  // {
  //   prop: 'status',
  //   label: '状态',
  //   formatter: (row) => row.end_time ? '已结束' : '进行中'
  // },
  { prop: 'start_time', label: '开始时间' },
  { prop: 'end_time', label: '结束时间' },
  { prop: 'operator', label: '操作', width: 300 },
]);

const page = reactive({
  index: 1,
  size: 10,
  total: 0,
});

const tableData = ref<Conversation[]>([]);

const getData = async () => {
  const params = {
    page: page.index,
    per_page: page.size,
    ...query
  };

  const res = await getConversationsApi(params);
  tableData.value = res.data.list;
  page.total = res.data.pageTotal;
};

getData();

const changePage = (val: number) => {
  page.index = val;
  getData();
};

// 新增/编辑弹窗相关
const options = ref<FormOption>({
  labelWidth: '100px',
  span: 12,
  list: [
    { type: 'input', label: '用户ID', prop: 'user_id', required: true },
    { type: 'datetime', label: '开始时间', prop: 'start_time', required: true },
    { type: 'datetime', label: '结束时间', prop: 'end_time' }
  ]
});

const visible = ref(false);
const isEdit = ref(false);
const rowData = ref({});

const handleEdit = (row: Conversation) => {
  rowData.value = { ...row };
  isEdit.value = true;
  visible.value = true;
};

const updateData = async (submitData) => {
  try {
    if (isEdit.value) {
      await updateConversationApi(submitData.id, submitData);
    } else {
      await createConversationApi(submitData);
    }
    closeDialog();
    getData();
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
};

const closeDialog = () => {
  visible.value = false;
  isEdit.value = false;
};

// 查看详情弹窗相关
const visible1 = ref(false);
const viewData = ref({
  row: {},
  list: [
    { prop: 'id', label: '对话ID' },
    { prop: 'user_id', label: '用户ID' },
    { prop: 'start_time', label: '开始时间' },
    { prop: 'end_time', label: '结束时间' },
    { prop: 'created_at', label: '创建时间' },
    { prop: 'updated_at', label: '更新时间' }
  ]
});

const handleView = (row: Conversation) => {
  viewData.value.row = { ...row };
  visible1.value = true;
};

// 删除相关
const handleDelete = async (row: Conversation) => {
  try {
    await deleteConversationApi(row.id);
    ElMessage.success('删除成功');
    getData();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

// 结束对话
const handleEndConversation = async (row: Conversation) => {
  try {
    await endConversationApi(row.id);
    ElMessage.success('对话已结束');
    getData();
  } catch (error) {
    ElMessage.error('操作失败');
  }
};
</script>

<style scoped></style>