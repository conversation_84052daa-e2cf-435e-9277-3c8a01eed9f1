<template>
  <div class="layout-header">
    <span>APP管理</span>
    <el-input clearable style="width: 200px;" placeholder="搜索" @input="getData2" v-model="appName" :prefix-icon="Search"></el-input>
  </div>
  <el-table :data="tableData" style="width: 100%">
    <el-table-column fixed label="序号" type="index" width="80"/>
    <el-table-column prop="Name" label="图标" sortable>
      <template #default="scoped">
        <div class="newImg">
          <img v-if="scoped.row.icon_type!=='emoji'" :src="scoped.row.icon_url" alt="" style="width: 42px;height: 42px;"/>
          <div v-else>
            <div class="emoji" :style="{background:scoped.row.icon_background}">{{ scoped.row.icon }}</div>
          </div>
          <img :src="`/images/${scoped.row.mode}.png`" alt="" class="tag">
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="name" label="名称" sortable/>
    <el-table-column prop="description" label="描述" sortable>
      <template #default="{ row }">
        <span class="table-ellipsis">{{ row.description }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="author_name" label="作者" sortable/>
    <el-table-column prop="CreatedAt" label="创建时间" sortable>
      <template #default="scope">
        {{ $moment(scope.row.CreatedAt).format('YYYY/MM/DD HH:mm') }}
      </template>
    </el-table-column>
    <el-table-column prop="UpdatedAt" label="更新时间" sortable>
      <template #default="scope">
        {{ $moment(scope.row.UpdatedAt).format('YYYY/MM/DD HH:mm') }}
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <div class="flex items-center">
          <el-tooltip
              class="box-item"
              effect="dark"
              content="删除APP"
              placement="top-start"
          >
            <el-icon class="backIcon" @click="deleteTenant(scope.row)">
              <Delete/>
            </el-icon>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination style="margin:20px auto" background layout="prev, pager, next" :total="total"
                 @change="changePage"/>
  <el-dialog v-model="dialogFormVisible" title="添加团队" width="500">
    <el-form :model="addUserForm" :rules="rules" ref="ruleFormRef">

      <el-form-item label="团队名称" prop="name">
        <el-input v-model="addUserForm.name" placeholder=""/>
      </el-form-item>
      <el-form-item label="指定团长" prop="user_id">
        <el-select v-model="addUserForm.user_id">
          <el-option v-for="item in canInviteUsers" :label="item.Name" :value="item.ID"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="addUser">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="agent-management">
import {ref, reactive} from 'vue';
import {ElMessage, ElMessageBox, FormInstance} from 'element-plus';
import {User} from '@/types/user';
import {getMyAgentsApi, deleteAgentApi, getAgentApi, setSiteStatus} from '@/api/agent';
import {useUserStore} from "@/store/user";
import {getUsersApi} from "@/api/user";
import $moment from 'moment';
import {Delete, Search} from "@element-plus/icons-vue";

const userStore = useUserStore();

const page = ref(1);
const limit = ref(10);
const total = ref(0);
const totalPage = ref(0);
const rules = {
  name: [
    {required: true, message: '请输入团队名称', trigger: 'blur'},
    {max: 255, message: '名称不能超过255个字符', trigger: 'blur'}
  ],
  user_id: [
    {required: true, message: '请指定一名团长', trigger: 'blur'},
  ],
};
const ruleFormRef = ref<FormInstance>();
const tableData = ref<User[]>([]);
const getData = async () => {
  const res = await getMyAgentsApi({
    page: page.value,
    limit: limit.value,
    is_created_by_me: false,
  });
  tableData.value = res.data;
  total.value = res.total;
};
const appName = ref('');
const getData2 = async () => {
  const res = await getMyAgentsApi({
    page: page.value,
    name:appName.value,
    limit: limit.value,
    is_created_by_me: false,
  });
  tableData.value = res.data;
  total.value = res.total;
};
getData();
const dialogFormVisible = ref(false);
const addUserForm = ref({});

const addUser = async () => {
  if (!ruleFormRef.value) return
  await ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      await addWorkspaces(addUserForm.value);
      dialogFormVisible.value = false;
      await getData();
    } else {
      console.log('error submit!', fields)
    }
  })
}
const changePage = (newPage) => {
  page.value = newPage;
  getData();
}
const deleteTenant = async (app) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    type: 'warning'
  })
      .then(async () => {
        await deleteAgentApi(app.id);
        await getData();
      })
      .catch(() => {
      });

}
const canInviteUsers = ref([]);
const showAddTenant = async () => {
  let res = await getUsersApi({
    page: 1,
    size: 999
  });
  canInviteUsers.value = res.data.data;
  dialogFormVisible.value = true;
}
</script>

<style scoped lang="less">
.newImg {
  position: relative;
  width: 42px;

  .tag {
    position: absolute;
    right: -5px;
    top: 28px;
    width: 20px;
    height: 20px;
  }
}
.emoji {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  overflow: hidden;
  font-size: 42px;
  border-radius: 14px;
  padding-bottom: 2px;
}
.table-ellipsis {
  display: inline-block;          /* 或 block, 取决于你的布局 */
  max-width: 100%;               /* 不能超出列宽 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>