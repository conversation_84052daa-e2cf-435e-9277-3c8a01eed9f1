<template>
  <div class="top">
    <div class="info">
      <el-icon @click="goUrl" class="backIcon">
        <arrow-left/>
      </el-icon>
      <div>
        <img v-if="robotInfo.icon_type!=='emoji'" :src="robotInfo.icon_url" alt=""/>
        <div v-else>
          <div class="emoji" :style="{background:robotInfo.icon_background}">{{ robotInfo.icon }}</div>
        </div>
        <div>{{ robotInfo.name }}</div>
        <el-tooltip
            effect="dark"
            content="编辑应用"
            placement="top-start"
        >
          <el-icon class="botIcon" @click="showEdit">
            <Edit/>
          </el-icon>
        </el-tooltip>
      </div>
    </div>
  </div>
  <div :style="{ width: '100%', height: 'calc(100vh - 63px)', position: 'relative' }">
    <!-- 加载状态 -->
    <div v-if="loading" :style="{
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    }">
      <div style="text-align: center;">
        <div class="spinner"></div>
        <p style="color: #666; font-size: 16px; margin: 16px 0 0 0;">正在加载...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-alert" :style="{ maxWidth: '80%', margin: '20px auto' }">
      <h3>加载错误</h3>
      <p>{{ error }}</p>
    </div>

    <!-- iframe -->
    <iframe
      ref="iframeRef"
      id="embedded-frame"
      :src="`/web/app/${route.query.id}/workflow`"
      title="Flow Setting Embedded Page"
      width="100%"
      height="100%"
      :style="{
        border: 'none',
        display: loading ? 'none' : 'block'
      }"
      @load="handleIframeLoad"
      @error="handleIframeError"
    />
  </div>
  <el-dialog
      title="编辑应用"
      v-model="dialogVisible"
      width="480px"
  >
    <el-form :rules="ruleForm.icon_type === 'emoji' ? rules : rules2" :model="ruleForm" label-position="top"
             ref="ruleFormRef">
      <el-form-item label="应用名称" prop="name">
        <el-input v-model="ruleForm.name" placeholder="为应用指定一个唯一的名称"/>
      </el-form-item>
      <el-form-item label="应用功能说明" prop="description">
        <el-input v-model="ruleForm.description" type="textarea" placeholder="介绍应用功能并向应用用户展示"
                  :rows="5"/>
      </el-form-item>
      <el-radio-group v-model="ruleForm.icon_type" class="icon_type">
        <el-radio-button label="emoji">表情图标</el-radio-button>
        <el-radio-button label="image">上传图片</el-radio-button>

      </el-radio-group>
      <el-form-item v-if="ruleForm.icon_type === 'emoji'" label="选择表情" prop="icon">
        <el-popover placement="bottom" width="300" trigger="click">
          <template #reference>
            <div class="showIcon">{{ ruleForm.icon || '🤖' }}</div>
          </template>
          <div class="emoji-grid">
              <span v-for="emoji in emojiList" :key="emoji" @click="ruleForm.icon = emoji">
                {{ emoji }}
              </span>
          </div>
        </el-popover>
      </el-form-item>

      <!-- 上传模式 -->
      <el-form-item v-else label="图标" prop="icon">
        <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeUpload"
            :on-error="handleUploadError"
        >
          <template v-if="!ruleForm.iconImage && ruleForm.icon_type === 'image'">
            <img src="@/assets/img/index/watiUpImg.png" class="watiUpImg"/>
          </template>
          <template v-else>
            <img :src="ruleForm.iconImage" class="watiUpImg"/>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button @click="toUpdateAgents">提交</el-button>
      </div>
    </template>
  </el-dialog>
  <el-drawer v-model="showAppSetting" di rection="rtl">
    <template #header>
      <div>
        <h4>功能</h4>
        <div style="font-size: 12px">增强 web app 用户体验</div>
      </div>
    </template>
    <template #default>
      <div class="functions">
        <el-card class="var-card" shadow="never" @mouseenter="showEditOpening = true"
                 @mouseleave="showEditOpening = false">
          <div class="mb-1 flex justify-between">
            <div class="flex items-center">
              <img src="@/assets/img/svg/open.svg" alt="" style="width: 30px;height: 30px;"/>
              <span class="ml-1">对话开场白</span>
            </div>
            <el-switch v-model="showOpenStatement"/>
          </div>
          <div class="font-12 opening">
            {{ model_config.opening_statement }}

          </div>
          <div v-if="showEditOpening" class="flex items-center editOpen font-14" @click="toShowEditOpeningDialog">
            <Edit class="edit"/>
            编写开场白
          </div>
        </el-card>
        <el-card class="var-card" shadow="never">
          <div class="mb-1 flex justify-between">
            <div class="flex items-center">
              <img src="@/assets/img/svg/next.svg" alt="" style="width: 30px;height: 30px;"/>
              <span class="ml-1">下一步问题建议</span>
            </div>
            <el-switch v-model="enabledSuggestion" @change="setSuggestionEnable"/>
          </div>
          <div class="font-12 opening">
            设置下一步问题建议可以让用户更好的对话。
          </div>
        </el-card>
        <el-card class="var-card" shadow="never">
          <div class="mb-1 flex justify-between">
            <div class="flex items-center">
              <img src="@/assets/img/svg/quote.svg" alt="" style="width: 30px;height: 30px;"/>
              <span class="ml-1">引用和归属</span>
            </div>
            <el-switch v-model="enabledOrigin" @change="setOriginEnable"/>
          </div>
          <div class="font-12 opening">
            显示源文档和生成内容的归属部分。
          </div>
        </el-card>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {useRoute, useRouter} from 'vue-router';
import {computed, reactive, ref} from "vue";
import {getAgentApi, updateAgentApi, setModelConfig, getMessagesHistory} from '@/api/agent'
import dayjs from "dayjs";
import {generatePrompt, setSiteImage, getTools, stopChat, getToolInfo, getDailyConversations,getDailyEndUser,getDailyAvgSession,getDailyToken,} from '@/api/agent';
import {ElMessage, FormRules, UploadProps} from "element-plus";
const route = useRoute();
const router = useRouter();
// 响应式状态
const loading = ref(true);
const error = ref<string | null>(null);
const iframeRef = ref<HTMLIFrameElement | null>(null);
const rules = reactive<FormRules<RuleForm>>({
  name: [
    {required: true, message: '为应用指定一个唯一的名称', trigger: 'blur'},
    {min: 3, max: 40, message: '长度在3-40个字符', trigger: 'blur'},
  ],
  description: [
    {required: false, message: '介绍应用功能并向应用用户展示', trigger: 'blur'},
  ],
  icon: [
    {required: true, message: '请选择一个表情', trigger: 'blur'},
  ],
});
const rules2 = reactive<FormRules<RuleForm>>({
  name: [
    {required: true, message: '为应用指定一个唯一的名称', trigger: 'blur'},
    {min: 3, max: 40, message: '长度在3-40个字符', trigger: 'blur'},
  ],
  description: [
    {required: false, message: '介绍应用功能并向应用用户展示', trigger: 'blur'},
  ],
  iconImage: [
    {required: true, message: '请选择一个封面', trigger: 'blur'},
  ],
});
const emojiList = [
  // 😀 常规表情
  '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '😍', '🥰', '😘', '😗', '😋', '😜', '🤪',

  // 💬 表达类
  '🤔', '🤨', '😐', '😑', '😶', '🙄', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢',

  // 🎉 活动类
  '🎉', '🥳', '🎂', '🎁', '🎈', '🎊', '🪅', '💌', '💯', '✔️', '✌️', '🤞', '🤟', '👍', '👎', '👏', '🙌', '🙏',

  // 🔥 热门类
  '💥', '🔥', '💦', '🌟', '💫', '✨', '💡', '🎯', '🚀', '🌈',

  // 🧠 AI / 技术
  '🤖', '🧠', '📦', '💻', '📱', '📊', '📈', '🖥️', '🧑‍💻', '🔐', '⚙️',

  // 🐶 动物
  '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🐙',

  // 🌍 地球/世界
  '🌍', '🌎', '🌏', '🌐', '🗺️', '🧭', '📍', '🚩'
];
const goUrl = () => {
  router.push('/myAgents');
}
interface RuleForm {
  name: string
  icon_type: string
  icon: string
  iconImage: string
  icon_background: string
  mode: string
  description: string
}
const showAppSetting = ref(false);
const robotInfo = ref({});
const ruleForm = reactive<RuleForm>({
  name: '',
  icon_type: 'emoji',
  icon: '🤖',
  iconImage: '',
  icon_background: '#FFEAD5',
  mode: 'chat',
  description: '',
});
const loadData = async () => {
  try {
    let tempRobotInfo = await getAgentApi(route.query.id);
    robotInfo.value = tempRobotInfo;
    if (tempRobotInfo.icon_type == 'emoji') {
      ruleForm.name = tempRobotInfo.name;
      ruleForm.icon_type = "emoji";
      ruleForm.icon = tempRobotInfo.icon;
      ruleForm.icon_background = "#FFEAD5";
      ruleForm.mode = "chat";
      ruleForm.description = tempRobotInfo.description;
    } else {
      ruleForm.name = tempRobotInfo.name;
      ruleForm.icon_type = "image";
      ruleForm.icon = tempRobotInfo.iconImage;
      ruleForm.mode = "chat";
      ruleForm.description = tempRobotInfo.description;
    }
  } catch (err) {
    console.error('加载数据出错：', err);
  }
};
loadData();
const dialogVisible = ref(false);
const showEdit = () => {
  dialogVisible.value = true;
}
const uploadUrl = `${import.meta.env.VITE_SERVE_HOST}/console/api/files/upload`;
const getUploadHeaders = () => {
  const token = localStorage.getItem('console_token');
  if (!token) {
    ElMessage.error('未找到有效的 token，请重新登录');
    return null;
  }
  return {
    'Authorization': `Bearer ${token}`,
    // 'X-Requested-With': 'XMLHttpRequest',
    // 'Accept': 'application/json'
  };
};
const uploadHeaders = ref(getUploadHeaders());
const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  if (response) {
    ruleForm.icon = response.id;
    ruleForm.icon_background = '#FFEAD5';
    ruleForm.icon_type = 'image';
    ruleForm.mode = 'chat';
    ElMessage.success('图片上传成功');
  } else {
    ElMessage.error('图片上传失败');
  }
};
// 上传失败处理方法
const handleUploadError = (error, uploadFile) => {
  console.error('图片上传失败:', error);
  ElMessage.error('图片上传失败，请检查网络或服务器状态');
};
const beforeUpload = (file) => {
  console.log('上传文件类型:', file.type);
  console.log('上传文件大小:', file.size);
  const allowedTypes = ['image/jpeg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB');
    return false;
  }
  ruleForm.iconImage = URL.createObjectURL(file);
  return true;
};
const toUpdateAgents = async () => {
  let form = {};
  if (ruleForm.icon_type === 'emoji') {
    form.description = ruleForm.description;
    form.icon = ruleForm.icon;
    form.icon_background = ruleForm.icon_background;
    form.icon_type = ruleForm.icon_type;
    form.name = ruleForm.name;
    form.use_icon_as_answer_icon = false;
  } else {
    form.description = ruleForm.description;
    form.icon = ruleForm.icon;
    form.icon_type = ruleForm.icon_type;
    form.name = ruleForm.name;
    form.use_icon_as_answer_icon = false;
  }
  await updateAgentApi(route.query.id, form);
  await setSiteImage(route.query.id, form);
  loadData();
  dialogVisible.value = false;
}
const showEditOpeningDialog = ref(false);
const tempOpeningVal = ref('');
const tempAnswers = ref<string[]>([])
const toShowEditOpeningDialog = () => {
  tempOpeningVal.value = JSON.parse(JSON.stringify(model_config.value.opening_statement))
  tempAnswers.value = JSON.parse(JSON.stringify(model_config.value.suggested_questions || []))  // 没有就空数组
  showEditOpeningDialog.value = true;
}
// 处理iframe加载事件
const handleIframeLoad = () => {
  loading.value = false;

  try {
    // 注入CSS隐藏导航栏
    if (iframeRef.value?.contentDocument) {
      const style = iframeRef.value.contentDocument.createElement('style');
      style.textContent = `
        /* 隐藏导航栏 - 使用多种选择器 */
        div[class="sticky top-0 left-0 right-0 z-30 flex flex-col grow-0 shrink-0 basis-auto min-h-[56px] border-b border-divider-regular"],
        div[class="sticky top-0 left-0 right-0 z-30 flex flex-col grow-0 shrink-0 basis-auto min-h-[56px]"],
        div[class="flex h-full flex-col border-l-[0.5px] border-divider-regular"],
        div[class="flex shrink-0 flex-col border-r border-divider-burn bg-background-default-subtle transition-all"],
        footer.shrink-0.grow-0.px-12.py-6 {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
          height: 0 !important;
          overflow: hidden !important;
          position: absolute !important;
          pointer-events: none !important;
        }

        .flex.shrink-0.flex-col.border-r.border-divider-burn.bg-background-default-subtle.transition-all {
            display: none;
        }
      `;
      iframeRef.value.contentDocument.head.appendChild(style);
    }
  } catch (error) {
    console.error('隐藏导航栏失败:', error);
  }
};

// 处理iframe错误
const handleIframeError = () => {
  error.value = '无法加载嵌入页面，请检查URL是否正确或者目标服务是否可用';
  loading.value = false;
};
</script>

<style scoped lang="less">
// 旋转动画 - 模拟 Ant Design 的 Spin 组件
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

// 错误提示样式 - 模拟 Ant Design 的 Alert 组件
.error-alert {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
  padding: 16px;

  h3 {
    color: #cf1322;
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
  }

  p {
    color: #cf1322;
    margin: 0;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.top {
  height: 46px;
  border-bottom: 1px solid rgba(29, 28, 35, .08);
  padding: 8px 12px;
  background-color: #F7F7FA;
  display: flex;
  align-items: center;
  justify-content: space-between;


  .info {
    display: flex;
    justify-content: center;

    > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    img {
      width: 32px;
      height: 32px;
      margin: 0 10px 0 0;
      border-radius: 8px;
    }

    span {
      font-size: 12px;
      color: rgba(32, 41, 69, 0.62);
      margin-left: 5px;
    }


  }

  .rightInfo {
    display: flex;
    align-items: center;

    .publishBtn {
      padding-right: 10px;
      color: rgba(15, 21, 40, 0.82);
      font-size: 12px;
    }
  }
}

.showIcon {
  padding: 20px 0 27px;
  border-radius: 10px;
  font-size: 60px;
  background-color: #FFEAD5;
}

.avatar-uploader {
  width: 64px;
  height: 64px;
}
.botIcon {
  margin-left: 10px;
  cursor: pointer;
}
.watiUpImg {
  width: 64px;
  height: 64px;
  border-radius: 14px;
}
</style>
