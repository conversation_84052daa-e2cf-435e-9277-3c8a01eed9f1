<template>
  <div class="main">
    <iframe :src="`/web/${frameUrl}/${route.params.from}`" frameborder="0" class="iframeMain"></iframe>
  </div>
</template>

<script setup lang="ts">
import {useRoute} from 'vue-router';
import {ref} from "vue";

const route = useRoute();
const frameUrl = ref('');
if(route.query.type === 'workflow'){
  frameUrl.value = 'workflow';
}else if(route.query.type === 'completion'){
  frameUrl.value = 'completion';
}else{
  frameUrl.value = 'chat';
}
console.log('route',route)
</script>

<style scoped lang="less">
.iframeMain{
  width: 100%;
  height: 100vh;
}
</style>
