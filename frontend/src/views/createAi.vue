<template>
  <div class="container">
    <div class="sidebar">
      <div class="logo">
        <img src="path/to/logo.png" alt="Logo" />
      </div>
      <ul class="menu">
        <li v-for="item in menuItems" :key="item.id">
          <button @click="selectMenuItem(item)">{{ item.name }}</button>
        </li>
      </ul>
    </div>

    <div class="main-content">
      <div class="header">
        <h1>{{ selectedItem.name }}</h1>
        <button @click="goToSettings">Settings</button>
      </div>

      <div class="content">
        <div v-for="section in sections" :key="section.id" class="section">
          <h2>{{ section.title }}</h2>
          <p>{{ section.content }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      menuItems: [
        { id: 1, name: "Item 1" },
        { id: 2, name: "Item 2" },
        { id: 3, name: "Item 3" },
      ],
      selectedItem: { name: "Item 1" },
      sections: [
        { id: 1, title: "Section 1", content: "Content for Section 1" },
        { id: 2, title: "Section 2", content: "Content for Section 2" },
      ]
    };
  },
  methods: {
    selectMenuItem(item) {
      this.selectedItem = item;
    },
    goToSettings() {
      // Navigate to the settings page or trigger a modal
      alert("Going to Settings!");
    }
  }
};
</script>

<style scoped>
.container {
  display: flex;
}

.sidebar {
  width: 250px;
  background-color: #f0f0f0;
  padding: 20px;
}

.main-content {
  flex: 1;
  padding: 20px;
}

.menu {
  list-style-type: none;
  padding: 0;
}

.menu li {
  margin-bottom: 10px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content {
  margin-top: 20px;
}

.section {
  margin-bottom: 20px;
}
</style>
