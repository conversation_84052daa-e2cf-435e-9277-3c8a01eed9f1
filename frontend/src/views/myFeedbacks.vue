<template>
  <div>
    <div class="layout-header">
      <span>我的反馈</span>
      <el-button type="primary" @click="openFeedbackDialog">新增反馈</el-button>
    </div>

    <el-table :data="feedbacks" stripe>
      <el-table-column type="index" label="序号" width="100" />
      <el-table-column prop="feedback_content" label="反馈内容" />
      <el-table-column label="状态">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'answered' ? 'success' : 'warning'">
            {{ scope.row.status === 'answered' ? '已回复' : '待回复' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" type="primary" @click="viewFeedback(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增反馈弹窗 -->
    <el-dialog title="新增反馈" v-model="dialogVisible" width="500px">
      <el-input
          v-model="newFeedbackContent"
          type="textarea"
          placeholder="请输入您的反馈内容"
          rows="5"
          class="input-content"
      ></el-input>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitFeedback">提交</el-button>
      </div>
    </el-dialog>

    <!-- 优化后的反馈详情弹窗 -->
    <el-dialog title="反馈详情" v-model="detailVisible" width="500px">
      <div class="feedback-detail">
        <h3 class="detail-title">反馈详情</h3>
        <div class="feedback-section">
          <p><strong>反馈内容：</strong>{{ selectedFeedback?.feedback_content }}</p>
        </div>

        <div class="feedback-section">
          <p><strong>状态：</strong>
            <el-tag :type="selectedFeedback?.status === 'answered' ? 'success' : 'warning'">
              {{ selectedFeedback?.status === 'answered' ? '已回复' : '待回复' }}
            </el-tag>
          </p>
        </div>

        <div class="feedback-section">
          <p><strong>管理员回复：</strong></p>
          <p v-if="selectedFeedback?.response?.String" class="response-text">{{ selectedFeedback?.response?.String }}</p>
          <p v-else class="no-response">暂无回复</p>
        </div>
      </div>

      <div class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getMyFeedbacksApi, submitFeedbackApi, getFeedbackApi } from '@/api/feedback';
import { ElMessage } from 'element-plus';

const feedbacks = ref([]);
const dialogVisible = ref(false);
const detailVisible = ref(false);
const newFeedbackContent = ref('');
const selectedFeedback = ref(null);

// 加载用户反馈列表
const loadFeedbacks = async () => {
  const res = await getMyFeedbacksApi();
  feedbacks.value = res.data;
};

// 打开新增反馈弹窗
const openFeedbackDialog = () => {
  dialogVisible.value = true;
};

// 提交反馈
const submitFeedback = async () => {
  if (!newFeedbackContent.value.trim()) {
    ElMessage.error('反馈内容不能为空');
    return;
  }

  await submitFeedbackApi({
    feedback_content: newFeedbackContent.value.trim()
  });

  ElMessage.success('反馈提交成功');
  dialogVisible.value = false;
  newFeedbackContent.value = '';
  loadFeedbacks();
};

// 查看反馈详情
const viewFeedback = async (feedback) => {
  const res = await getFeedbackApi(feedback.id);
  selectedFeedback.value = res.data;
  detailVisible.value = true;
};

// 初始化加载反馈列表
onMounted(() => {
  loadFeedbacks();
});
</script>

<style scoped>


.feedback-detail {
  padding: 10px 0;
}

.feedback-detail .detail-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
  color: #333;
}

.feedback-section {
  margin-bottom: 12px;
}

.feedback-section p {
  margin: 0;
  font-size: 15px;
  color: #444;
}

.response-text {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 5px;
  color: #333;
  margin-top: 5px;
}

.no-response {
  color: #999;
  font-style: italic;
}

.dialog-footer {
  text-align: right;
  margin-top: 15px;
}
</style>
