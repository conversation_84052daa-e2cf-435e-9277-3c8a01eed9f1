// User.ts

// User 接口，表示用户数据
export interface User {
    id: number;               // 用户 ID
    name: string;             // 用户名
    password: string;         // 密码
    email: string;            // 邮箱
    role: string;             // 角色
    is_enabled: number;       // 是否启用，1为启用，0为禁用
}

// Register 接口，表示注册时的数据结构
export interface Register {
    username: string;         // 用户名
    password: string;         // 密码
    password2: string;        // 确认密码
    email: string;            // 邮箱
}
