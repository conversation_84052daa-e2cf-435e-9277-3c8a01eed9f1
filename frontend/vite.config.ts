import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import VueSetupExtend from 'vite-plugin-vue-setup-extend';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

export default defineConfig({
	base: './',
	// base: '/ai/',
	plugins: [
		vue(),
		VueSetupExtend(),
		AutoImport({
			resolvers: [ElementPlusResolver()]
		}),
		Components({
			resolvers: [ElementPlusResolver()]
		})
	],
	optimizeDeps: {
		include: ['schart.js']
	},
	resolve: {
		alias: {
			'@': '/src',
			'~': '/src/assets'
		}
	},
	define: {
		__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: "true",
	},
	build: {
		outDir: 'dist',
		assetsDir: 'static',
		terserOptions: {
			compress: {
				// 移除 console 语句（可以避免 ESLint 对 console 的规则检查）
				drop_console: true,
				// 移除 debugger 语句（可以避免 ESLint 对 debugger 的规则检查）
				drop_debugger: true
			}
		},
		rollupOptions: {
			output: {
				// 使用手动分块来控制哪些模块打包在一起
				manualChunks(id) {
					if (id.includes('node_modules')) {
						return 'vendor';
					}
					// 可以根据自己的需求添加更多的分块逻辑
					// 例如，将所有视图组件打包到一个单独的块中
					if (id.includes('src/views')) {
						return 'views';
					}
				},
				entryFileNames: `static/[name].[hash].js`,
				chunkFileNames: `static/[name].[hash].js`,
				assetFileNames: `static/[name].[hash].[ext]`
			},
			external: (id) => {
				// 示例：跳过 src/views 目录下的文件检查
				// if (id.includes('src/views')) {
				// 	return true;
				// }
				// return false;
			}
		},
		// 配置缓存破坏策略，确保在文件内容变化时更新哈希
		cache: false,
		// 强制在每次构建时重新生成所有块
		minify: 'terser',
		// 确保使用 terser 进行代码压缩
		sourcemap: false
		// 生产环境可以关闭 sourcemap 以减小文件大小
	},
	server: {
		port: 3000
		// proxy: {
		// 	// 👇 所有 /admin/api 开头的请求，都会被应用到后端服务器
		// 	'/admin/api': {
		// 		target: 'http://localhost', // 你的后端地址
		// 		changeOrigin: true,
		// 		rewrite: path => path.replace(/^\/admin\/api/, '/admin/api') // 保持原始路径
		// 	}
		// }
	},
});
