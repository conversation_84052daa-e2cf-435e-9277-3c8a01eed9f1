{"name": "vue-manage-system", "version": "5.5.0", "private": true, "scripts": {"dev": "vite", "build:dev": "vite build --mode dev", "build:prod": "vite build --mode prod", "build:docker": "vite build --mode docker", "serve:dev": "vite preview --mode dev", "serve:prod": "vite preview --mode prod", "serve:docker": "vite preview --mode docker"}, "dependencies": {"@chevrotain/regexp-to-ast": "^11.0.3", "@element-plus/icons-vue": "*", "@vueuse/core": "^13.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.3", "countup.js": "^2.8.0", "dayjs": "^1.11.13", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.6.3", "less": "^4.2.2", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "marked": "^15.0.11", "md-editor-v3": "^2.11.2", "moment": "^2.30.1", "nprogress": "^0.2.0", "pdfjs-dist": "^5.1.91", "pinia": "^2.1.7", "vue": "^3.5.13", "vue-cropper": "1.1.1", "vue-echarts": "^6.6.9", "vue-router": "^4.2.5", "vue-schart": "^2.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.0", "@vue/compiler-sfc": "^3.1.2", "autoprefixer": "10", "html-webpack-plugin": "^5.6.3", "typescript": "^4.6.4", "unplugin-auto-import": "^0.11.2", "unplugin-vue-components": "^0.22.4", "vite": "^3.2.11", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^0.38.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}