# 第一阶段：构建前端静态文件
# guergeiro/pnpm:20-8
FROM  registry.cn-hangzhou.aliyuncs.com/docker_test_xjy/difyserver:pnpm-20-8 AS frontend-builder
WORKDIR /app

# 复制前端代码
COPY frontend/package*.json ./
# 设置 npm 镜像源为腾讯云镜像（可根据需要修改）
RUN npm config set registry https://mirrors.cloud.tencent.com/npm/

# 安装项目依赖
RUN npm install --legacy-peer-deps
COPY frontend/ ./

# 构建前端
RUN pnpm run build:prod

# 第二阶段：构建后端 Go 应用
FROM registry.cn-hangzhou.aliyuncs.com/docker_test_xjy/difyserver:go-1.23 AS backend-builder
WORKDIR /app

# 设置 Go 环境变量
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=0


# 复制源代码
COPY . .

RUN go mod tidy

# 构建 Go 应用（根据构建参数设置架构）
ARG TARGETARCH
RUN GOARCH=$TARGETARCH go build -o difyserver

# 第三阶段：创建最终镜像
FROM  registry.cn-hangzhou.aliyuncs.com/docker_test_xjy/difyserver:alpine-3.18
WORKDIR /app

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 从前一阶段复制编译好的应用
COPY --from=backend-builder /app/difyserver /app/

# 从前一阶段复制前端构建文件
COPY --from=frontend-builder /app/dist /app/frontend/build

# 复制配置文件
COPY config.yaml /app/
COPY resource /app/resource
COPY templates /app/templates

# 设置工作目录
WORKDIR /app

# 暴露端口
EXPOSE 8080

# 设置入口点
ENTRYPOINT ["/app/difyserver"]
