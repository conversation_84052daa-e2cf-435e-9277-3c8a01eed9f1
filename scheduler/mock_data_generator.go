package scheduler

import (
	"crypto/rand"
	"fmt"
	"log/slog"
	"math/big"
	"time"

	"difyserver/config"
	"difyserver/database"
	"difyserver/models"
	"difyserver/services"
	"difyserver/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MockDataGenerator 模拟数据生成器
type MockDataGenerator struct {
	config         *config.Config
	holidayService *services.HolidayService
}

// NewMockDataGenerator 创建新的模拟数据生成器
func NewMockDataGenerator(cfg *config.Config) *MockDataGenerator {
	return &MockDataGenerator{
		config:         cfg,
		holidayService: services.NewHolidayService(),
	}
}

// GenerateDailyMockData 生成每日模拟数据（使用当前日期）
func (m *MockDataGenerator) GenerateDailyMockData() error {
	return m.GenerateMockDataForDate(time.Now())
}

// GenerateMockDataForDate 为指定日期生成模拟数据
func (m *MockDataGenerator) GenerateMockDataForDate(targetDate time.Time) error {
	if !m.config.MockData.Enabled {
		slog.Info("模拟数据生成已禁用")
		return nil
	}

	dateStr := targetDate.Format("2006-01-02")

	// 检查是否为工作日
	if !m.holidayService.IsWorkingDay(targetDate) {
		slog.Info("指定日期非工作日，跳过模拟数据生成", "date", dateStr)
		return nil
	}

	slog.Info("开始生成指定日期的模拟数据", "date", dateStr)

	// 1. 确保模拟用户存在
	mockUser, err := m.ensureMockUserExists()
	if err != nil {
		slog.Error("确保模拟用户存在失败", "error", err)
		return err
	}

	// 2. 获取第一个租户
	firstTenant, err := m.getFirstTenant()
	if err != nil {
		slog.Error("获取第一个租户失败", "error", err)
		return err
	}

	// 3. 确保用户与租户关联
	err = m.ensureUserTenantRelation(mockUser.ID, firstTenant.ID)
	if err != nil {
		slog.Error("确保用户租户关联失败", "error", err)
		return err
	}

	// 4. 获取所有应用
	apps, err := m.getAllApps(firstTenant.ID)
	if err != nil {
		slog.Error("获取所有应用失败", "error", err)
		return err
	}

	// 5. 选择要生成数据的应用（按配置的百分比）
	selectedApps := m.selectAppsForMockData(apps)
	slog.Info("选择应用生成模拟数据", "date", dateStr, "total_apps", len(apps), "selected_apps", len(selectedApps))

	// 6. 为选中的应用生成点击和消息数据
	for _, app := range selectedApps {
		err = m.generateAppMockDataForDate(mockUser, firstTenant, app, targetDate)
		if err != nil {
			slog.Error("为应用生成模拟数据失败", "app_id", app.Id, "app_name", app.Name, "date", dateStr, "error", err)
			continue
		}
	}

	slog.Info("指定日期模拟数据生成完成", "date", dateStr, "processed_apps", len(selectedApps))
	return nil
}

// ensureMockUserExists 确保模拟用户存在，如果不存在则创建
func (m *MockDataGenerator) ensureMockUserExists() (*models.Account, error) {
	accountDao := database.GetAccountDao()

	// 尝试通过邮箱查找用户
	var existingUser models.Account
	err := accountDao.DB.Where("email = ?", m.config.MockData.MockEmail).First(&existingUser).Error

	if err == nil {
		// 用户已存在
		slog.Info("模拟用户已存在", "email", m.config.MockData.MockEmail, "id", existingUser.ID)
		return &existingUser, nil
	}

	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 用户不存在，创建新用户

	// 使用系统相同的加密逻辑
	//hashedPassword, base64Salt := utils.HashPasswordWithSalt("admin@mock123", salt)
	password, salt, err := utils.GetPassword("admin@mock123")

	newUser := models.Account{
		ID:                uuid.New().String(),
		Name:              m.config.MockData.MockName,
		Email:             m.config.MockData.MockEmail,
		Password:          password,
		PasswordSalt:      salt,
		InterfaceLanguage: "zh-Hans",
		InterfaceTheme:    "light",
		Timezone:          "Asia/Shanghai",
		Status:            models.ACTIVE,
		Department:        "无",
		Unit:              "无",
	}

	err = accountDao.DB.Create(&newUser).Error
	if err != nil {
		return nil, fmt.Errorf("创建模拟用户失败: %w", err)
	}

	slog.Info("创建模拟用户成功", "email", newUser.Email, "id", newUser.ID)
	return &newUser, nil
}

// getFirstTenant 获取第一个租户
func (m *MockDataGenerator) getFirstTenant() (*models.Tenant, error) {
	tenant := new(models.Tenant)
	err := database.DB.Where("status=?", "normal").Order("created_at ASC").First(tenant).Error
	if err != nil {
		return nil, fmt.Errorf("获取第一个租户失败: %w", err)
	}
	return tenant, nil
}

// ensureUserTenantRelation 确保用户与租户的关联关系
func (m *MockDataGenerator) ensureUserTenantRelation(userID, tenantID string) error {
	var relation models.TenantAccountJoin
	err := database.DB.Where("account_id = ? AND tenant_id = ?", userID, tenantID).First(&relation).Error

	if err == nil {
		// 关联已存在
		return nil
	}

	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询用户租户关联失败: %w", err)
	}

	// 创建新的关联关系
	newRelation := models.TenantAccountJoin{
		ID:        uuid.New().String(),
		TenantID:  tenantID,
		AccountID: userID,
		Role:      "normal",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Current:   true,
	}

	err = database.DB.Create(&newRelation).Error
	if err != nil {
		return fmt.Errorf("创建用户租户关联失败: %w", err)
	}

	slog.Info("创建用户租户关联成功", "user_id", userID, "tenant_id", tenantID)
	return nil
}

// getAllApps 获取所有应用
func (m *MockDataGenerator) getAllApps(uuid string) ([]models.Apps, error) {
	var apps []models.Apps
	err := database.DB.Where("tenant_id=?", uuid).Where("mode!=?", "workflow").Find(&apps).Error
	if err != nil {
		return nil, fmt.Errorf("获取所有应用失败: %w", err)
	}
	return apps, nil
}

// selectAppsForMockData 根据配置的百分比选择要生成模拟数据的应用
func (m *MockDataGenerator) selectAppsForMockData(apps []models.Apps) []models.Apps {
	if len(apps) == 0 {
		return apps
	}

	// 计算要选择的应用数量
	selectedCount := int(float64(len(apps)) * m.config.MockData.AppPercentage)
	if selectedCount == 0 && len(apps) > 0 {
		selectedCount = 1 // 至少选择一个应用
	}

	// 随机选择应用
	selected := make([]models.Apps, 0, selectedCount)
	used := make(map[int]bool)

	for len(selected) < selectedCount && len(selected) < len(apps) {
		// 生成随机索引
		randIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(apps))))
		if err != nil {
			slog.Error("生成随机数失败", "error", err)
			break
		}

		index := int(randIndex.Int64())
		if !used[index] {
			used[index] = true
			selected = append(selected, apps[index])
		}
	}

	return selected
}

// generateAppMockData 为指定应用生成模拟数据（使用当前日期）
func (m *MockDataGenerator) generateAppMockData(user *models.Account, tenant *models.Tenant, app models.Apps) error {
	return m.generateAppMockDataForDate(user, tenant, app, time.Now())
}

// generateAppMockDataForDate 为指定应用和日期生成模拟数据
func (m *MockDataGenerator) generateAppMockDataForDate(user *models.Account, tenant *models.Tenant, app models.Apps, targetDate time.Time) error {
	// 生成随机点击数
	clickCount, err := m.generateRandomCount(m.config.MockData.MinClickCount, m.config.MockData.MaxClickCount)
	if err != nil {
		return fmt.Errorf("生成随机点击数失败: %w", err)
	}

	// 生成随机消息数
	messageCount, err := m.generateRandomCount(m.config.MockData.MinMessageCount, m.config.MockData.MaxMessageCount)
	if err != nil {
		return fmt.Errorf("生成随机消息数失败: %w", err)
	}

	// 生成点击统计数据
	if clickCount > 0 {
		err = m.generateClickStatisticsForDate(user, tenant, app, clickCount, targetDate)
		if err != nil {
			return fmt.Errorf("生成点击统计失败: %w", err)
		}
	}

	// 生成消息数据
	if messageCount > 0 {
		err = m.generateMessageDataForDate(user, app, messageCount, targetDate)
		if err != nil {
			return fmt.Errorf("生成消息数据失败: %w", err)
		}
	}

	slog.Info("为应用生成模拟数据成功",
		"app_id", app.Id,
		"app_name", app.Name,
		"date", targetDate.Format("2006-01-02"),
		"click_count", clickCount,
		"message_count", messageCount)

	return nil
}

// generateRandomCount 生成指定范围内的随机数
func (m *MockDataGenerator) generateRandomCount(min, max int) (int, error) {
	if min > max {
		min, max = max, min
	}

	if min == max {
		return min, nil
	}

	randNum, err := rand.Int(rand.Reader, big.NewInt(int64(max-min+1)))
	if err != nil {
		return 0, err
	}

	return min + int(randNum.Int64()), nil
}

// generateClickStatistics 生成点击统计数据（使用当前日期）
func (m *MockDataGenerator) generateClickStatistics(user *models.Account, tenant *models.Tenant, app models.Apps, clickCount int) error {
	return m.generateClickStatisticsForDate(user, tenant, app, clickCount, time.Now())
}

// generateClickStatisticsForDate 为指定日期生成点击统计数据
func (m *MockDataGenerator) generateClickStatisticsForDate(user *models.Account, tenant *models.Tenant, app models.Apps, clickCount int, targetDate time.Time) error {
	targetDateOnly, err := time.Parse("2006-01-02", targetDate.Format("2006-01-02"))
	if err != nil {
		return fmt.Errorf("解析日期失败: %w", err)
	}

	// 解析UUID
	userUUID, err := uuid.Parse(user.ID)
	if err != nil {
		return fmt.Errorf("解析用户UUID失败: %w", err)
	}

	tenantUUID, err := uuid.Parse(tenant.ID)
	if err != nil {
		return fmt.Errorf("解析租户UUID失败: %w", err)
	}

	// 检查指定日期是否已经有该应用的点击统计
	var existingStat models.AnchorStatistics
	err = database.DB.Where("user_id = ? AND tenant_id = ? AND resource_type = ? AND resource_id = ? AND action_type = ? AND record_date = ?",
		userUUID, tenantUUID, "app", app.Id, "click", targetDateOnly).First(&existingStat).Error

	if err == nil {
		// 已存在，更新计数
		existingStat.ActionCount += clickCount
		err = database.DB.Save(&existingStat).Error
		if err != nil {
			return fmt.Errorf("更新点击统计失败: %w", err)
		}
		slog.Info("更新点击统计", "app_id", app.Id, "new_count", existingStat.ActionCount)
		return nil
	}

	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询点击统计失败: %w", err)
	}

	// 不存在，创建新记录
	newStat := models.AnchorStatistics{
		UserID:       userUUID,
		TenantID:     tenantUUID,
		ResourceType: "app",
		ResourceID:   app.Id,
		ResourceName: app.Name,
		ActionType:   "click",
		ActionCount:  clickCount,
		RecordDate:   targetDateOnly,
		CreatedAt:    targetDate,
		UpdatedAt:    targetDate,
	}

	err = database.DB.Create(&newStat).Error
	if err != nil {
		return fmt.Errorf("创建点击统计失败: %w", err)
	}

	return nil
}

// generateMessageData 生成消息数据（使用当前日期）
func (m *MockDataGenerator) generateMessageData(user *models.Account, app models.Apps, messageCount int) error {
	return m.generateMessageDataForDate(user, app, messageCount, time.Now())
}

// generateMessageDataForDate 为指定日期生成消息数据
func (m *MockDataGenerator) generateMessageDataForDate(user *models.Account, app models.Apps, messageCount int, targetDate time.Time) error {
	// 首先创建一个对话
	conversation := models.Conversations{
		ID:                      uuid.New().String(),
		AppID:                   app.Id,
		Mode:                    "chat",
		Name:                    fmt.Sprintf("New Conversation %s", targetDate.Format("2006-01-02 15:04:05")),
		Inputs:                  "{}",
		SystemInstructionTokens: 0,
		Status:                  "normal",
		FromSource:              "api",
		InvokeFrom:              "web-app",
		CreatedAt:               targetDate,
		UpdatedAt:               targetDate,
	}

	err := database.DB.Create(&conversation).Error
	if err != nil {
		return fmt.Errorf("创建对话失败: %w", err)
	}

	// 为对话创建消息
	for i := 0; i < messageCount; i++ {
		messageTime := targetDate.Add(time.Duration(i) * time.Minute) // 每条消息间隔1分钟
		message := models.Messages{
			ID:                      uuid.New().String(),
			AppID:                   app.Id,
			ConversationID:          conversation.ID,
			Inputs:                  "{}",
			Query:                   fmt.Sprintf("query %d from %s", i+1, user.Name),
			Message:                 "{}",
			MessageTokens:           0,
			MessageUnitPrice:        0,
			Answer:                  fmt.Sprintf("<think>answer</think>answer %d", i+1),
			AnswerTokens:            0,
			AnswerUnitPrice:         0,
			ProviderResponseLatency: 0,
			Currency:                "USD",
			FromSource:              "api",
			InvokeFrom:              "web-app",
			CreatedAt:               messageTime,
			UpdatedAt:               messageTime,
		}

		err = database.DB.Create(&message).Error
		if err != nil {
			slog.Error("创建消息失败", "error", err, "message_index", i)
			continue
		}
	}

	return nil
}
