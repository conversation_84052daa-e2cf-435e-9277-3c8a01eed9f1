package scheduler

import (
	"log/slog"
	"time"

	"difyserver/config"
	"difyserver/services"

	"github.com/robfig/cron/v3"
)

// Scheduler 定时任务调度器
type Scheduler struct {
	cron           *cron.Cron
	generator      *MockDataGenerator
	holidayService *services.HolidayService
}

// NewScheduler 创建新的调度器
func NewScheduler(cfg *config.Config) *Scheduler {
	// 创建带有秒级精度的cron调度器
	c := cron.New(cron.WithSeconds())

	return &Scheduler{
		cron:           c,
		generator:      NewMockDataGenerator(cfg),
		holidayService: services.NewHolidayService(),
	}
}

// Start 启动调度器
func (s *Scheduler) Start() error {
	if !config.GlobalConfig.MockData.Enabled {
		slog.Info("模拟数据生成已禁用，跳过定时任务启动")
		return nil
	}

	// 添加每天凌晨6点的定时任务
	// Cron表达式: 秒 分 时 日 月 周
	// "0 0 6 * * *" 表示每天6点0分0秒执行
	_, err := s.cron.AddFunc("0 0 6 * * *", func() {
		today := time.Now()
		slog.Info("开始执行每日模拟数据生成任务", "date", today.Format("2006-01-02"))

		// 检查是否为工作日
		if !s.holidayService.IsWorkingDay(today) {
			slog.Info("今日非工作日，跳过模拟数据生成", "date", today.Format("2006-01-02"))
			return
		}

		err := s.generator.GenerateDailyMockData()
		if err != nil {
			slog.Error("每日模拟数据生成失败", "error", err)
		} else {
			slog.Info("每日模拟数据生成完成")
		}
	})

	if err != nil {
		return err
	}

	// 启动调度器
	s.cron.Start()
	slog.Info("定时任务调度器已启动，每天凌晨6点执行模拟数据生成")

	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	if s.cron != nil {
		s.cron.Stop()
		slog.Info("定时任务调度器已停止")
	}
}

// RunOnce 立即执行一次模拟数据生成（用于测试）
func (s *Scheduler) RunOnce() error {
	slog.Info("手动执行模拟数据生成")
	return s.generator.GenerateDailyMockData()
}

// GetNextRunTime 获取下次执行时间
func (s *Scheduler) GetNextRunTime() time.Time {
	if s.cron == nil {
		return time.Time{}
	}

	entries := s.cron.Entries()
	if len(entries) == 0 {
		return time.Time{}
	}

	return entries[0].Next
}
