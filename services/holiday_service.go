package services

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

// HolidayInfo 节假日信息
type HolidayInfo struct {
	Holiday bool   `json:"holiday"`
	Name    string `json:"name"`
	Wage    int    `json:"wage"`
	Date    string `json:"date"`
	Rest    int    `json:"rest"`
}

// HolidayResponse API响应结构
type HolidayResponse struct {
	Code     int                    `json:"code"`
	Holiday  map[string]HolidayInfo `json:"holiday"`
	Year     int                    `json:"year"`
	Message  string                 `json:"message"`
}

// HolidayService 节假日服务
type HolidayService struct {
	holidayCache map[string]HolidayInfo
	cacheYear    int
	apiURL       string
	localPath    string
}

// NewHolidayService 创建节假日服务
func NewHolidayService() *HolidayService {
	return &HolidayService{
		holidayCache: make(map[string]HolidayInfo),
		cacheYear:    0,
		apiURL:       "http://timor.tech/api/holiday/year",
		localPath:    "resource/holiday",
	}
}

// IsWorkingDay 检查指定日期是否为工作日
func (h *HolidayService) IsWorkingDay(date time.Time) bool {
	// 检查是否为周末
	weekday := date.Weekday()
	if weekday == time.Saturday || weekday == time.Sunday {
		slog.Info("日期为周末，跳过模拟数据生成", "date", date.Format("2006-01-02"), "weekday", weekday.String())
		return false
	}

	// 检查是否为节假日
	if h.isHoliday(date) {
		slog.Info("日期为节假日，跳过模拟数据生成", "date", date.Format("2006-01-02"))
		return false
	}

	return true
}

// isHoliday 检查是否为节假日
func (h *HolidayService) isHoliday(date time.Time) bool {
	year := date.Year()
	// 使用MM-dd格式查询，与缓存key格式保持一致
	dateKey := date.Format("01-02")

	// 如果缓存的年份不匹配，重新获取数据
	if h.cacheYear != year {
		// 优先尝试从API获取数据
		err := h.loadHolidayData(year)
		if err != nil {
			slog.Warn("从API获取节假日数据失败，尝试本地文件", "year", year, "error", err)
			// API失败，尝试从本地文件加载
			err = h.loadLocalHolidayData(year)
			if err != nil {
				slog.Error("从本地文件获取节假日数据失败，使用默认数据", "year", year, "error", err)
				// 本地文件也失败，使用默认数据
				h.loadDefaultHolidayData(year)
			}
		}
		h.cacheYear = year
	}

	// 检查是否为节假日（使用MM-dd格式的key）
	if holidayInfo, exists := h.holidayCache[dateKey]; exists {
		return holidayInfo.Holiday
	}

	return false
}

// loadHolidayData 从API获取节假日数据
func (h *HolidayService) loadHolidayData(year int) error {
	url := fmt.Sprintf("%s/%d", h.apiURL, year)
	
	slog.Info("正在获取节假日数据", "year", year, "url", url)

	// 设置超时时间
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 创建请求并设置请求头
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置User-Agent和其他请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Cache-Control", "no-cache")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求节假日API失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("节假日API返回错误状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应数据失败: %w", err)
	}

	var holidayResp HolidayResponse
	err = json.Unmarshal(body, &holidayResp)
	if err != nil {
		return fmt.Errorf("解析节假日数据失败: %w", err)
	}

	if holidayResp.Code != 0 {
		return fmt.Errorf("节假日API返回错误: %s", holidayResp.Message)
	}

	// 更新缓存，API返回的数据已经是MM-dd格式，直接使用
	h.holidayCache = holidayResp.Holiday
	slog.Info("节假日数据获取成功", "year", year, "holiday_count", len(h.holidayCache))

	return nil
}

// loadLocalHolidayData 从本地文件加载节假日数据
func (h *HolidayService) loadLocalHolidayData(year int) error {
	filename := fmt.Sprintf("%d.json", year)
	filePath := filepath.Join(h.localPath, filename)

	slog.Info("正在从本地文件加载节假日数据", "year", year, "file", filePath)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("本地节假日文件不存在: %s", filePath)
	}

	// 读取文件内容
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取本地节假日文件失败: %w", err)
	}

	// 解析JSON数据
	var holidayResp HolidayResponse
	err = json.Unmarshal(data, &holidayResp)
	if err != nil {
		return fmt.Errorf("解析本地节假日数据失败: %w", err)
	}

	if holidayResp.Code != 0 {
		return fmt.Errorf("本地节假日数据格式错误: %s", holidayResp.Message)
	}

	// 更新缓存，将MM-dd格式转换为YYYY-MM-DD格式
	h.holidayCache = make(map[string]HolidayInfo)
	for dateKey, holidayInfo := range holidayResp.Holiday {
		// 本地文件的日期格式也是"MM-dd"，需要转换为"YYYY-MM-DD"
		fullDate := fmt.Sprintf("%d-%s", year, dateKey)
		h.holidayCache[fullDate] = holidayInfo
	}

	slog.Info("本地节假日数据加载成功", "year", year, "holiday_count", len(h.holidayCache))
	return nil
}

// loadDefaultHolidayData 加载默认节假日数据（当API和本地文件都不可用时）
func (h *HolidayService) loadDefaultHolidayData(year int) {
	// 清空缓存
	h.holidayCache = make(map[string]HolidayInfo)

	// 2025年默认节假日数据（使用MM-dd格式作为key，与API保持一致）
	defaultHolidays2025 := map[string]HolidayInfo{
		"01-01": {Holiday: true, Name: "元旦", Date: "2025-01-01", Rest: 1},
		"01-28": {Holiday: true, Name: "春节", Date: "2025-01-28", Rest: 1},
		"01-29": {Holiday: true, Name: "春节", Date: "2025-01-29", Rest: 1},
		"01-30": {Holiday: true, Name: "春节", Date: "2025-01-30", Rest: 1},
		"01-31": {Holiday: true, Name: "春节", Date: "2025-01-31", Rest: 1},
		"02-01": {Holiday: true, Name: "春节", Date: "2025-02-01", Rest: 1},
		"02-02": {Holiday: true, Name: "春节", Date: "2025-02-02", Rest: 1},
		"02-03": {Holiday: true, Name: "春节", Date: "2025-02-03", Rest: 1},
		"04-05": {Holiday: true, Name: "清明节", Date: "2025-04-05", Rest: 1},
		"04-06": {Holiday: true, Name: "清明节", Date: "2025-04-06", Rest: 1},
		"04-07": {Holiday: true, Name: "清明节", Date: "2025-04-07", Rest: 1},
		"05-01": {Holiday: true, Name: "劳动节", Date: "2025-05-01", Rest: 1},
		"05-02": {Holiday: true, Name: "劳动节", Date: "2025-05-02", Rest: 1},
		"05-03": {Holiday: true, Name: "劳动节", Date: "2025-05-03", Rest: 1},
		"05-04": {Holiday: true, Name: "劳动节", Date: "2025-05-04", Rest: 1},
		"05-05": {Holiday: true, Name: "劳动节", Date: "2025-05-05", Rest: 1},
		"05-31": {Holiday: true, Name: "端午节", Date: "2025-05-31", Rest: 1},
		"06-02": {Holiday: true, Name: "端午节", Date: "2025-06-02", Rest: 1},
		"10-01": {Holiday: true, Name: "国庆节", Date: "2025-10-01", Rest: 1},
		"10-02": {Holiday: true, Name: "国庆节", Date: "2025-10-02", Rest: 1},
		"10-03": {Holiday: true, Name: "国庆节", Date: "2025-10-03", Rest: 1},
		"10-04": {Holiday: true, Name: "国庆节", Date: "2025-10-04", Rest: 1},
		"10-05": {Holiday: true, Name: "国庆节", Date: "2025-10-05", Rest: 1},
		"10-06": {Holiday: true, Name: "国庆节", Date: "2025-10-06", Rest: 1},
		"10-07": {Holiday: true, Name: "国庆节", Date: "2025-10-07", Rest: 1},
		"10-08": {Holiday: true, Name: "国庆节", Date: "2025-10-08", Rest: 1},
	}

	// 2024年默认节假日数据（使用MM-dd格式作为key，与API保持一致）
	defaultHolidays2024 := map[string]HolidayInfo{
		"01-01": {Holiday: true, Name: "元旦", Date: "2024-01-01", Rest: 1},
		"02-10": {Holiday: true, Name: "春节", Date: "2024-02-10", Rest: 1},
		"02-11": {Holiday: true, Name: "春节", Date: "2024-02-11", Rest: 1},
		"02-12": {Holiday: true, Name: "春节", Date: "2024-02-12", Rest: 1},
		"02-13": {Holiday: true, Name: "春节", Date: "2024-02-13", Rest: 1},
		"02-14": {Holiday: true, Name: "春节", Date: "2024-02-14", Rest: 1},
		"02-15": {Holiday: true, Name: "春节", Date: "2024-02-15", Rest: 1},
		"02-16": {Holiday: true, Name: "春节", Date: "2024-02-16", Rest: 1},
		"02-17": {Holiday: true, Name: "春节", Date: "2024-02-17", Rest: 1},
		"04-04": {Holiday: true, Name: "清明节", Date: "2024-04-04", Rest: 1},
		"04-05": {Holiday: true, Name: "清明节", Date: "2024-04-05", Rest: 1},
		"04-06": {Holiday: true, Name: "清明节", Date: "2024-04-06", Rest: 1},
		"05-01": {Holiday: true, Name: "劳动节", Date: "2024-05-01", Rest: 1},
		"05-02": {Holiday: true, Name: "劳动节", Date: "2024-05-02", Rest: 1},
		"05-03": {Holiday: true, Name: "劳动节", Date: "2024-05-03", Rest: 1},
		"05-04": {Holiday: true, Name: "劳动节", Date: "2024-05-04", Rest: 1},
		"05-05": {Holiday: true, Name: "劳动节", Date: "2024-05-05", Rest: 1},
		"06-10": {Holiday: true, Name: "端午节", Date: "2024-06-10", Rest: 1},
		"09-15": {Holiday: true, Name: "中秋节", Date: "2024-09-15", Rest: 1},
		"09-16": {Holiday: true, Name: "中秋节", Date: "2024-09-16", Rest: 1},
		"09-17": {Holiday: true, Name: "中秋节", Date: "2024-09-17", Rest: 1},
		"10-01": {Holiday: true, Name: "国庆节", Date: "2024-10-01", Rest: 1},
		"10-02": {Holiday: true, Name: "国庆节", Date: "2024-10-02", Rest: 1},
		"10-03": {Holiday: true, Name: "国庆节", Date: "2024-10-03", Rest: 1},
		"10-04": {Holiday: true, Name: "国庆节", Date: "2024-10-04", Rest: 1},
		"10-05": {Holiday: true, Name: "国庆节", Date: "2024-10-05", Rest: 1},
		"10-06": {Holiday: true, Name: "国庆节", Date: "2024-10-06", Rest: 1},
		"10-07": {Holiday: true, Name: "国庆节", Date: "2024-10-07", Rest: 1},
	}

	// 根据年份选择默认数据
	switch year {
	case 2024:
		h.holidayCache = defaultHolidays2024
	case 2025:
		h.holidayCache = defaultHolidays2025
	default:
		// 对于其他年份，只使用固定的节假日：元旦、劳动节、国庆节
		h.holidayCache = h.generateBasicHolidays(year)
	}

	slog.Info("使用默认节假日数据", "year", year, "holiday_count", len(h.holidayCache))
}

// generateBasicHolidays 为未知年份生成基础节假日（元旦、劳动节、国庆节）
func (h *HolidayService) generateBasicHolidays(year int) map[string]HolidayInfo {
	basicHolidays := make(map[string]HolidayInfo)

	// 元旦：每年1月1日（使用MM-dd格式作为key）
	newYearDate := fmt.Sprintf("%d-01-01", year)
	basicHolidays["01-01"] = HolidayInfo{
		Holiday: true,
		Name:    "元旦",
		Date:    newYearDate,
		Rest:    1,
	}

	// 劳动节：每年5月1日-5月5日（5天）
	for day := 1; day <= 5; day++ {
		laborDate := fmt.Sprintf("%d-05-%02d", year, day)
		laborKey := fmt.Sprintf("05-%02d", day)
		basicHolidays[laborKey] = HolidayInfo{
			Holiday: true,
			Name:    "劳动节",
			Date:    laborDate,
			Rest:    1,
		}
	}

	// 国庆节：每年10月1日-10月7日（7天）
	for day := 1; day <= 7; day++ {
		nationalDate := fmt.Sprintf("%d-10-%02d", year, day)
		nationalKey := fmt.Sprintf("10-%02d", day)
		basicHolidays[nationalKey] = HolidayInfo{
			Holiday: true,
			Name:    "国庆节",
			Date:    nationalDate,
			Rest:    1,
		}
	}

	slog.Info("生成基础节假日数据", "year", year, "holiday_count", len(basicHolidays), "holidays", "元旦、劳动节、国庆节")
	return basicHolidays
}

// GetHolidayInfo 获取指定日期的节假日信息
func (h *HolidayService) GetHolidayInfo(date time.Time) *HolidayInfo {
	year := date.Year()
	// 使用MM-dd格式查询，与缓存key格式保持一致
	dateKey := date.Format("01-02")

	// 确保数据已加载
	if h.cacheYear != year {
		err := h.loadHolidayData(year)
		if err != nil {
			// API失败，尝试本地文件
			err = h.loadLocalHolidayData(year)
			if err != nil {
				// 本地文件也失败，使用默认数据
				h.loadDefaultHolidayData(year)
			}
		}
		h.cacheYear = year
	}

	if holidayInfo, exists := h.holidayCache[dateKey]; exists {
		return &holidayInfo
	}

	return nil
}
