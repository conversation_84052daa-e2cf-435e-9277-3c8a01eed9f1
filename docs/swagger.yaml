basePath: /api
definitions:
  handlers.Question:
    properties:
      correct_answer:
        type: string
      explanation:
        type: string
      options:
        items:
          type: string
        type: array
      question:
        type: string
      question_id:
        type: string
      question_type:
        type: string
    type: object
  models.Account:
    properties:
      Avatar:
        type: string
      Department:
        type: string
      Email:
        type: string
      ID:
        type: string
      InterfaceLanguage:
        type: string
      InterfaceTheme:
        type: string
      Name:
        type: string
      Status:
        $ref: '#/definitions/models.AccountStatus'
      Timezone:
        type: string
      unit:
        type: string
    type: object
  models.AccountIDRequest:
    properties:
      id:
        type: string
    type: object
  models.AccountStatus:
    enum:
    - pending
    - uninitialized
    - active
    - banned
    - closed
    type: string
    x-enum-varnames:
    - PENDING
    - UNINITIALIZED
    - ACTIVE
    - BANNED
    - CLOSED
  models.AppStatistics:
    properties:
      app_id:
        type: string
      app_name:
        type: string
      click_count:
        type: integer
      message_count:
        type: integer
    type: object
  models.Apps:
    properties:
      api_rph:
        type: integer
      api_rpm:
        type: integer
      app_model_config_id:
        type: string
      created_at:
        type: string
      created_by:
        type: string
      description:
        type: string
      enable_api:
        type: boolean
      enable_site:
        type: boolean
      icon:
        type: string
      icon_background:
        type: string
      icon_type:
        type: string
      id:
        type: string
      is_demo:
        type: boolean
      is_public:
        type: boolean
      is_universal:
        type: boolean
      max_active_requests:
        type: integer
      mode:
        type: string
      name:
        type: string
      only_me:
        type: boolean
      status:
        type: string
      tenant_id:
        type: string
      tracing:
        type: string
      updated_at:
        type: string
      updated_by:
        type: string
      use_icon_as_answer_icon:
        type: boolean
      workflow_id:
        type: string
    type: object
  models.DashboardSummary:
    properties:
      anchor_statistics:
        items:
          $ref: '#/definitions/models.DateStatistics'
        type: array
      app_statistics:
        items:
          $ref: '#/definitions/models.AppStatistics'
        type: array
      message_statistics:
        items:
          $ref: '#/definitions/models.DateStatistics'
        type: array
      total_accounts:
        type: integer
      total_anchor_statistics:
        type: integer
      total_apps:
        type: integer
      total_data_sets:
        type: integer
      total_documents:
        type: integer
      total_messages:
        type: integer
    type: object
  models.Dataset:
    properties:
      collectionBindingID:
        type: string
      createdAt:
        type: string
      createdBy:
        type: string
      dataSourceType:
        type: string
      description:
        type: string
      embeddingModel:
        type: string
      embeddingModelProvider:
        type: string
      id:
        type: string
      indexStruct:
        type: string
      indexingTechnique:
        type: string
      name:
        type: string
      permission:
        type: string
      provider:
        type: string
      retrievalModel:
        type: string
      tenantID:
        type: string
      updatedAt:
        type: string
      updatedBy:
        type: string
    type: object
  models.DatasetTenantRequest:
    properties:
      dataset_id:
        type: string
      tenant_id:
        type: string
    type: object
  models.DateStatistics:
    properties:
      count:
        type: integer
      date:
        type: string
    type: object
  models.DeleteTenantAccount:
    properties:
      id:
        type: string
    type: object
  models.Feedback:
    properties:
      created_at:
        description: Nullable timestamp
        type: string
      feedback_content:
        type: string
      id:
        type: string
      response:
        allOf:
        - $ref: '#/definitions/sql.NullString'
        description: Nullable text
      status:
        $ref: '#/definitions/models.FeedbackStatus'
      tenant_id:
        type: string
      updated_at:
        allOf:
        - $ref: '#/definitions/sql.NullTime'
        description: Nullable timestamp
      user_id:
        type: string
    type: object
  models.FeedbackStatus:
    enum:
    - pending
    - answered
    type: string
    x-enum-varnames:
    - FeedbackStatusPending
    - FeedbackStatusAnswered
  models.LoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    type: object
  models.PageResponse:
    properties:
      data: {}
      page:
        description: 当前页
        type: integer
      page_size:
        description: 每页大小
        type: integer
      total:
        description: 总记录数
        type: integer
      total_pages:
        description: 总页数
        type: integer
    type: object
  models.SetPasswordRequest:
    properties:
      id:
        type: string
      password:
        type: string
    type: object
  models.SystemMenu:
    properties:
      created_at:
        type: string
      icon:
        type: string
      id:
        type: integer
      index:
        type: string
      permiss:
        type: string
      permission_id:
        type: integer
      pid:
        type: integer
      title:
        type: string
      updated_at:
        type: string
    type: object
  models.Tenant:
    properties:
      createdAt:
        type: string
      customConfig:
        type: string
      encryptPublicKey:
        type: string
      id:
        type: string
      name:
        type: string
      plan:
        $ref: '#/definitions/models.TenantPlan'
      status:
        type: string
      updatedAt:
        type: string
    type: object
  models.TenantAccountRequest:
    properties:
      account_id:
        type: string
      role:
        type: string
      tenant_id:
        type: string
    type: object
  models.TenantPlan:
    enum:
    - basic
    - pro
    - enterprise
    type: string
    x-enum-varnames:
    - TenantPlanBasic
    - TenantPlanPro
    - TenantPlanEnterprise
  models.UpdateTenantAccountRole:
    properties:
      id:
        type: string
      role:
        type: string
    type: object
  models.UserDocumentListResponse:
    properties:
      created_at:
        type: string
      created_by:
        type: string
      doc_type:
        type: string
      id:
        type: integer
      updated_at:
        type: string
    type: object
  models.UserDocumentRequest:
    properties:
      content:
        example: 这是用户手册的内容...
        type: string
      doc_type:
        example: user_manual
        type: string
    required:
    - content
    - doc_type
    type: object
  models.UserDocumentResponse:
    properties:
      content:
        type: string
      created_at:
        type: string
      created_by:
        type: string
      doc_type:
        type: string
      id:
        type: integer
      updated_at:
        type: string
    type: object
  models.UserDocumentUpdateByIDRequest:
    properties:
      content:
        example: 这是更新后的内容...
        type: string
    required:
    - content
    type: object
  sql.NullString:
    properties:
      string:
        type: string
      valid:
        description: Valid is true if String is not NULL
        type: boolean
    type: object
  sql.NullTime:
    properties:
      time:
        type: string
      valid:
        description: Valid is true if Time is not NULL
        type: boolean
    type: object
  view.AccountView:
    properties:
      Avatar:
        type: string
      Department:
        type: string
      Email:
        type: string
      ID:
        type: string
      InterfaceLanguage:
        type: string
      InterfaceTheme:
        type: string
      Name:
        type: string
      Status:
        $ref: '#/definitions/models.AccountStatus'
      Timezone:
        type: string
      firstTenantAccountID:
        type: string
      firstTenantID:
        type: string
      firstTenantRole:
        type: string
      unit:
        type: string
    type: object
  view.AddTenantParam:
    properties:
      name:
        type: string
      user_id:
        type: string
    type: object
  view.AnalysisAnchorStatisticsListResponse:
    properties:
      action_type:
        type: string
      data:
        items:
          $ref: '#/definitions/view.AnalysisAnchorStatisticsResponse'
        type: array
      resource_type:
        type: string
      step_days:
        type: integer
      summary:
        $ref: '#/definitions/view.AnalysisAnchorStatisticsSummary'
      total_days:
        type: integer
    type: object
  view.AnalysisAnchorStatisticsResponse:
    properties:
      action_type:
        description: 操作类型
        type: string
      end_date:
        description: 结束日期
        type: string
      resource_type:
        description: 资源类型
        type: string
      start_date:
        description: 开始日期
        type: string
      total_count:
        description: 总操作次数
        type: integer
      user_count:
        description: 用户数
        type: integer
    type: object
  view.AnalysisAnchorStatisticsSummary:
    properties:
      average_per_period:
        description: 每个周期平均次数
        type: number
      max_period_count:
        description: 单个周期最大次数
        type: integer
      min_period_count:
        description: 单个周期最小次数
        type: integer
      total_count:
        description: 总操作次数
        type: integer
      total_user_count:
        description: 总用户数
        type: integer
    type: object
  view.AnchorStatisticsAggregateListResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/view.AnchorStatisticsAggregateResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  view.AnchorStatisticsAggregateResponse:
    properties:
      action_type:
        description: 操作类型
        type: string
      period:
        description: 时间周期 (2024-01-01, 2024-01, 2024)
        type: string
      resource_id:
        description: 资源ID
        type: string
      resource_name:
        description: 资源名称
        type: string
      resource_type:
        description: 资源类型
        type: string
      total_count:
        description: 总次数
        type: integer
      user_count:
        description: 用户数
        type: integer
    type: object
  view.AnchorStatisticsListResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/view.AnchorStatisticsResponse'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  view.AnchorStatisticsResponse:
    properties:
      action_count:
        type: integer
      action_type:
        type: string
      created_at:
        type: string
      id:
        type: integer
      record_date:
        type: string
      resource_id:
        type: string
      resource_name:
        type: string
      resource_type:
        type: string
      tenant_id:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
      user_name:
        type: string
    type: object
  view.AppsSimpleView:
    properties:
      app_id:
        type: string
      enable_site:
        type: boolean
      name:
        type: string
      only_me:
        type: boolean
      status:
        type: string
    type: object
  view.AuthRefreshTokenParam:
    properties:
      refresh_token:
        type: string
    type: object
  view.CreateAccountParam:
    properties:
      department:
        type: string
      email:
        type: string
      name:
        type: string
      password:
        type: string
      tenant_id:
        type: string
      unit:
        type: string
    type: object
  view.CreateFeedbackRequest:
    properties:
      feedback_content:
        type: string
    required:
    - feedback_content
    type: object
  view.CurrentTenant:
    properties:
      created_at:
        type: integer
      custom_config: {}
      id:
        type: string
      in_trial: {}
      name:
        type: string
      plan:
        type: string
      role:
        type: string
      status:
        type: string
      trial_end_reason: {}
    type: object
  view.DelTenantParam:
    properties:
      tenant_id:
        type: string
    type: object
  view.DifyError:
    properties:
      code:
        type: string
      message:
        type: string
      status:
        type: integer
    type: object
  view.FeedbackResponse:
    properties:
      created_at:
        type: string
      feedback_content:
        type: string
      id:
        type: integer
      response:
        type: string
      status:
        type: string
      tenant_id:
        type: integer
      updated_at:
        type: string
      user_id:
        type: integer
      user_name:
        type: string
    type: object
  view.RecordAnchorRequest:
    properties:
      action_type:
        example: click
        type: string
      resource_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      resource_type:
        example: app
        type: string
    required:
    - action_type
    - resource_id
    - resource_type
    type: object
  view.RecordAnchorResponse:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  view.ResourceSummaryAnchorStatisticsItem:
    properties:
      action_type:
        description: 操作类型
        type: string
      resource_id:
        description: 资源ID
        type: string
      resource_name:
        description: 资源名称
        type: string
      resource_type:
        description: 资源类型
        type: string
      total_count:
        description: 总操作次数
        type: integer
      user_count:
        description: 用户数
        type: integer
    type: object
  view.ResourceSummaryAnchorStatisticsResponse:
    additionalProperties:
      $ref: '#/definitions/view.ResourceSummaryAnchorStatisticsItem'
    type: object
  view.RespondFeedbackRequest:
    properties:
      response:
        type: string
    required:
    - response
    type: object
  view.Result:
    properties:
      data: {}
      result:
        type: string
    type: object
  view.SimpleAccount:
    properties:
      department:
        type: string
      email:
        type: string
      id:
        type: string
      name:
        type: string
      unit:
        type: string
    type: object
  view.SimpleTenant:
    properties:
      id:
        type: string
      name:
        type: string
    type: object
  view.TempDataCreateRequest:
    properties:
      content:
        example: 这是临时数据内容
        type: string
    required:
    - content
    type: object
  view.TempDataCreateResponse:
    properties:
      message:
        type: string
      uuid:
        type: string
    type: object
  view.TempDataResponse:
    properties:
      content:
        type: string
      created_at:
        type: string
      uuid:
        type: string
    type: object
  view.TenantAccountParam:
    properties:
      tenant_id:
        type: string
    type: object
  view.TenantAccountView:
    properties:
      accountID:
        type: string
      accountName:
        type: string
      createdAt:
        type: string
      department:
        type: string
      id:
        type: string
      invitedBy:
        type: string
      role:
        type: string
      tenantID:
        type: string
      tenantName:
        type: string
      unit:
        type: string
      updatedAt:
        type: string
    type: object
  view.UpdateAccountInfoParam:
    properties:
      department:
        type: string
      id:
        type: string
      name:
        type: string
      unit:
        type: string
    type: object
  view.UpdateAppOnlyMeStatusParam:
    properties:
      app_id:
        type: string
      only_me:
        type: boolean
    type: object
  view.UpdateTenantNameParam:
    properties:
      name:
        type: string
      tenant_id:
        type: string
    type: object
  view.UpdateTenantStatusParam:
    properties:
      status:
        type: string
      tenant_id:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.example.com/support
  description: Dify Server 的 API 文档
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Dify Server API
  version: "1.0"
paths:
  /accounts.json:
    get:
      consumes:
      - application/json
      description: 获取系统中的账户列表，支持分页和按姓名、邮箱过滤
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 每页长度
        in: query
        name: size
        type: integer
      - description: 按姓名过滤（模糊匹配）
        in: query
        name: name
        type: string
      - description: 按邮箱过滤（模糊匹配）
        in: query
        name: email
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取账户列表
      tags:
      - 账户管理
  /add_account.json:
    post:
      consumes:
      - application/json
      description: 创建一个新的系统账户
      parameters:
      - description: 账户信息
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/view.CreateAccountParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 添加新账户
      tags:
      - 账户管理
  /add_dataset_tenant.json:
    post:
      consumes:
      - application/json
      description: 创建数据集与工作空间的关联关系
      parameters:
      - description: 数据集与工作空间关联信息
        in: body
        name: datasetTenant
        required: true
        schema:
          $ref: '#/definitions/models.DatasetTenantRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 添加数据集与工作空间的关联
      tags:
      - 数据集管理
  /add_tenant.json:
    post:
      consumes:
      - application/json
      description: 创建一个新的团队
      parameters:
      - description: 团队信息
        in: body
        name: tenant
        required: true
        schema:
          $ref: '#/definitions/view.AddTenantParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 添加新团队
      tags:
      - 团队管理
  /add_tenant_account.json:
    post:
      consumes:
      - application/json
      description: 创建团队与账户的关联关系
      parameters:
      - description: 团队账户关联信息
        in: body
        name: tenantAccount
        required: true
        schema:
          $ref: '#/definitions/models.TenantAccountRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 添加团队账户关联
      tags:
      - 团队账户管理
  /anchor-statistics.json:
    get:
      consumes:
      - application/json
      description: 获取锚点统计数据列表（分页）
      parameters:
      - description: 资源类型
        in: query
        name: resource_type
        type: string
      - description: 资源ID
        in: query
        name: resource_id
        type: string
      - description: 操作类型
        in: query
        name: action_type
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: string
      - description: 开始日期 (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: 结束日期 (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取锚点统计列表
      tags:
      - 锚点统计
  /anchor-statistics/{id}/info.json:
    get:
      consumes:
      - application/json
      description: 根据ID获取单条锚点统计记录详情
      parameters:
      - description: 统计记录ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取锚点统计详情
      tags:
      - 锚点统计
  /anchor-statistics/aggregate.json:
    get:
      consumes:
      - application/json
      description: 获取按时间周期聚合的锚点统计数据
      parameters:
      - description: 资源类型
        in: query
        name: resource_type
        type: string
      - description: 资源ID
        in: query
        name: resource_id
        type: string
      - description: 操作类型
        in: query
        name: action_type
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: string
      - description: 开始日期 (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: 结束日期 (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      - default: day
        description: 聚合维度 (day/month/year)
        in: query
        name: group_by
        type: string
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页大小
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取锚点统计聚合数据
      tags:
      - 锚点统计
  /anchor-statistics/analysis.json:
    get:
      consumes:
      - application/json
      description: 获取过去N天某个资源类型和操作类型的操作次数，按指定步长汇总
      parameters:
      - description: 资源类型
        in: query
        name: resource_type
        required: true
        type: string
      - description: 资源ID（可选，不指定则统计该类型下所有资源）
        in: query
        name: resource_id
        type: string
      - description: 操作类型
        in: query
        name: action_type
        required: true
        type: string
      - description: 过去天数 (1-365)
        in: query
        name: days
        required: true
        type: integer
      - description: 汇总步长天数 (1-30)
        in: query
        name: step
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取锚点统计分析数据
      tags:
      - 锚点统计
  /anchor-statistics/record.json:
    post:
      consumes:
      - application/json
      description: 记录用户对某个资源的操作统计
      parameters:
      - description: 锚点统计记录
        in: body
        name: anchor
        required: true
        schema:
          $ref: '#/definitions/view.RecordAnchorRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 记录锚点统计
      tags:
      - 锚点统计
  /anchor-statistics/resource-summary.json:
    get:
      consumes:
      - application/json
      description: 获取指定资源类型和操作类型下所有资源的统计汇总，以resource_id为key返回
      parameters:
      - description: 资源类型
        in: query
        name: resource_type
        required: true
        type: string
      - description: 操作类型
        in: query
        name: action_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取资源汇总统计数据
      tags:
      - 锚点统计
  /api/quiz/questions:
    get:
      consumes:
      - application/json
      description: 返回答题系统的题目数据
      produces:
      - application/json
      responses:
        "200":
          description: 题目列表
          schema:
            items:
              $ref: '#/definitions/handlers.Question'
            type: array
      summary: 获取题目数据
      tags:
      - Quiz
  /apps/status.json:
    get:
      consumes:
      - application/json
      description: 获取应用状态
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取应用状态
      tags:
      - 应用管理
  /apps/update_only_me_status.json:
    post:
      consumes:
      - application/json
      description: 更新指定应用的OnlyMe状态
      parameters:
      - description: 更新应用OnlyMe状态参数
        in: body
        name: app
        required: true
        schema:
          $ref: '#/definitions/view.UpdateAppOnlyMeStatusParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 更新应用OnlyMe状态
      tags:
      - 应用管理
  /current_tenant.json:
    get:
      consumes:
      - application/json
      description: 获取当前团队信息
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取当前团队信息
      tags:
      - 团队管理
      - 认证
  /dashboard/summary:
    get:
      consumes:
      - application/json
      description: 获取Dashboard总览数据，包括总量统计、按日期统计的数据和每个app的点击数及对话消息数统计（全局统计，不按租户隔离）
      parameters:
      - default: daily
        description: 统计类型：daily(按日) 或 monthly(按月)
        in: query
        name: stat_type
        type: string
      - description: 开始日期 YYYY-MM-DD
        in: query
        name: start_date
        type: string
      - description: 结束日期 YYYY-MM-DD
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功获取Dashboard数据
          schema:
            $ref: '#/definitions/models.DashboardSummary'
        "400":
          description: 参数错误
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: 获取Dashboard总览数据
      tags:
      - 首页Dashboard
  /datasets.json:
    get:
      consumes:
      - application/json
      description: 获取系统中的数据集列表，支持分页
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取数据集列表
      tags:
      - 数据集管理
  /del_account.json:
    post:
      consumes:
      - application/json
      description: 删除指定账户及其相关关联
      parameters:
      - description: 账户ID信息
        in: body
        name: accountInfo
        required: true
        schema:
          $ref: '#/definitions/models.AccountIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 删除账户
      tags:
      - 账户管理
  /del_dataset_tenant.json:
    post:
      consumes:
      - application/json
      description: 删除数据集与工作空间的关联关系
      parameters:
      - description: 数据集与工作空间关联信息
        in: body
        name: datasetTenant
        required: true
        schema:
          $ref: '#/definitions/models.DatasetTenantRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 删除数据集与工作空间的关联
      tags:
      - 数据集管理
  /del_tenant.json:
    post:
      consumes:
      - application/json
      description: 删除指定的团队及其所有相关数据
      parameters:
      - description: 删除团队参数
        in: body
        name: tenant
        required: true
        schema:
          $ref: '#/definitions/view.DelTenantParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 删除团队
      tags:
      - 团队管理
  /del_tenant_account.json:
    post:
      consumes:
      - application/json
      description: 删除团队与账户的关联关系
      parameters:
      - description: 团队账户关联信息
        in: body
        name: tenantAccount
        required: true
        schema:
          $ref: '#/definitions/models.DeleteTenantAccount'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 删除团队账户关联
      tags:
      - 团队账户管理
  /documents.json:
    get:
      consumes:
      - application/json
      description: 获取所有用户文档的列表信息（不包含内容）
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/view.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.UserDocumentListResponse'
                  type: array
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 获取用户文档列表
      tags:
      - 用户文档管理
    post:
      consumes:
      - application/json
      description: 创建新的用户文档，只有超管可以操作
      parameters:
      - description: 用户文档信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UserDocumentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/view.Result'
            - properties:
                data:
                  $ref: '#/definitions/models.UserDocumentResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/view.DifyError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/view.DifyError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 创建用户文档
      tags:
      - 用户文档管理
  /documents/{doc_type}:
    delete:
      consumes:
      - application/json
      description: 根据文档类型删除用户文档，只有超管可以操作
      parameters:
      - description: 文档类型
        in: path
        name: doc_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/view.Result'
            - properties:
                data:
                  type: string
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/view.DifyError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/view.DifyError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 删除用户文档（会同时删除所有该类型的文档）
      tags:
      - 用户文档管理
    get:
      consumes:
      - application/json
      description: 根据文档类型获取用户文档数据列表
      parameters:
      - description: 文档类型
        in: path
        name: doc_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/view.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.UserDocumentResponse'
                  type: array
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/view.DifyError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 根据文档类型获取用户文档
      tags:
      - 用户文档管理
  /documents/id/{id}:
    delete:
      consumes:
      - application/json
      description: 根据用户文档ID删除用户文档，只有超管可以操作
      parameters:
      - description: 用户文档ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/view.Result'
            - properties:
                data:
                  type: string
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/view.DifyError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/view.DifyError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 根据ID删除用户文档
      tags:
      - 用户文档管理
    get:
      consumes:
      - application/json
      description: 根据用户文档ID获取完整的用户文档内容
      parameters:
      - description: 用户文档ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/view.Result'
            - properties:
                data:
                  $ref: '#/definitions/models.UserDocumentResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/view.DifyError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/view.DifyError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 根据ID获取用户文档详情
      tags:
      - 用户文档管理
    put:
      consumes:
      - application/json
      description: 根据用户文档ID更新用户文档内容，只有超管可以操作
      parameters:
      - description: 用户文档ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新内容
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UserDocumentUpdateByIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/view.Result'
            - properties:
                data:
                  $ref: '#/definitions/models.UserDocumentResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/view.DifyError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/view.DifyError'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 根据ID更新用户文档
      tags:
      - 用户文档管理
  /feedbacks.json:
    get:
      consumes:
      - application/json
      description: 管理员获取所有用户的反馈列表
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取所有反馈
      tags:
      - 反馈管理
    post:
      consumes:
      - application/json
      description: 用户提交新的反馈
      parameters:
      - description: 反馈内容
        in: body
        name: feedback
        required: true
        schema:
          $ref: '#/definitions/view.CreateFeedbackRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 提交反馈
      tags:
      - 反馈管理
  /feedbacks/{id}/info.json:
    get:
      consumes:
      - application/json
      description: 查看单个反馈的详细信息
      parameters:
      - description: 反馈ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 查看反馈详情
      tags:
      - 反馈管理
  /feedbacks/{id}/respond.json:
    post:
      consumes:
      - application/json
      description: 管理员回复用户的反馈
      parameters:
      - description: 反馈ID
        in: path
        name: id
        required: true
        type: integer
      - description: 回复内容
        in: body
        name: response
        required: true
        schema:
          $ref: '#/definitions/view.RespondFeedbackRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 回复反馈
      tags:
      - 反馈管理
  /file:
    get:
      consumes:
      - application/json
      description: 获取服务器上传文件
      parameters:
      - description: 文件地址
        in: query
        name: file
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 获取服务器上传文件
      tags:
      - 文件
  /file-url.json:
    get:
      consumes:
      - application/json
      description: 获取文件 URL
      parameters:
      - description: 文件 ID
        in: query
        name: fileID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 获取文件 URL
      tags:
      - 文件
  /file/{fileID}:
    get:
      consumes:
      - application/json
      description: 获取文件
      parameters:
      - description: 文件 ID
        in: path
        name: fileID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 获取文件
      tags:
      - 文件
  /list_can_join_tenant.json:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取有权限加入的团队列表
      tags:
      - 团队账户管理
  /list_dataset_tenant.json:
    get:
      consumes:
      - application/json
      description: 获取特定数据集关联的工作空间列表，支持分页
      parameters:
      - description: 数据集ID
        in: query
        name: dataset_id
        required: true
        type: string
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取数据集关联的工作空间列表
      tags:
      - 数据集管理
  /list_tenant_account.json:
    get:
      consumes:
      - application/json
      description: 获取团队账户关联列表，支持分页，返回包含团队名称和账户名称的详细信息
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      - description: 团队ID
        in: query
        name: tenant_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取团队账户关联列表
      tags:
      - 团队账户管理
  /list_tenant_account_by_account.json:
    get:
      consumes:
      - application/json
      description: 根据指定账户ID获取团队账户关联列表
      parameters:
      - description: 账户ID
        in: query
        name: account_id
        required: true
        type: string
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 根据账户获取团队账户关联
      tags:
      - 团队账户管理
  /list_tenant_account_by_tenant.json:
    get:
      consumes:
      - application/json
      description: 根据指定团队ID获取团队账户关联列表
      parameters:
      - description: 团队ID
        in: query
        name: tenant_id
        required: true
        type: string
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 根据团队获取团队账户关联
      tags:
      - 团队账户管理
  /list_tenant_can_join_account.json:
    get:
      consumes:
      - application/json
      description: 获取可加入的账户
      parameters:
      - description: 租户ID
        in: query
        name: tenant_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 获取可加入的账户
      tags:
      - 团队账户管理
  /login.json:
    post:
      consumes:
      - application/json
      description: 管理员用户登录系统
      parameters:
      - description: 登录凭证
        in: body
        name: credentials
        required: true
        schema:
          $ref: '#/definitions/models.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 用户登录
      tags:
      - 认证
  /logout.json:
    post:
      consumes:
      - application/json
      description: 用户退出登录状态
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 用户登出
      tags:
      - 认证
  /menus.json:
    get:
      consumes:
      - application/json
      description: 获取系统中的菜单树，支持分页
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取菜单树
      tags:
      - 菜单管理
  /mock-data/check-working-day:
    get:
      consumes:
      - application/json
      description: 检查指定日期是否为工作日（排除周末和节假日）
      parameters:
      - description: 目标日期 YYYY-MM-DD
        example: "2025-06-29"
        in: query
        name: date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 日期检查结果
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 参数错误
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 检查指定日期是否为工作日
      tags:
      - 模拟数据
  /mock-data/generate-for-date:
    post:
      consumes:
      - application/json
      description: 为指定日期手动触发一次模拟数据生成（用于测试和补充历史数据）
      parameters:
      - description: 目标日期 YYYY-MM-DD
        example: "2025-06-29"
        in: query
        name: date
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功触发模拟数据生成
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 参数错误
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: 为指定日期手动触发模拟数据生成
      tags:
      - 模拟数据
  /mock-data/trigger:
    post:
      consumes:
      - application/json
      description: 手动触发一次模拟数据生成（用于测试）
      produces:
      - application/json
      responses:
        "200":
          description: 成功触发模拟数据生成
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: 手动触发模拟数据生成
      tags:
      - 模拟数据
  /quiz:
    get:
      consumes:
      - text/html
      description: 渲染答题系统的HTML模板页面
      parameters:
      - description: 临时数据UUID
        in: path
        name: uuid
        required: true
        type: string
      produces:
      - text/html
      responses:
        "200":
          description: HTML页面
          schema:
            type: string
      summary: 显示答题系统页面
      tags:
      - Quiz
  /refresh_token.json:
    post:
      consumes:
      - application/json
      description: 使用 refresh_token 获取新的 access_token
      parameters:
      - description: 刷新令牌
        in: body
        name: refresh_token
        required: true
        schema:
          $ref: '#/definitions/view.AuthRefreshTokenParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 刷新用户令牌
      tags:
      - 认证
  /set_account_password.json:
    post:
      consumes:
      - application/json
      description: 为指定账户设置新密码
      parameters:
      - description: 设置密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.SetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 设置账户密码
      tags:
      - 账户管理
  /switch_tenant.json:
    post:
      consumes:
      - application/json
      description: 切换团队
      parameters:
      - description: 团队ID
        in: body
        name: tenantID
        required: true
        schema:
          $ref: '#/definitions/view.TenantAccountParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 切换团队
      tags:
      - 团队管理
      - 认证
  /temp-data:
    post:
      consumes:
      - application/json
      description: 创建一条新的临时数据记录
      parameters:
      - description: 临时数据内容
        in: body
        name: temp_data
        required: true
        schema:
          $ref: '#/definitions/view.TempDataCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 创建临时数据
      tags:
      - 临时数据
  /temp-data/{uuid}:
    get:
      consumes:
      - application/json
      description: 根据UUID获取单条临时数据详情
      parameters:
      - description: 临时数据UUID
        in: path
        name: uuid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      summary: 根据UUID获取临时数据
      tags:
      - 临时数据
  /tenants.json:
    get:
      consumes:
      - application/json
      description: 获取系统中的团队列表，支持分页
      parameters:
      - description: 页码，默认为1
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取团队列表
      tags:
      - 团队管理
  /update_account_info.json:
    post:
      consumes:
      - application/json
      description: 管理员修改指定账户的姓名、部门和单位信息
      parameters:
      - description: 修改账户信息请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/view.UpdateAccountInfoParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 修改账户信息
      tags:
      - 账户管理
  /update_tenant_account_role.json:
    post:
      consumes:
      - application/json
      description: 更新团队账户关联中的角色
      parameters:
      - description: 角色更新信息
        in: body
        name: updateInfo
        required: true
        schema:
          $ref: '#/definitions/models.UpdateTenantAccountRole'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 更新团队账户关联角色
      tags:
      - 团队账户管理
  /update_tenant_name.json:
    post:
      consumes:
      - application/json
      description: 更新指定团队的名称
      parameters:
      - description: 更新团队名称参数
        in: body
        name: tenant
        required: true
        schema:
          $ref: '#/definitions/view.UpdateTenantNameParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 更新团队名称
      tags:
      - 团队管理
  /update_tenant_status.json:
    post:
      consumes:
      - application/json
      description: 更新指定团队的状态，支持normal和archive两种状态
      parameters:
      - description: 更新团队状态参数
        in: body
        name: tenant
        required: true
        schema:
          $ref: '#/definitions/view.UpdateTenantStatusParam'
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 更新团队状态
      tags:
      - 团队管理
  /user/feedbacks.json:
    get:
      consumes:
      - application/json
      description: 用户获取自己提交的反馈列表
      produces:
      - application/json
      responses:
        "200":
          description: 错误响应
          schema:
            $ref: '#/definitions/view.DifyError'
      security:
      - ApiKeyAuth: []
      summary: 获取用户自己的反馈
      tags:
      - 反馈管理
schemes:
- http
- https
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
