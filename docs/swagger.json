{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Dify Server 的 API 文档", "title": "Dify Server API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.example.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api", "paths": {"/accounts.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取系统中的账户列表，支持分页和按姓名、邮箱过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["账户管理"], "summary": "获取账户列表", "parameters": [{"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页长度", "name": "size", "in": "query"}, {"type": "string", "description": "按姓名过滤（模糊匹配）", "name": "name", "in": "query"}, {"type": "string", "description": "按邮箱过滤（模糊匹配）", "name": "email", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/add_account.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "创建一个新的系统账户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["账户管理"], "summary": "添加新账户", "parameters": [{"description": "账户信息", "name": "account", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.CreateAccountParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/add_dataset_tenant.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "创建数据集与工作空间的关联关系", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["数据集管理"], "summary": "添加数据集与工作空间的关联", "parameters": [{"description": "数据集与工作空间关联信息", "name": "datasetTenant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.DatasetTenantRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/add_tenant.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "创建一个新的团队", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队管理"], "summary": "添加新团队", "parameters": [{"description": "团队信息", "name": "tenant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.AddTenantParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/add_tenant_account.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "创建团队与账户的关联关系", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "添加团队账户关联", "parameters": [{"description": "团队账户关联信息", "name": "tenantAccount", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TenantAccountRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/anchor-statistics.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取锚点统计数据列表（分页）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["锚点统计"], "summary": "获取锚点统计列表", "parameters": [{"type": "string", "description": "资源类型", "name": "resource_type", "in": "query"}, {"type": "string", "description": "资源ID", "name": "resource_id", "in": "query"}, {"type": "string", "description": "操作类型", "name": "action_type", "in": "query"}, {"type": "string", "description": "用户ID", "name": "user_id", "in": "query"}, {"type": "string", "description": "开始日期 (YYYY-MM-DD)", "name": "start_date", "in": "query"}, {"type": "string", "description": "结束日期 (YYYY-MM-DD)", "name": "end_date", "in": "query"}, {"type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页大小", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/anchor-statistics/aggregate.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取按时间周期聚合的锚点统计数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["锚点统计"], "summary": "获取锚点统计聚合数据", "parameters": [{"type": "string", "description": "资源类型", "name": "resource_type", "in": "query"}, {"type": "string", "description": "资源ID", "name": "resource_id", "in": "query"}, {"type": "string", "description": "操作类型", "name": "action_type", "in": "query"}, {"type": "string", "description": "用户ID", "name": "user_id", "in": "query"}, {"type": "string", "description": "开始日期 (YYYY-MM-DD)", "name": "start_date", "in": "query"}, {"type": "string", "description": "结束日期 (YYYY-MM-DD)", "name": "end_date", "in": "query"}, {"type": "string", "default": "day", "description": "聚合维度 (day/month/year)", "name": "group_by", "in": "query"}, {"type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页大小", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/anchor-statistics/analysis.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取过去N天某个资源类型和操作类型的操作次数，按指定步长汇总", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["锚点统计"], "summary": "获取锚点统计分析数据", "parameters": [{"type": "string", "description": "资源类型", "name": "resource_type", "in": "query", "required": true}, {"type": "string", "description": "资源ID（可选，不指定则统计该类型下所有资源）", "name": "resource_id", "in": "query"}, {"type": "string", "description": "操作类型", "name": "action_type", "in": "query", "required": true}, {"type": "integer", "description": "过去天数 (1-365)", "name": "days", "in": "query", "required": true}, {"type": "integer", "description": "汇总步长天数 (1-30)", "name": "step", "in": "query", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/anchor-statistics/record.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "记录用户对某个资源的操作统计", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["锚点统计"], "summary": "记录锚点统计", "parameters": [{"description": "锚点统计记录", "name": "anchor", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.RecordAnchorRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/anchor-statistics/resource-summary.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取指定资源类型和操作类型下所有资源的统计汇总，以resource_id为key返回", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["锚点统计"], "summary": "获取资源汇总统计数据", "parameters": [{"type": "string", "description": "资源类型", "name": "resource_type", "in": "query", "required": true}, {"type": "string", "description": "操作类型", "name": "action_type", "in": "query", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/anchor-statistics/{id}/info.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据ID获取单条锚点统计记录详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["锚点统计"], "summary": "获取锚点统计详情", "parameters": [{"type": "integer", "description": "统计记录ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/api/quiz/questions": {"get": {"description": "返回答题系统的题目数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Quiz"], "summary": "获取题目数据", "responses": {"200": {"description": "题目列表", "schema": {"type": "array", "items": {"$ref": "#/definitions/handlers.Question"}}}}}}, "/apps/status.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取应用状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用管理"], "summary": "获取应用状态", "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/apps/update_only_me_status.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "更新指定应用的OnlyMe状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["应用管理"], "summary": "更新应用OnlyMe状态", "parameters": [{"description": "更新应用OnlyMe状态参数", "name": "app", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.UpdateAppOnlyMeStatusParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/current_tenant.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取当前团队信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队管理", "认证"], "summary": "获取当前团队信息", "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/dashboard/summary": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取Dashboard总览数据，包括总量统计、按日期统计的数据和每个app的点击数及对话消息数统计（全局统计，不按租户隔离）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["首页Dashboard"], "summary": "获取Dashboard总览数据", "parameters": [{"type": "string", "default": "daily", "description": "统计类型：daily(按日) 或 monthly(按月)", "name": "stat_type", "in": "query"}, {"type": "string", "description": "开始日期 YYYY-MM-DD", "name": "start_date", "in": "query"}, {"type": "string", "description": "结束日期 YYYY-MM-DD", "name": "end_date", "in": "query"}], "responses": {"200": {"description": "成功获取Dashboard数据", "schema": {"$ref": "#/definitions/models.DashboardSummary"}}, "400": {"description": "参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/datasets.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取系统中的数据集列表，支持分页", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["数据集管理"], "summary": "获取数据集列表", "parameters": [{"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/del_account.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "删除指定账户及其相关关联", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["账户管理"], "summary": "删除账户", "parameters": [{"description": "账户ID信息", "name": "accountInfo", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AccountIDRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/del_dataset_tenant.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "删除数据集与工作空间的关联关系", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["数据集管理"], "summary": "删除数据集与工作空间的关联", "parameters": [{"description": "数据集与工作空间关联信息", "name": "datasetTenant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.DatasetTenantRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/del_tenant.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "删除指定的团队及其所有相关数据", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队管理"], "summary": "删除团队", "parameters": [{"description": "删除团队参数", "name": "tenant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.DelTenantParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/del_tenant_account.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "删除团队与账户的关联关系", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "删除团队账户关联", "parameters": [{"description": "团队账户关联信息", "name": "tenantAccount", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.DeleteTenantAccount"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/documents.json": {"get": {"description": "获取所有用户文档的列表信息（不包含内容）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户文档管理"], "summary": "获取用户文档列表", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/view.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.UserDocumentListResponse"}}}}]}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/view.DifyError"}}}}, "post": {"description": "创建新的用户文档，只有超管可以操作", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户文档管理"], "summary": "创建用户文档", "parameters": [{"description": "用户文档信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UserDocumentRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/view.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.UserDocumentResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/view.DifyError"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/view.DifyError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/documents/id/{id}": {"get": {"description": "根据用户文档ID获取完整的用户文档内容", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户文档管理"], "summary": "根据ID获取用户文档详情", "parameters": [{"type": "integer", "description": "用户文档ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/view.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.UserDocumentResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/view.DifyError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/view.DifyError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/view.DifyError"}}}}, "put": {"description": "根据用户文档ID更新用户文档内容，只有超管可以操作", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户文档管理"], "summary": "根据ID更新用户文档", "parameters": [{"type": "integer", "description": "用户文档ID", "name": "id", "in": "path", "required": true}, {"description": "更新内容", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UserDocumentUpdateByIDRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/view.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.UserDocumentResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/view.DifyError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/view.DifyError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/view.DifyError"}}}}, "delete": {"description": "根据用户文档ID删除用户文档，只有超管可以操作", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户文档管理"], "summary": "根据ID删除用户文档", "parameters": [{"type": "integer", "description": "用户文档ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/view.Result"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/view.DifyError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/view.DifyError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/documents/{doc_type}": {"get": {"description": "根据文档类型获取用户文档数据列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户文档管理"], "summary": "根据文档类型获取用户文档", "parameters": [{"type": "string", "description": "文档类型", "name": "doc_type", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/view.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.UserDocumentResponse"}}}}]}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/view.DifyError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/view.DifyError"}}}}, "delete": {"description": "根据文档类型删除用户文档，只有超管可以操作", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户文档管理"], "summary": "删除用户文档（会同时删除所有该类型的文档）", "parameters": [{"type": "string", "description": "文档类型", "name": "doc_type", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/view.Result"}, {"type": "object", "properties": {"data": {"type": "string"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/view.DifyError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/view.DifyError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/feedbacks.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "管理员获取所有用户的反馈列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["反馈管理"], "summary": "获取所有反馈", "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}, "post": {"security": [{"ApiKeyAuth": []}], "description": "用户提交新的反馈", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["反馈管理"], "summary": "提交反馈", "parameters": [{"description": "反馈内容", "name": "feedback", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.CreateFeedbackRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/feedbacks/{id}/info.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "查看单个反馈的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["反馈管理"], "summary": "查看反馈详情", "parameters": [{"type": "integer", "description": "反馈ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/feedbacks/{id}/respond.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "管理员回复用户的反馈", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["反馈管理"], "summary": "回复反馈", "parameters": [{"type": "integer", "description": "反馈ID", "name": "id", "in": "path", "required": true}, {"description": "回复内容", "name": "response", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.RespondFeedbackRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/file": {"get": {"description": "获取服务器上传文件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["文件"], "summary": "获取服务器上传文件", "parameters": [{"type": "string", "description": "文件地址", "name": "file", "in": "query", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/file-url.json": {"get": {"description": "获取文件 URL", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["文件"], "summary": "获取文件 URL", "parameters": [{"type": "string", "description": "文件 ID", "name": "fileID", "in": "query", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/file/{fileID}": {"get": {"description": "获取文件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["文件"], "summary": "获取文件", "parameters": [{"type": "string", "description": "文件 ID", "name": "fileID", "in": "path", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/list_can_join_tenant.json": {"get": {"security": [{"ApiKeyAuth": []}], "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "获取有权限加入的团队列表", "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/list_dataset_tenant.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取特定数据集关联的工作空间列表，支持分页", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["数据集管理"], "summary": "获取数据集关联的工作空间列表", "parameters": [{"type": "string", "description": "数据集ID", "name": "dataset_id", "in": "query", "required": true}, {"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/list_tenant_account.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取团队账户关联列表，支持分页，返回包含团队名称和账户名称的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "获取团队账户关联列表", "parameters": [{"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}, {"type": "string", "description": "团队ID", "name": "tenant_id", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/list_tenant_account_by_account.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据指定账户ID获取团队账户关联列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "根据账户获取团队账户关联", "parameters": [{"type": "string", "description": "账户ID", "name": "account_id", "in": "query", "required": true}, {"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/list_tenant_account_by_tenant.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据指定团队ID获取团队账户关联列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "根据团队获取团队账户关联", "parameters": [{"type": "string", "description": "团队ID", "name": "tenant_id", "in": "query", "required": true}, {"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/list_tenant_can_join_account.json": {"get": {"description": "获取可加入的账户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "获取可加入的账户", "parameters": [{"type": "string", "description": "租户ID", "name": "tenant_id", "in": "query", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/login.json": {"post": {"description": "管理员用户登录系统", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "用户登录", "parameters": [{"description": "登录凭证", "name": "credentials", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.LoginRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/logout.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "用户退出登录状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "用户登出", "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/menus.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取系统中的菜单树，支持分页", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["菜单管理"], "summary": "获取菜单树", "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/mock-data/check-working-day": {"get": {"description": "检查指定日期是否为工作日（排除周末和节假日）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模拟数据"], "summary": "检查指定日期是否为工作日", "parameters": [{"type": "string", "example": "2025-06-29", "description": "目标日期 YYYY-MM-DD", "name": "date", "in": "query", "required": true}], "responses": {"200": {"description": "日期检查结果", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/mock-data/generate-for-date": {"post": {"security": [{"ApiKeyAuth": []}], "description": "为指定日期手动触发一次模拟数据生成（用于测试和补充历史数据）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模拟数据"], "summary": "为指定日期手动触发模拟数据生成", "parameters": [{"type": "string", "example": "2025-06-29", "description": "目标日期 YYYY-MM-DD", "name": "date", "in": "query", "required": true}], "responses": {"200": {"description": "成功触发模拟数据生成", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "参数错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/mock-data/trigger": {"post": {"security": [{"ApiKeyAuth": []}], "description": "手动触发一次模拟数据生成（用于测试）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模拟数据"], "summary": "手动触发模拟数据生成", "responses": {"200": {"description": "成功触发模拟数据生成", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/quiz": {"get": {"description": "渲染答题系统的HTML模板页面", "consumes": ["text/html"], "produces": ["text/html"], "tags": ["Quiz"], "summary": "显示答题系统页面", "parameters": [{"type": "string", "description": "临时数据UUID", "name": "uuid", "in": "path", "required": true}], "responses": {"200": {"description": "HTML页面", "schema": {"type": "string"}}}}}, "/refresh_token.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "使用 refresh_token 获取新的 access_token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证"], "summary": "刷新用户令牌", "parameters": [{"description": "刷新令牌", "name": "refresh_token", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.AuthRefreshTokenParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/set_account_password.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "为指定账户设置新密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["账户管理"], "summary": "设置账户密码", "parameters": [{"description": "设置密码请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SetPasswordRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/switch_tenant.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "切换团队", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队管理", "认证"], "summary": "切换团队", "parameters": [{"description": "团队ID", "name": "tenantID", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.TenantAccountParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/temp-data": {"post": {"description": "创建一条新的临时数据记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["临时数据"], "summary": "创建临时数据", "parameters": [{"description": "临时数据内容", "name": "temp_data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.TempDataCreateRequest"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/temp-data/{uuid}": {"get": {"description": "根据UUID获取单条临时数据详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["临时数据"], "summary": "根据UUID获取临时数据", "parameters": [{"type": "string", "description": "临时数据UUID", "name": "uuid", "in": "path", "required": true}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/tenants.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "获取系统中的团队列表，支持分页", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队管理"], "summary": "获取团队列表", "parameters": [{"type": "integer", "description": "页码，默认为1", "name": "page", "in": "query"}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/update_account_info.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "管理员修改指定账户的姓名、部门和单位信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["账户管理"], "summary": "修改账户信息", "parameters": [{"description": "修改账户信息请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.UpdateAccountInfoParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/update_tenant_account_role.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "更新团队账户关联中的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队账户管理"], "summary": "更新团队账户关联角色", "parameters": [{"description": "角色更新信息", "name": "updateInfo", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateTenantAccountRole"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/update_tenant_name.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "更新指定团队的名称", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队管理"], "summary": "更新团队名称", "parameters": [{"description": "更新团队名称参数", "name": "tenant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.UpdateTenantNameParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/update_tenant_status.json": {"post": {"security": [{"ApiKeyAuth": []}], "description": "更新指定团队的状态，支持normal和archive两种状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队管理"], "summary": "更新团队状态", "parameters": [{"description": "更新团队状态参数", "name": "tenant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/view.UpdateTenantStatusParam"}}], "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}, "/user/feedbacks.json": {"get": {"security": [{"ApiKeyAuth": []}], "description": "用户获取自己提交的反馈列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["反馈管理"], "summary": "获取用户自己的反馈", "responses": {"200": {"description": "错误响应", "schema": {"$ref": "#/definitions/view.DifyError"}}}}}}, "definitions": {"handlers.Question": {"type": "object", "properties": {"correct_answer": {"type": "string"}, "explanation": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}, "question": {"type": "string"}, "question_id": {"type": "string"}, "question_type": {"type": "string"}}}, "models.Account": {"type": "object", "properties": {"Avatar": {"type": "string"}, "Department": {"type": "string"}, "Email": {"type": "string"}, "ID": {"type": "string"}, "InterfaceLanguage": {"type": "string"}, "InterfaceTheme": {"type": "string"}, "Name": {"type": "string"}, "Status": {"$ref": "#/definitions/models.AccountStatus"}, "Timezone": {"type": "string"}, "unit": {"type": "string"}}}, "models.AccountIDRequest": {"type": "object", "properties": {"id": {"type": "string"}}}, "models.AccountStatus": {"type": "string", "enum": ["pending", "uninitialized", "active", "banned", "closed"], "x-enum-varnames": ["PENDING", "UNINITIALIZED", "ACTIVE", "BANNED", "CLOSED"]}, "models.AppStatistics": {"type": "object", "properties": {"app_id": {"type": "string"}, "app_name": {"type": "string"}, "click_count": {"type": "integer"}, "message_count": {"type": "integer"}}}, "models.Apps": {"type": "object", "properties": {"api_rph": {"type": "integer"}, "api_rpm": {"type": "integer"}, "app_model_config_id": {"type": "string"}, "created_at": {"type": "string"}, "created_by": {"type": "string"}, "description": {"type": "string"}, "enable_api": {"type": "boolean"}, "enable_site": {"type": "boolean"}, "icon": {"type": "string"}, "icon_background": {"type": "string"}, "icon_type": {"type": "string"}, "id": {"type": "string"}, "is_demo": {"type": "boolean"}, "is_public": {"type": "boolean"}, "is_universal": {"type": "boolean"}, "max_active_requests": {"type": "integer"}, "mode": {"type": "string"}, "name": {"type": "string"}, "only_me": {"type": "boolean"}, "status": {"type": "string"}, "tenant_id": {"type": "string"}, "tracing": {"type": "string"}, "updated_at": {"type": "string"}, "updated_by": {"type": "string"}, "use_icon_as_answer_icon": {"type": "boolean"}, "workflow_id": {"type": "string"}}}, "models.DashboardSummary": {"type": "object", "properties": {"anchor_statistics": {"type": "array", "items": {"$ref": "#/definitions/models.DateStatistics"}}, "app_statistics": {"type": "array", "items": {"$ref": "#/definitions/models.AppStatistics"}}, "message_statistics": {"type": "array", "items": {"$ref": "#/definitions/models.DateStatistics"}}, "total_accounts": {"type": "integer"}, "total_anchor_statistics": {"type": "integer"}, "total_apps": {"type": "integer"}, "total_data_sets": {"type": "integer"}, "total_documents": {"type": "integer"}, "total_messages": {"type": "integer"}}}, "models.Dataset": {"type": "object", "properties": {"collectionBindingID": {"type": "string"}, "createdAt": {"type": "string"}, "createdBy": {"type": "string"}, "dataSourceType": {"type": "string"}, "description": {"type": "string"}, "embeddingModel": {"type": "string"}, "embeddingModelProvider": {"type": "string"}, "id": {"type": "string"}, "indexStruct": {"type": "string"}, "indexingTechnique": {"type": "string"}, "name": {"type": "string"}, "permission": {"type": "string"}, "provider": {"type": "string"}, "retrievalModel": {"type": "string"}, "tenantID": {"type": "string"}, "updatedAt": {"type": "string"}, "updatedBy": {"type": "string"}}}, "models.DatasetTenantRequest": {"type": "object", "properties": {"dataset_id": {"type": "string"}, "tenant_id": {"type": "string"}}}, "models.DateStatistics": {"type": "object", "properties": {"count": {"type": "integer"}, "date": {"type": "string"}}}, "models.DeleteTenantAccount": {"type": "object", "properties": {"id": {"type": "string"}}}, "models.Feedback": {"type": "object", "properties": {"created_at": {"description": "Nullable timestamp", "type": "string"}, "feedback_content": {"type": "string"}, "id": {"type": "string"}, "response": {"description": "Nullable text", "allOf": [{"$ref": "#/definitions/sql.NullString"}]}, "status": {"$ref": "#/definitions/models.FeedbackStatus"}, "tenant_id": {"type": "string"}, "updated_at": {"description": "Nullable timestamp", "allOf": [{"$ref": "#/definitions/sql.NullTime"}]}, "user_id": {"type": "string"}}}, "models.FeedbackStatus": {"type": "string", "enum": ["pending", "answered"], "x-enum-varnames": ["FeedbackStatusPending", "FeedbackStatusAnswered"]}, "models.LoginRequest": {"type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}}, "models.PageResponse": {"type": "object", "properties": {"data": {}, "page": {"description": "当前页", "type": "integer"}, "page_size": {"description": "每页大小", "type": "integer"}, "total": {"description": "总记录数", "type": "integer"}, "total_pages": {"description": "总页数", "type": "integer"}}}, "models.SetPasswordRequest": {"type": "object", "properties": {"id": {"type": "string"}, "password": {"type": "string"}}}, "models.SystemMenu": {"type": "object", "properties": {"created_at": {"type": "string"}, "icon": {"type": "string"}, "id": {"type": "integer"}, "index": {"type": "string"}, "permiss": {"type": "string"}, "permission_id": {"type": "integer"}, "pid": {"type": "integer"}, "title": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.Tenant": {"type": "object", "properties": {"createdAt": {"type": "string"}, "customConfig": {"type": "string"}, "encryptPublicKey": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "plan": {"$ref": "#/definitions/models.TenantPlan"}, "status": {"type": "string"}, "updatedAt": {"type": "string"}}}, "models.TenantAccountRequest": {"type": "object", "properties": {"account_id": {"type": "string"}, "role": {"type": "string"}, "tenant_id": {"type": "string"}}}, "models.TenantPlan": {"type": "string", "enum": ["basic", "pro", "enterprise"], "x-enum-varnames": ["TenantPlanBasic", "TenantPlanPro", "TenantPlanEnterprise"]}, "models.UpdateTenantAccountRole": {"type": "object", "properties": {"id": {"type": "string"}, "role": {"type": "string"}}}, "models.UserDocumentListResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "created_by": {"type": "string"}, "doc_type": {"type": "string"}, "id": {"type": "integer"}, "updated_at": {"type": "string"}}}, "models.UserDocumentRequest": {"type": "object", "required": ["content", "doc_type"], "properties": {"content": {"type": "string", "example": "这是用户手册的内容..."}, "doc_type": {"type": "string", "example": "user_manual"}}}, "models.UserDocumentResponse": {"type": "object", "properties": {"content": {"type": "string"}, "created_at": {"type": "string"}, "created_by": {"type": "string"}, "doc_type": {"type": "string"}, "id": {"type": "integer"}, "updated_at": {"type": "string"}}}, "models.UserDocumentUpdateByIDRequest": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "example": "这是更新后的内容..."}}}, "sql.NullString": {"type": "object", "properties": {"string": {"type": "string"}, "valid": {"description": "Valid is true if String is not NULL", "type": "boolean"}}}, "sql.NullTime": {"type": "object", "properties": {"time": {"type": "string"}, "valid": {"description": "Valid is true if Time is not NULL", "type": "boolean"}}}, "view.AccountView": {"type": "object", "properties": {"Avatar": {"type": "string"}, "Department": {"type": "string"}, "Email": {"type": "string"}, "ID": {"type": "string"}, "InterfaceLanguage": {"type": "string"}, "InterfaceTheme": {"type": "string"}, "Name": {"type": "string"}, "Status": {"$ref": "#/definitions/models.AccountStatus"}, "Timezone": {"type": "string"}, "firstTenantAccountID": {"type": "string"}, "firstTenantID": {"type": "string"}, "firstTenantRole": {"type": "string"}, "unit": {"type": "string"}}}, "view.AddTenantParam": {"type": "object", "properties": {"name": {"type": "string"}, "user_id": {"type": "string"}}}, "view.AnalysisAnchorStatisticsListResponse": {"type": "object", "properties": {"action_type": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/view.AnalysisAnchorStatisticsResponse"}}, "resource_type": {"type": "string"}, "step_days": {"type": "integer"}, "summary": {"$ref": "#/definitions/view.AnalysisAnchorStatisticsSummary"}, "total_days": {"type": "integer"}}}, "view.AnalysisAnchorStatisticsResponse": {"type": "object", "properties": {"action_type": {"description": "操作类型", "type": "string"}, "end_date": {"description": "结束日期", "type": "string"}, "resource_type": {"description": "资源类型", "type": "string"}, "start_date": {"description": "开始日期", "type": "string"}, "total_count": {"description": "总操作次数", "type": "integer"}, "user_count": {"description": "用户数", "type": "integer"}}}, "view.AnalysisAnchorStatisticsSummary": {"type": "object", "properties": {"average_per_period": {"description": "每个周期平均次数", "type": "number"}, "max_period_count": {"description": "单个周期最大次数", "type": "integer"}, "min_period_count": {"description": "单个周期最小次数", "type": "integer"}, "total_count": {"description": "总操作次数", "type": "integer"}, "total_user_count": {"description": "总用户数", "type": "integer"}}}, "view.AnchorStatisticsAggregateListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/view.AnchorStatisticsAggregateResponse"}}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "view.AnchorStatisticsAggregateResponse": {"type": "object", "properties": {"action_type": {"description": "操作类型", "type": "string"}, "period": {"description": "时间周期 (2024-01-01, 2024-01, 2024)", "type": "string"}, "resource_id": {"description": "资源ID", "type": "string"}, "resource_name": {"description": "资源名称", "type": "string"}, "resource_type": {"description": "资源类型", "type": "string"}, "total_count": {"description": "总次数", "type": "integer"}, "user_count": {"description": "用户数", "type": "integer"}}}, "view.AnchorStatisticsListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/view.AnchorStatisticsResponse"}}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "view.AnchorStatisticsResponse": {"type": "object", "properties": {"action_count": {"type": "integer"}, "action_type": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "record_date": {"type": "string"}, "resource_id": {"type": "string"}, "resource_name": {"type": "string"}, "resource_type": {"type": "string"}, "tenant_id": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}, "user_name": {"type": "string"}}}, "view.AppsSimpleView": {"type": "object", "properties": {"app_id": {"type": "string"}, "enable_site": {"type": "boolean"}, "name": {"type": "string"}, "only_me": {"type": "boolean"}, "status": {"type": "string"}}}, "view.AuthRefreshTokenParam": {"type": "object", "properties": {"refresh_token": {"type": "string"}}}, "view.CreateAccountParam": {"type": "object", "properties": {"department": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}, "tenant_id": {"type": "string"}, "unit": {"type": "string"}}}, "view.CreateFeedbackRequest": {"type": "object", "required": ["feedback_content"], "properties": {"feedback_content": {"type": "string"}}}, "view.CurrentTenant": {"type": "object", "properties": {"created_at": {"type": "integer"}, "custom_config": {}, "id": {"type": "string"}, "in_trial": {}, "name": {"type": "string"}, "plan": {"type": "string"}, "role": {"type": "string"}, "status": {"type": "string"}, "trial_end_reason": {}}}, "view.DelTenantParam": {"type": "object", "properties": {"tenant_id": {"type": "string"}}}, "view.DifyError": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "status": {"type": "integer"}}}, "view.FeedbackResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "feedback_content": {"type": "string"}, "id": {"type": "integer"}, "response": {"type": "string"}, "status": {"type": "string"}, "tenant_id": {"type": "integer"}, "updated_at": {"type": "string"}, "user_id": {"type": "integer"}, "user_name": {"type": "string"}}}, "view.RecordAnchorRequest": {"type": "object", "required": ["action_type", "resource_id", "resource_type"], "properties": {"action_type": {"type": "string", "example": "click"}, "resource_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "resource_type": {"type": "string", "example": "app"}}}, "view.RecordAnchorResponse": {"type": "object", "properties": {"message": {"type": "string"}, "success": {"type": "boolean"}}}, "view.ResourceSummaryAnchorStatisticsItem": {"type": "object", "properties": {"action_type": {"description": "操作类型", "type": "string"}, "resource_id": {"description": "资源ID", "type": "string"}, "resource_name": {"description": "资源名称", "type": "string"}, "resource_type": {"description": "资源类型", "type": "string"}, "total_count": {"description": "总操作次数", "type": "integer"}, "user_count": {"description": "用户数", "type": "integer"}}}, "view.ResourceSummaryAnchorStatisticsResponse": {"type": "object", "additionalProperties": {"$ref": "#/definitions/view.ResourceSummaryAnchorStatisticsItem"}}, "view.RespondFeedbackRequest": {"type": "object", "required": ["response"], "properties": {"response": {"type": "string"}}}, "view.Result": {"type": "object", "properties": {"data": {}, "result": {"type": "string"}}}, "view.SimpleAccount": {"type": "object", "properties": {"department": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "unit": {"type": "string"}}}, "view.SimpleTenant": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "view.TempDataCreateRequest": {"type": "object", "required": ["content"], "properties": {"content": {"type": "string", "example": "这是临时数据内容"}}}, "view.TempDataCreateResponse": {"type": "object", "properties": {"message": {"type": "string"}, "uuid": {"type": "string"}}}, "view.TempDataResponse": {"type": "object", "properties": {"content": {"type": "string"}, "created_at": {"type": "string"}, "uuid": {"type": "string"}}}, "view.TenantAccountParam": {"type": "object", "properties": {"tenant_id": {"type": "string"}}}, "view.TenantAccountView": {"type": "object", "properties": {"accountID": {"type": "string"}, "accountName": {"type": "string"}, "createdAt": {"type": "string"}, "department": {"type": "string"}, "id": {"type": "string"}, "invitedBy": {"type": "string"}, "role": {"type": "string"}, "tenantID": {"type": "string"}, "tenantName": {"type": "string"}, "unit": {"type": "string"}, "updatedAt": {"type": "string"}}}, "view.UpdateAccountInfoParam": {"type": "object", "properties": {"department": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "unit": {"type": "string"}}}, "view.UpdateAppOnlyMeStatusParam": {"type": "object", "properties": {"app_id": {"type": "string"}, "only_me": {"type": "boolean"}}}, "view.UpdateTenantNameParam": {"type": "object", "properties": {"name": {"type": "string"}, "tenant_id": {"type": "string"}}}, "view.UpdateTenantStatusParam": {"type": "object", "properties": {"status": {"type": "string"}, "tenant_id": {"type": "string"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}