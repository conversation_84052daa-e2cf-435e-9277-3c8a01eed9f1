package middleware

import (
	"log/slog"
	"strings"

	"difyserver/config"
	"difyserver/database"
	"difyserver/models"
	"difyserver/utils"
	"difyserver/utils/define"
	"difyserver/view"
	"github.com/gin-gonic/gin"
)

func authMiddleware(c *gin.Context) *view.CurrentTenant {
	authHeader := c.<PERSON>("Authorization")
	if authHeader == "" {
		c.JSON(401, gin.H{"error": "未提供认证信息"})
		c.Abort()
		return nil
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if !(len(parts) == 2 && parts[0] == "Bearer") {
		c.JSON(401, gin.H{"error": "认证格式错误"})
		c.Abort()
		return nil
	}
	token := parts[1]
	claims, err := utils.ParseToken(token)
	if err != nil {
		slog.Error("解析token失败，无效的认证信息")
		c.<PERSON>(401, gin.H{"error": "无效的认证信息"})
		c.Abort()
		return nil
	}
	account := new(models.Account)
	if result := database.DB.Where("id = ?", claims.ID).First(&account); result.Error != nil || account.Status != models.ACTIVE {

		slog.Error("查询数据库，无效的认证信息")
		c.JSON(401, gin.H{"error": "无效的认证信息"})
		c.Abort()
		return nil
	}
	cTeam, err := current(c, token)
	if err != nil {
		slog.Error("获取当前团队信息失败，无效的认证信息")
		c.JSON(401, gin.H{"error": "无效的认证信息"})
		c.Abort()
		return nil
	}

	// 将用户信息存储到上下文中
	c.Set(define.UserIDKey, claims.ID)
	c.Set(define.UserEmailKey, account.Email)
	c.Set(define.UserAttrKey, *account)
	c.Set(define.CurrentToken, token)
	return cTeam
}

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authMiddleware(c)
		c.Next()
	}
}
func AuthSuperAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		cTeam := authMiddleware(c)
		if cTeam.Role != view.TenantAccountRoleOwner {
			c.JSON(401, gin.H{"error": "没有超级管理员权限"})
			c.Abort()
			return
		}
		c.Next()
	}
}

func AuthAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		cTeam := authMiddleware(c)
		if view.RolesNum[cTeam.Role] > 2 {
			c.JSON(401, gin.H{"error": "没有管理员权限"})
			c.Abort()
			return
		}
		c.Next()
	}
}

// AuthConfigAdminMiddleware 检查用户是否为配置文件中的超管
func AuthConfigAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authMiddleware(c)

		// 获取用户邮箱
		userEmail := c.GetString(define.UserEmailKey)
		if userEmail == "" {
			c.JSON(401, gin.H{"error": "无法获取用户邮箱信息"})
			c.Abort()
			return
		}

		// 检查用户是否在配置的管理员列表中
		isAdmin := false
		for _, adminEmail := range config.GlobalConfig.Admins {
			if adminEmail == userEmail {
				isAdmin = true
				break
			}
		}

		if !isAdmin {
			c.JSON(403, gin.H{"error": "没有超级管理员权限"})
			c.Abort()
			return
		}

		c.Next()
	}
}

func current(c *gin.Context, token string) (*view.CurrentTenant, error) {
	client := utils.GetDifyClientWithToken(token)
	//http://localhost/console/api/workspaces/current?
	currentTenant := new(view.CurrentTenant)
	if err := client.GetJSON(c, "/console/api/workspaces/current", nil, nil, currentTenant); err != nil {
		slog.Error("获取当前团队信息失败", "error", err)
		return nil, err
	}
	c.Set(define.CurrentTenantKey, currentTenant)
	c.Set(define.CurrentTenantIDKey, currentTenant.Id)
	return currentTenant, nil
}
