app:
  description: '比对用户上传的合同与标准合规样例，提示不符合规范的内容'
  icon: 📝
  icon_background: '#E6F7FF'
  mode: workflow
  name: 合同规范性审查
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/ollama:0.0.6@7d66a960a68cafdcdf5589fdf5d01a995533f956853c69c54eddcf797006fa37
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: bowenliang123/md_exporter:0.5.1@a577f215826426f34b5c4ed9c7b90695298470c885d1a925ea2af584374bf002
kind: app
version: 0.2.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: [.doc, .docx]
      allowed_file_types:
      - document
      allowed_file_upload_methods:
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      number_limits: 1
    opening_statement: '欢迎使用合同规范性审查助手。请上传您需要审查的合同文件，我将与标准合规样例进行比对，并提示不符合规范的内容。'
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: document-extractor
      id: start-source-document-extractor-target
      source: 'start'
      sourceHandle: source
      target: 'document_extractor'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: document-extractor
        targetType: llm
      id: document_extractor-source-compare_llm-target
      source: 'document_extractor'
      sourceHandle: source
      target: 'compare_llm'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: tool
      id: compare_llm-source-md_to_pdf-target
      source: 'compare_llm'
      sourceHandle: source
      target: 'md_to_pdf'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: tool
      id: md_to_pdf-source-md_to_docx-target
      source: 'md_to_pdf'
      sourceHandle: source
      target: 'md_to_docx'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: end
      id: md_to_docx-source-end-target
      source: 'md_to_docx'
      sourceHandle: source
      target: 'end'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions: [.doc, .docx]
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          label: 请上传需要审查的合同文件
          max_length: 1
          options: []
          required: true
          type: file-list
          variable: user_contract
        - label: 请选择合同类型
          max_length: 48
          options: 
          - 劳动合同
          - 租赁合同
          - 买卖合同
          - 服务合同
          - 其他合同
          required: true
          type: select
          variable: contract_type
      height: 167
      id: 'start'
      position:
        x: 30
        y: 283.5
      positionAbsolute:
        x: 30
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_array_file: true
        selected: false
        title: 文档提取器
        type: document-extractor
        variable_selector:
        - 'start'
        - user_contract
      height: 91
      id: 'document_extractor'
      position:
        x: 334
        y: 283.5
      positionAbsolute:
        x: 334
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - 'document_extractor'
          - text
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: qwen3:30b-a3b
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: system-prompt
          role: system
          text: "## Role: \n合同审查专家\n \n## Profile:\n- language: 中文\n- description: 你是一位专业的合同审查专家，负责比对用户上传的合同与标准合规样例，找出不符合规范的内容。\n \n## Goals:\n- 详细分析用户合同与标准合规样例的差异\n- 指出缺失的必要条款\n- 识别不符合规范的条款内容\n- 评估可能存在的法律风险\n- 提供具体的修改建议\n\n## Constrains:\n- 要依据正在适用的法律，不能引用废止的法律条文\n- 合同条款约定应当符合最新法律法规及相关政策要求\n- 专用名称地点应当准确\n- 要结合合同类型进行分析\n\n## Skills:\n- 熟悉中国的合同法律法规\n- 能够识别合同中的法律风险\n- 擅长比对文本差异\n- 能够提供专业的修改建议\n\n## Knowledge Base:\n根据用户选择的合同类型({{#start.contract_type#}})，从知识库中检索相应的标准合规样例合同。\n\n## Output Format:\n请以Markdown格式输出分析结果，包含以下部分：\n\n### 1. 摘要\n简要概述合规性评估结果\n\n### 2. 主要问题\n按严重程度排序的问题列表\n\n### 3. 详细分析\n针对每个条款的具体分析\n\n### 4. 修改建议\n如何使合同符合规范的具体建议\n\n### 5. 合规性评分\n给出一个0-100的分数评估整体合规性"
        - id: user-prompt
          role: user
          text: "请分析以下合同文档，与标准合规样例进行比对，找出不符合规范的内容：\n\n{{#document_extractor.text#}}"
        selected: false
        title: 合同比对分析
        type: llm
        variables: []
        vision:
          configs:
            detail: high
          enabled: false
      height: 89
      id: 'compare_llm'
      position:
        x: 638
        y: 283.5
      positionAbsolute:
        x: 638
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - 'compare_llm'
          - text
          variable: 审查意见
        - value_selector:
          - 'md_to_pdf'
          - files
          variable: PDF
        - value_selector:
          - 'md_to_docx'
          - files
          variable: DOCX
        selected: false
        title: 结束
        type: end
      height: 141
      id: 'end'
      position:
        x: 1550
        y: 283.5
      positionAbsolute:
        x: 1550
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Markdown text
            ja_JP: Markdown text
            pt_BR: Markdown text
            zh_Hans: Markdown格式文本
          label:
            en_US: Markdown text
            ja_JP: Markdown text
            pt_BR: Markdown text
            zh_Hans: Markdown格式文本
          llm_description: ''
          max: null
          min: null
          name: md_text
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          md_text: ''
        provider_id: bowenliang123/md_exporter/md_exporter
        provider_name: bowenliang123/md_exporter/md_exporter
        provider_type: builtin
        selected: false
        title: Markdown转PDF文件
        tool_configurations: {}
        tool_label: Markdown转PDF文件
        tool_name: md_to_pdf
        tool_parameters:
          md_text:
            type: mixed
            value: '{{#compare_llm.text#}}'
        type: tool
      height: 53
      id: 'md_to_pdf'
      position:
        x: 942
        y: 283.5
      positionAbsolute:
        x: 942
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Markdown text
            ja_JP: Markdown text
            pt_BR: Markdown text
            zh_Hans: Markdown格式文本
          label:
            en_US: Markdown text
            ja_JP: Markdown text
            pt_BR: Markdown text
            zh_Hans: Markdown格式文本
          llm_description: ''
          max: null
          min: null
          name: md_text
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          md_text: ''
        provider_id: bowenliang123/md_exporter/md_exporter
        provider_name: bowenliang123/md_exporter/md_exporter
        provider_type: builtin
        selected: false
        title: Markdown转Docx文件
        tool_configurations: {}
        tool_label: Markdown转Docx文件
        tool_name: md_to_docx
        tool_parameters:
          md_text:
            type: mixed
            value: '{{#compare_llm.text#}}'
        type: tool
      height: 53
      id: 'md_to_docx'
      position:
        x: 1246
        y: 283.5
      positionAbsolute:
        x: 1246
        y: 283.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 74.80000000000018
      y: 157.7
      zoom: 0.7