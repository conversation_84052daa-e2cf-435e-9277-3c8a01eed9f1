package config

import (
	"flag"
	"fmt"
	"log/slog"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

var logLevelMap = map[string]slog.Level{
	"debug": slog.LevelDebug,
	"info":  slog.LevelInfo,
	"warn":  slog.LevelWarn,
	"error": slog.LevelError,
}

const DifyStoragePath = "/app/api/storage"

type Config struct {
	Database struct {
		Host     string `yaml:"host"`
		Port     int    `yaml:"port"`
		User     string `yaml:"user"`
		Password string `yaml:"password"`
		DBName   string `yaml:"dbname"`
	} `yaml:"database"`
	Admins          []string `yaml:"admins"` // 添加管理员邮箱列表
	DifyStoragePath string   `yaml:"difyStoragePath"`
	DifyUrl         string   `yaml:"difyUrl"`
	DifySecretKey   string   `yaml:"difySecretKey"`
	SyncPlugin      []string `yaml:"syncPlugin"`
	LogLevel        string   `yaml:"logLevel"`
	MockData        struct {
		Enabled         bool    `yaml:"enabled"`           // 是否启用模拟数据生成
		MockName        string  `yaml:"mock_name"`         // 模拟用户名称
		MockEmail       string  `yaml:"mock_email"`        // 模拟用户邮箱
		AppPercentage   float64 `yaml:"app_percentage"`    // 每天生成数据的app百分比 (0.0-1.0)
		MinClickCount   int     `yaml:"min_click_count"`   // 最小点击数
		MaxClickCount   int     `yaml:"max_click_count"`   // 最大点击数
		MinMessageCount int     `yaml:"min_message_count"` // 最小消息数
		MaxMessageCount int     `yaml:"max_message_count"` // 最大消息数
	} `yaml:"mock_data"`
}

var GlobalConfig Config

func LoadConfig() error {

	config := flag.String("config", "./config.yaml", "server config")
	flag.Parse()
	configPath := *config
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return err
	}

	// 解析配置文件
	err = yaml.Unmarshal(data, &GlobalConfig)
	if err != nil {
		return err
	}
	GlobalConfig.setDefault()
	slog.Info("load config success", "config", GlobalConfig)
	return nil
}
func (c *Config) setDefault() {
	if c.Admins == nil {
		c.Admins = []string{"<EMAIL>"}
	}
	if c.DifyStoragePath == "" {
		c.DifyStoragePath = DifyStoragePath
	}
	if c.DifyUrl == "" {
		c.DifyUrl = "http://nginx"
	}
	if c.DifySecretKey == "" {
		c.DifySecretKey = "************************************************"
	}
	if c.SyncPlugin == nil {
		c.SyncPlugin = []string{"langgenius/openai_api_compatible/openai_api_compatible", "langgenius/huggingface_tei/huggingface_tei"}
	}
	// 设置模拟数据默认值
	if c.MockData.MockName == "" {
		c.MockData.MockName = "myTest1"
	}
	if c.MockData.MockEmail == "" {
		c.MockData.MockEmail = "<EMAIL>"
	}
	if c.MockData.AppPercentage == 0 {
		c.MockData.AppPercentage = 0.3 // 默认30%
	}
	if c.MockData.MinClickCount == 0 {
		c.MockData.MinClickCount = 5
	}
	if c.MockData.MaxClickCount == 0 {
		c.MockData.MaxClickCount = 15
	}
	if c.MockData.MinMessageCount == 0 {
		c.MockData.MinMessageCount = 5
	}
	if c.MockData.MaxMessageCount == 0 {
		c.MockData.MaxMessageCount = 15
	}
}

func (c *Config) GetPluginIDs() []string {
	ids := make([]string, 0)
	for _, plugin := range c.SyncPlugin {
		names := strings.Split(plugin, "/")
		ids = append(ids, fmt.Sprintf("%s/%s", names[0], names[1]))
	}
	return ids
}

func (c *Config) GetLogLevel() slog.Level {
	if c.LogLevel == "" {
		return slog.LevelInfo
	}
	if v, ok := logLevelMap[c.LogLevel]; ok {
		return v
	} else {
		return slog.LevelInfo
	}
}
