# 锚点统计API测试示例

## 测试环境准备

1. 启动服务器：`./difyserver.exe`
2. 确保数据库连接正常
3. 获取有效的JWT token

## API测试示例

### 1. 记录锚点统计

```bash
curl -X POST "http://localhost:8080/api/anchor-statistics/record.json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "resource_type": "app",
    "resource_id": "test-app-001",
    "action_type": "click"
  }'
```

### 2. 获取锚点统计列表

```bash
curl -X GET "http://localhost:8080/api/anchor-statistics.json?resource_type=app&action_type=click&page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 获取聚合统计数据

```bash
curl -X GET "http://localhost:8080/api/anchor-statistics/aggregate.json?resource_type=app&action_type=click&group_by=day&start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 获取分析统计数据（新增）

```bash
# 获取过去30天所有应用点击统计，按7天汇总
curl -X GET "http://localhost:8080/api/anchor-statistics/analysis.json?resource_type=app&action_type=click&days=30&step=7" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取过去30天特定应用点击统计，按7天汇总
curl -X GET "http://localhost:8080/api/anchor-statistics/analysis.json?resource_type=app&resource_id=test-app-001&action_type=click&days=30&step=7" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取过去90天特定应用的月度统计
curl -X GET "http://localhost:8080/api/anchor-statistics/analysis.json?resource_type=app&resource_id=test-app-001&action_type=click&days=90&step=30" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取过去7天所有应用的日度统计
curl -X GET "http://localhost:8080/api/anchor-statistics/analysis.json?resource_type=app&action_type=click&days=7&step=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 获取统计详情

```bash
curl -X GET "http://localhost:8080/api/anchor-statistics/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 预期响应格式

### 分析统计响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "start_date": "2024-01-01",
        "end_date": "2024-01-07",
        "total_count": 150,
        "user_count": 25,
        "resource_type": "app",
        "action_type": "click"
      },
      {
        "start_date": "2024-01-08",
        "end_date": "2024-01-14",
        "total_count": 180,
        "user_count": 30,
        "resource_type": "app",
        "action_type": "click"
      }
    ],
    "resource_type": "app",
    "action_type": "click",
    "total_days": 30,
    "step_days": 7,
    "summary": {
      "total_count": 1200,
      "total_user_count": 85,
      "average_per_period": 171.4,
      "max_period_count": 220,
      "min_period_count": 120
    }
  }
}
```

### 5. 获取资源汇总统计数据（新增）

```bash
# 获取所有应用的点击统计汇总
curl -X GET "http://localhost:8080/api/anchor-statistics/resource-summary.json?resource_type=app&action_type=click" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取所有数据集的查看统计汇总
curl -X GET "http://localhost:8080/api/anchor-statistics/resource-summary.json?resource_type=dataset&action_type=view" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 6. 获取统计详情

```bash
curl -X GET "http://localhost:8080/api/anchor-statistics/1.json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 预期响应格式

### 资源汇总统计响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "f99b7ff2-d722-4dbc-9b04-f6df29376def": {
      "resource_type": "app",
      "resource_id": "f99b7ff2-d722-4dbc-9b04-f6df29376def",
      "resource_name": "我的智能助手",
      "action_type": "click",
      "total_count": 150,
      "user_count": 25
    },
    "a1b2c3d4-e5f6-7890-abcd-ef1234567890": {
      "resource_type": "app",
      "resource_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      "resource_name": "数据分析工具",
      "action_type": "click",
      "total_count": 89,
      "user_count": 15
    }
  }
}
```

## 测试数据准备

可以通过多次调用记录接口来生成测试数据：

```bash
# 记录不同日期的数据（需要修改数据库中的record_date字段来模拟历史数据）
for i in {1..10}; do
  curl -X POST "http://localhost:8080/api/anchor-statistics/record.json" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer YOUR_TOKEN" \
    -d '{
      "resource_type": "app",
      "resource_id": "test-app-001",
      "action_type": "click"
    }'
  sleep 1
done
```

## 注意事项

1. 所有查询接口都需要管理员权限
2. 记录接口只需要普通用户权限
3. 数据按租户隔离
4. 同一天的重复操作会自动累加计数
5. 分析接口的days参数范围是1-365，step参数范围是1-30
