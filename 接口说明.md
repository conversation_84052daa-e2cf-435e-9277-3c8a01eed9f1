# dify接口二次开发，以及源接口调用说明

#### 说明：

- 在dify的基础额外多部署一个应用服务difyserver
- 打通dify和difyserver的会话信息，公用一套token逻辑
- 通过配置nginx使得dify和difyserver同域,admin上下文代表difyserver服务，并可支持页面嵌入方式 既： localhost/admin   
- difyserver复用dify的数据库以及存储挂载
- 代码路径：https://codeup.aliyun.com/61233c140fc7bf0dbe1e642d/ai-dify/difyserver.git
- swagger文档： 使用apifox共享
- 会话认证统一使用`Authorization: Bearer ${Token}`方案
- dify创建成功会出现201状态码  删除成功会出现204状态码


##  1登陆认证接口说明：

### 1.1 登陆接口 （Auth-User login）

- 调用登陆接口成功后，需要会获得`token`以及`refresh_token` 需要将`token`和`refresh_token`写入到浏览器本地存储
- 调用其他接口需要使用附带认证token，若是接口返回状态码`401`则需要去调用`刷新用户令牌接口`，后再去调用接口，若还是失败则调转会登录页面，并清理本地token

```javascript
localStorage.setItem("console_token", token);
localStorage.setItem("refresh_token", refreshToken);
```

### 1.2 刷新用户令牌接口  （Auth-Refresh access token）

- 调用接口成功后会返回对应新的`token`和`refresh_token`需要将`token`和`refresh_token`写入到浏览器本地存储

### 1.3 退出登录 （Auth-User logout）

- 调用接口成功后需要清理本地浏览器的token

## 2.账户管理接口说明（管理者角度）：

### 2.1 获取账户列表接口  (账户管理-获取账户列表)

### 2.2  添加新账户接口 （账户管理-添加新账户）

- `tenant_id`有传时候，会自动加入团队，没有的时候则不会加入团队（注：没有团队用户无法登陆使用）

### 2.3 删除账户接口（账户管理-删除账户）

### 2.4 重置密码（账户管理-设置账户密码）

- 提供给管理员进行用户重置密码（无需原密码）

## 3.登陆账号管理（账号自身角度）

### 3.1 修改密码（Account-Update password)

- 用户登陆后修改自身密码

### 3.2 修改名称（Account-Update account name）

### 3.3 修改头像（Account-Update account avatar）

- 需要先上传图片获取到附件ID（Files-Upload a file)
- 然后再将ID去调用修改头像接口

## 4. 模型供应商管理（我们默认全部采用OpenAI-API-compatible）这个插件

### 4.1（待定） 单团队情况 我们提前配置，后需多团队时候再考虑方案，因为目前每个团队是不共享的

## 5. 模型管理（支持类型llm text-embedding  rerank  speech2text  tts）

### 5.1 查询默认模型（Models-Get default model）

### 5.2 查询可用的模型（Models-Get available models by type）



## 6.应用管理

### 6.1 获取应用列表(Apps-获取应用列表)

- 支持应用模式mode、应用名称name、标签ID tag_ids,is_created_by_me 四个个条件过滤
- mode 取值参考文档枚举

### 6.2 创建应用（Apps-创建应用)

- 上传图片支持icon和image两种方式

### 6.3 获取应用详情（Apps-获取应用详情)

### 6.4 删除应用（Apps-删除应用）

## 7. 应用服务流程

- 首先通过`6.2 创建应用（Apps-创建应用)` 创建出应用，并调用`6.3 获取应用详情（Apps-获取应用详情)`获取数据
- 能得到`site`(里面包含提供直接对话应用信息)和`model_config`  （模型配置）
- 主要更新``  配置如下
  - 模型配置（provider、name、parameters 等） 选择数据来源`5.2 查询可用的模型（Models-Get available models by type）以及调整模型参数（Models-Get model parameter rules）`
  - 开场白（opening_statement）  
  - 建议问题（suggested_questions）
  - 回答后的建议问题配置（suggested_questions_after_answer）
  - 语音转文本配置（speech_to_text）   需要的时候可以设置为true
  - 文本转语音配置（text_to_speech）    需要的时候可额外配置
  - "更多类似内容"功能配置（more_like_this）
  - 检索资源配置（retriever_resource）（引用和归属）
  - 敏感词回避配置（sensitive_word_avoidance）
  - 代理模式配置（agent_mode），包括工具列表
  - 文件上传配置（file_upload）
  - 用户输入表单配置（user_input_form)
- 修改完配置后调用保存并发布接口`(Model Configuration-Modify app model configuration)`
- 调试对话 `核心参数传入 model_config `
  - 开始对话`Chat-Generate chat message`
  - 结束对话`Chat-Stop chat message generation`

## 8. 对话功能(目前暂时使用嵌入方式)

## 9 知识库流程

- 获取知识库列表（支持展示1 文档·81 千字符·0 关联应用）

- 创建一个空的知识库`` Datasets-Create a dataset``
- 查询知识库下的文档`Documents-List documents in a dataset`
- 开始添加文件，先上传文件`Files-Upload a file`,需附带参数**source=datasets**
- 获取Embedding模型、Rerank模型
- 选择**分段设置**、**是否使用Embedding模型**、检索设置以及是否使用rerank模型
- **具体情况比较多，自己根据前端要实现那些功能，看接口使用吧**
