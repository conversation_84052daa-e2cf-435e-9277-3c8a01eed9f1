
import os
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill, Protection
from openpyxl.utils import get_column_letter
import warnings
import pandas as pd

warnings.filterwarnings('ignore')
def get_year_and_quarter(excel_file: str) -> tuple:
    """从Excel文件的第一行提取年份和季度信息"""
    # 加载工作簿并获取第一个工作表
    wb = load_workbook(excel_file, read_only=True)

    ws = wb.worksheets[0]


    # 获取第一行第一个单元格的值
    first_cell_value = ws.cell(row=1, column=1).value

    if first_cell_value and "年" in first_cell_value and "月" in first_cell_value:
        # 提取年份 (假设格式为"YYYY年X-X月...")
        year = first_cell_value.split("年")[0]
        # 提取季度信息 (如"2024年1-3月")
        quarter = first_cell_value.split("商洛")[0].strip()

        print(f"✅ 从第一行提取到: 年份={year}, 季度={quarter}")
        wb.close()
        return year, quarter
    raise ValueError(f"无法从Excel文件 {excel_file} 的任何sheet名称中提取年份")

def main(file1_path: str) -> dict:
    year, quarter= get_year_and_quarter(file1_path)
    return {
        "year": year,
        "quarter": quarter
    }

if __name__ == "__main__":
    file1_path = "./tmp/2024年1-3月商洛安康汉中县级分公司对标指标分析表.xlsx"
    result = main(file1_path)
    print(result)  # 输出 {'year': '2024', 'quarter': '1-3月'}