import os
import numpy as np
import pandas as pd
from openpyxl import Workbook,load_workbook
from openpyxl.styles import Alignment, Border, Side, Font,PatternFill, Protection
from openpyxl.utils import get_column_letter


def copy_cell_style(source_cell, target_cell):
    """
    完整复制单元格样式
    """
    try:
        # 复制字体
        if source_cell.font:
            target_cell.font = Font(
                name=source_cell.font.name,
                size=source_cell.font.size,
                bold=source_cell.font.bold,
                italic=source_cell.font.italic,
                vertAlign=source_cell.font.vertAlign,
                underline=source_cell.font.underline,
                strike=source_cell.font.strike,
                color=source_cell.font.color
            )

        # 复制填充（背景颜色）
        if source_cell.fill:
            target_cell.fill = PatternFill(
                fill_type=source_cell.fill.fill_type,
                start_color=source_cell.fill.start_color,
                end_color=source_cell.fill.end_color
            )

        # 复制边框
        if source_cell.border:
            target_cell.border = Border(
                left=Side(
                    border_style=source_cell.border.left.style,
                    color=source_cell.border.left.color
                ),
                right=Side(
                    border_style=source_cell.border.right.style,
                    color=source_cell.border.right.color
                ),
                top=Side(
                    border_style=source_cell.border.top.style,
                    color=source_cell.border.top.color
                ),
                bottom=Side(
                    border_style=source_cell.border.bottom.style,
                    color=source_cell.border.bottom.color
                )
            )

        # 复制对齐
        if source_cell.alignment:
            target_cell.alignment = Alignment(
                horizontal=source_cell.alignment.horizontal,
                vertical=source_cell.alignment.vertical,
                text_rotation=source_cell.alignment.text_rotation,
                wrap_text=source_cell.alignment.wrap_text,
                shrink_to_fit=source_cell.alignment.shrink_to_fit,
                indent=source_cell.alignment.indent
            )

        # 复制数字格式
        if source_cell.number_format:
            target_cell.number_format = source_cell.number_format

        # 复制保护
        if source_cell.protection:
            target_cell.protection = Protection(
                locked=source_cell.protection.locked,
                hidden=source_cell.protection.hidden
            )

    except Exception as e:
        # 如果某个样式复制失败，继续处理其他样式
        pass

def copy_worksheet_complete(source_wb, source_sheet_name, target_wb, target_sheet_name):
    """
    完整复制工作表，包括所有样式和格式
    """
    try:
        print(f"  正在复制工作表: {source_sheet_name} -> {target_sheet_name}")

        # 获取源工作表
        source_ws = source_wb[source_sheet_name]

        # 创建目标工作表
        if target_sheet_name in target_wb.sheetnames:
            target_wb.remove(target_wb[target_sheet_name])

        target_ws = target_wb.create_sheet(title=target_sheet_name)

        # 复制所有单元格的值和样式
        for row in range(1, source_ws.max_row + 1):
            for col in range(1, 100 + 1):  # source_ws.max_column
                source_cell = source_ws.cell(row=row, column=col)
                target_cell = target_ws.cell(row=row, column=col)

                # 复制值
                target_cell.value = source_cell.value

                # 复制样式
                copy_cell_style(source_cell, target_cell)

        # 复制行高
        for row_num in range(1, source_ws.max_row + 1):
            if row_num in source_ws.row_dimensions:
                if source_ws.row_dimensions[row_num].height:
                    target_ws.row_dimensions[row_num].height = source_ws.row_dimensions[row_num].height

        # 复制列宽
        for col_num in range(1, source_ws.max_column + 1):
            col_letter = get_column_letter(col_num)
            if col_letter in source_ws.column_dimensions:
                if source_ws.column_dimensions[col_letter].width:
                    target_ws.column_dimensions[col_letter].width = source_ws.column_dimensions[col_letter].width

        # 复制合并单元格
        for merged_range in source_ws.merged_cells.ranges:
            try:
                target_ws.merge_cells(str(merged_range))
            except Exception:
                pass

        # 复制工作表保护
        if source_ws.protection.sheet:
            target_ws.protection = source_ws.protection

        # 复制打印设置
        target_ws.page_setup = source_ws.page_setup
        target_ws.page_margins = source_ws.page_margins

        print(f"  ✓ 成功复制: {source_sheet_name} -> {target_sheet_name}")
        return True

    except Exception as e:
        print(f"  ✗ 复制失败: {source_sheet_name} -> {target_sheet_name}")
        print(f"    错误: {str(e)}")
        return False


def main(workflow_run_id, file_path, quarter,year: str):
    # ========== 参数 ==========
    current_quarter = quarter
    sheet_2024 = f"商洛安康汉中对标指标原始数据表"
    sheet_2023 = f"去年商洛安康汉中对标指标原始数据表"
    file_title = f"{current_quarter}商洛、安康、汉中县级分公司对标指标汇总表"
    dir_path = f"./tmp/upload_files/workflow/{workflow_run_id}/"
    os.makedirs(dir_path, exist_ok=True)
    output_path = f"{dir_path}{current_quarter}商洛安康汉中县级分公司对标指标分析表.xlsx"
    title = file_title

    city_map = {
        '商洛市公司': ['商州', '洛南', '丹凤', '商南', '山阳', '镇安', '柞水'],
        '安康市公司': ['汉滨', '旬阳', '白河', '紫阳', '平利', '汉阴', '石泉', '岚皋', '宁陕', '镇坪'],
        '汉中市公司': ['汉台', '南郑', '城固', '洋县', '勉县', '宁强', '略阳', '西乡', '镇巴', '留坝', '佛坪'],
    }
    cities = list(city_map.keys())
    shangluo_districts = city_map['商洛市公司']
    all_districts = []
    for city in cities:
        all_districts.extend(city_map[city])

    summary_blocks = ['排名前三位', '排名末三位', '我市在平均值以上的单位', '我市在平均值以上的单位数']

    # ========== 读取数据 ==========
    df_2024 = pd.read_excel(file_path, sheet_name=sheet_2024, header=[0, 1, 2])
    df_2023 = pd.read_excel(file_path, sheet_name=sheet_2023, header=[0, 1, 2])

    def find_col(df, keyword):
        for col in df.columns:
            if keyword in str(col[1]).replace(' ', ''):
                return col
        raise ValueError(f"找不到包含关键字“{keyword}”的列")

    seq_col_2024 = find_col(df_2024, '序号')
    indicator_col_2024 = find_col(df_2024, '指标')

    lower_better_names = [
        '人均费用总额（万元/人）',
        '单箱卷烟费用（元/箱）',
        '人工费用占销售收入比重（卷烟）（%）'
    ]

    def is_lower_better(name):
        return any(x in str(name).replace(' ', '') for x in lower_better_names)

    def is_valid_idx(row):
        val = row[seq_col_2024]
        ind = row[indicator_col_2024]
        if pd.isna(val) or pd.isna(ind): return False
        sval = str(val).strip()
        return sval.isdigit() or (sval.replace('.0', '').isdigit())

    valid_rows = [idx for idx, row in df_2024.iterrows() if is_valid_idx(row)]

    def get_top_n(names, values, n=3, lower_better=False):
        s = pd.Series(values, index=names)
        s_valid = s.dropna()
        if s_valid.empty:
            return ''
        ascending = lower_better
        s_sorted = s_valid.sort_values(ascending=ascending)
        unique_vals = s_sorted.unique()
        if len(unique_vals) < n:
            nth_val = unique_vals[-1]
        else:
            nth_val = unique_vals[n - 1]
        if ascending:
            result = s_sorted[s_sorted <= nth_val]
        else:
            result = s_sorted[s_sorted >= nth_val]
        return '、'.join(result.index.tolist())

    def get_bottom_n(names, values, n=3, lower_better=False):
        return get_top_n(names, values, n=n, lower_better=not lower_better)

    # ========== 构建四行表头 ==========
    header_row1 = [title] + [''] * (2 + 2 + 4 + len(all_districts) * 2 + len(summary_blocks) * 2 - 1)
    header_row2 = ['序号', '指标', '最优指标', '', '最优单位', '', '平均值', '']
    for city in cities:
        header_row2 += [city] + [''] * (2 * len(city_map[city]) - 1)
    header_row2 += sum([[x, ''] for x in summary_blocks], [])

    header_row3 = ['', '', '', '', '', '', '', '']
    for city in cities:
        for district in city_map[city]:
            header_row3 += [district, '']
    header_row3 += sum([['', ''] for _ in summary_blocks], [])

    header_row4 = ['', '', '本期', '同期', '本期', '同期', '本期', '同期']
    for city in cities:
        for district in city_map[city]:
            header_row4 += ['本期', '同期']
    header_row4 += ['本期', '同期'] * len(summary_blocks)

    # ========== 导出Excel ==========
    source_wb = load_workbook(file_path, data_only=False)
    wb = Workbook()
    copy_worksheet_complete(source_wb, f"商洛安康汉中对标指标原始数据表", wb,
                            f"商洛安康汉中对标指标原始数据表")
    copy_worksheet_complete(source_wb, f"去年商洛安康汉中对标指标原始数据表", wb,
                            f"去年商洛安康汉中对标指标原始数据表")
    if 'Sheet' in wb.sheetnames:
        wb.remove(wb['Sheet'])
    ws = wb.create_sheet()
    ws.title = '商洛安康汉中对标指标分析结果1'
    ws.append(header_row1)
    ws.append(header_row2)
    ws.append(header_row3)
    ws.append(header_row4)

    # ========== 合并单元格 ==========
    # 标题行合并
    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(header_row1))
    ws.merge_cells(start_row=2, start_column=3, end_row=3, end_column=4)
    ws.merge_cells(start_row=2, start_column=5, end_row=3, end_column=6)
    ws.merge_cells(start_row=2, start_column=7, end_row=3, end_column=8)

    col_offset = 9  # 2+2+2+2+1=9
    for city in cities:
        n = 2 * len(city_map[city])
        ws.merge_cells(start_row=2, start_column=col_offset, end_row=2, end_column=col_offset + n - 1)
        col_offset += n
    col_offset = 9
    for city in cities:
        for district in city_map[city]:
            ws.merge_cells(start_row=3, start_column=col_offset, end_row=3, end_column=col_offset + 1)
            col_offset += 2
    col_start = 9 + 2 * len(all_districts)
    for i in range(len(summary_blocks)):
        ws.merge_cells(start_row=2, start_column=col_start + i * 2, end_row=3, end_column=col_start + i * 2 + 1)


    # ========== 数据行 ==========
    for idx in valid_rows:
        indicator = df_2024.loc[idx, indicator_col_2024]
        if pd.isna(indicator): continue

        vals_2024, vals_2023 = [], []
        for city in cities:
            for district in city_map[city]:
                col_2024 = (df_2024.columns[0][0], city, district)
                col_2023 = (df_2023.columns[0][0], city, district)
                v2024 = df_2024.loc[idx, col_2024] if col_2024 in df_2024.columns else np.nan
                v2023 = df_2023.loc[idx, col_2023] if col_2023 in df_2023.columns else np.nan
                vals_2024.append(v2024)
                vals_2023.append(v2023)

        lower_better = is_lower_better(indicator)
        vals_2024_num = [x for x in vals_2024 if pd.notnull(x) and isinstance(x, (int, float, np.integer, np.floating))]
        vals_2023_num = [x for x in vals_2023 if pd.notnull(x) and isinstance(x, (int, float, np.integer, np.floating))]
        if vals_2024_num:
            best_val_2024 = min(vals_2024_num) if lower_better else max(vals_2024_num)
            best_district_2024 = all_districts[vals_2024.index(best_val_2024)]
        else:
            best_val_2024 = ''
            best_district_2024 = ''
        if vals_2023_num:
            best_val_2023 = min(vals_2023_num) if lower_better else max(vals_2023_num)
            best_district_2023 = all_districts[vals_2023.index(best_val_2023)]
        else:
            best_val_2023 = ''
            best_district_2023 = ''
        avg_2024 = np.mean(vals_2024_num) if vals_2024_num else ''
        avg_2023 = np.mean(vals_2023_num) if vals_2023_num else ''

        # 商洛市公司区县本期/同期指标值
        sl_vals_2024 = [vals_2024[all_districts.index(d)] for d in shangluo_districts]
        sl_vals_2023 = [vals_2023[all_districts.index(d)] for d in shangluo_districts]

        # “优于平均值”判断
        if lower_better:
            sl_above_names_2024 = [d for d, v in zip(shangluo_districts, sl_vals_2024) if
                                   pd.notnull(v) and v < avg_2024]
            sl_above_names_2023 = [d for d, v in zip(shangluo_districts, sl_vals_2023) if
                                   pd.notnull(v) and v < avg_2023]
        else:
            sl_above_names_2024 = [d for d, v in zip(shangluo_districts, sl_vals_2024) if
                                   pd.notnull(v) and v > avg_2024]
            sl_above_names_2023 = [d for d, v in zip(shangluo_districts, sl_vals_2023) if
                                   pd.notnull(v) and v > avg_2023]

        sl_above_str_2024 = '、'.join(sl_above_names_2024) if len(sl_above_names_2024) < len(
            shangluo_districts) else '全部'
        sl_above_str_2023 = '、'.join(sl_above_names_2023) if len(sl_above_names_2023) < len(
            shangluo_districts) else '全部'

        row = [
            int(float(df_2024.loc[idx, seq_col_2024])),
            str(indicator),
            round(best_val_2024, 2) if best_val_2024 != '' else '',
            round(best_val_2023, 2) if best_val_2023 != '' else '',
            best_district_2024,
            best_district_2023,
            round(avg_2024, 2) if avg_2024 != '' else '',
            round(avg_2023, 2) if avg_2023 != '' else '',
        ]
        for i in range(len(all_districts)):
            v2024 = vals_2024[i]
            v2023 = vals_2023[i]
            row += [
                round(v2024, 2) if isinstance(v2024, (int, float, np.integer, np.floating)) and pd.notnull(
                    v2024) else '',
                round(v2023, 2) if isinstance(v2023, (int, float, np.integer, np.floating)) and pd.notnull(
                    v2023) else '',
            ]
        row.append(get_top_n(all_districts, vals_2024, n=3, lower_better=lower_better))
        row.append(get_top_n(all_districts, vals_2023, n=3, lower_better=lower_better))
        row.append(get_bottom_n(all_districts, vals_2024, n=3, lower_better=lower_better))
        row.append(get_bottom_n(all_districts, vals_2023, n=3, lower_better=lower_better))
        row.append(sl_above_str_2024)
        row.append(sl_above_str_2023)
        row.append(len(sl_above_names_2024))
        row.append(len(sl_above_names_2023))
        ws.append(row)

        # ==== 排名行 ====
        rank_row = ['', '排名', '', '', '', '', '', '']
        if vals_2024_num:
            series = pd.Series(vals_2024)
            rank_2024 = series.rank(method='min', ascending=lower_better)
            rank_2024 = [int(r) if not pd.isnull(r) else '' for r in rank_2024]
        else:
            rank_2024 = ['' for _ in vals_2024]
        if vals_2023_num:
            series = pd.Series(vals_2023)
            rank_2023 = series.rank(method='min', ascending=lower_better)
            rank_2023 = [int(r) if not pd.isnull(r) else '' for r in rank_2023]
        else:
            rank_2023 = ['' for _ in vals_2023]
        for i in range(len(all_districts)):
            rank_row += [rank_2024[i], rank_2023[i]]
        rank_row += [''] * (len(summary_blocks) * 2)
        ws.append(rank_row)

    # ========== 统计“商洛市公司”改善指标数和排名提升数 ==========
    districts = shangluo_districts
    district_improve_total = {d: 0 for d in districts}
    district_rankup_total = {d: 0 for d in districts}
    for idx in valid_rows:
        indicator = df_2024.loc[idx, indicator_col_2024]
        if pd.isna(indicator): continue
        lower_better = is_lower_better(indicator)
        vals_2024, vals_2023 = [], []
        for city in cities:
            for district in city_map[city]:
                col_2024 = (df_2024.columns[0][0], city, district)
                col_2023 = (df_2023.columns[0][0], city, district)
                v2024 = df_2024.loc[idx, col_2024] if col_2024 in df_2024.columns else np.nan
                v2023 = df_2023.loc[idx, col_2023] if col_2023 in df_2023.columns else np.nan
                vals_2024.append(v2024)
                vals_2023.append(v2023)
        district_indices = [all_districts.index(d) for d in districts]
        for i, district in zip(district_indices, districts):
            v2024 = vals_2024[i]
            v2023 = vals_2023[i]
            if pd.notnull(v2024) and pd.notnull(v2023):
                if lower_better:
                    if v2024 < v2023:
                        district_improve_total[district] += 1
                else:
                    if v2024 > v2023:
                        district_improve_total[district] += 1
        s2024 = pd.Series([vals_2024[i] for i in district_indices])
        s2023 = pd.Series([vals_2023[i] for i in district_indices])
        rank_2024 = s2024.rank(method='min', ascending=lower_better)
        rank_2023 = s2023.rank(method='min', ascending=lower_better)
        for j, district in enumerate(districts):
            r2024 = rank_2024.iloc[j]
            r2023 = rank_2023.iloc[j]
            if not pd.isnull(r2024) and not pd.isnull(r2023):
                if r2024 < r2023:
                    district_rankup_total[district] += 1

    improve_row = [''] * len(header_row1)
    rankup_row = [''] * len(header_row1)
    improve_row[1] = '改善指标数'
    rankup_row[1] = '排名提升数'
    for i, district in enumerate(all_districts):
        idx = 7 + i * 2
        if district in districts:
            improve_row[idx + 1] = district_improve_total[district]
            rankup_row[idx + 1] = district_rankup_total[district]

    ws.append(improve_row)
    ws.append(rankup_row)
    ws.merge_cells(start_row=2, start_column=1, end_row=4, end_column=1)
    ws.merge_cells(start_row=2, start_column=2, end_row=4, end_column=2)
    ws.row_dimensions[1].height = 35
    # ========== 美化样式 ==========
    thin = Side(style='thin', color='000000')
    border = Border(left=thin, right=thin, top=thin, bottom=thin)
    for r in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
        for cell in r:
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = border
            if cell.row in [1, 2, 3, 4]:
                cell.font = Font(bold=True)
    for cell in ws[ws.max_row - 1]:
        cell.font = Font(bold=True)
    for cell in ws[ws.max_row]:
        cell.font = Font(bold=True)
    # ========== 新标签页“商洛安康汉中指标分析结果2” ==========
    header2 = [
        '序号', '指标', '最优指标', '最优单位', '平均',
        '排名前三位', '排名末三位', '我市高于平均值的单位', '我市高于平均值的单位数'
    ]

    ws2 = wb.create_sheet(title='商洛安康汉中指标分析结果2')

    # 标题行
    title2 = f"{current_quarter}商洛、安康、汉中三市县级分公司对标指标汇总表"
    ws2.append([title2] + [''] * (len(header2) - 1))
    ws2.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(header2))

    # 表头
    ws2.append(header2)

    for idx in valid_rows:
        indicator = df_2024.loc[idx, indicator_col_2024]
        if pd.isna(indicator):
            continue

        # 本期所有区县数据
        vals_2024 = []
        for city in cities:
            for district in city_map[city]:
                col_2024 = (df_2024.columns[0][0], city, district)
                v2024 = df_2024.loc[idx, col_2024] if col_2024 in df_2024.columns else np.nan
                vals_2024.append(v2024)
        lower_better = is_lower_better(indicator)
        vals_2024_num = [x for x in vals_2024 if pd.notnull(x) and isinstance(x, (int, float, np.integer, np.floating))]

        # 最优指标和单位
        if vals_2024_num:
            best_val_2024 = min(vals_2024_num) if lower_better else max(vals_2024_num)
            best_district_2024 = all_districts[vals_2024.index(best_val_2024)]
        else:
            best_val_2024 = ''
            best_district_2024 = ''
        avg_2024 = np.mean(vals_2024_num) if vals_2024_num else ''

        # 排名前三位
        s = pd.Series(vals_2024, index=all_districts)
        s_valid = s.dropna()
        ascending = lower_better
        s_sorted = s_valid.sort_values(ascending=ascending)
        unique_vals = s_sorted.unique()
        n = 3
        if len(unique_vals) < n:
            nth_val = unique_vals[-1]
        else:
            nth_val = unique_vals[n - 1]
        if ascending:
            top3 = s_sorted[s_sorted <= nth_val]
        else:
            top3 = s_sorted[s_sorted >= nth_val]
        top3_names = '、'.join(top3.index.tolist())

        # 排名末三位
        if ascending:
            bottom3 = s_sorted[s_sorted >= sorted(unique_vals)[-n]]
        else:
            bottom3 = s_sorted[s_sorted <= sorted(unique_vals)[n - 1]]
        if len(bottom3) < n:
            last3_names = '、'.join(s_sorted.iloc[-n:].index)
        else:
            last3_names = '、'.join(bottom3.index.tolist())

        # 商洛市高于平均值的单位
        shangluo_vals = [vals_2024[all_districts.index(d)] for d in shangluo_districts]
        if lower_better:
            # 越小越好，统计小于平均值的单位
            shangluo_names = [d for d, v in zip(shangluo_districts, shangluo_vals) if pd.notnull(v) and v < avg_2024]
        else:
            # 越大越好，统计大于平均值的单位
            shangluo_names = [d for d, v in zip(shangluo_districts, shangluo_vals) if pd.notnull(v) and v > avg_2024]
        shangluo_count = len(shangluo_names)
        shangluo_names_str = '、'.join(shangluo_names) if shangluo_count > 0 else ''

        ws2.append([
            int(float(df_2024.loc[idx, seq_col_2024])),
            str(indicator),
            round(best_val_2024, 2) if best_val_2024 != '' else '',
            best_district_2024,
            round(avg_2024, 2) if avg_2024 != '' else '',
            top3_names,
            last3_names,
            shangluo_names_str if shangluo_count != len(shangluo_districts) else '全部',
            shangluo_count
        ])

    # ========== 美化Sheet2 ==========
    thin = Side(style='thin', color='000000')
    border = Border(left=thin, right=thin, top=thin, bottom=thin)
    for r in ws2.iter_rows(min_row=1, max_row=ws2.max_row, min_col=1, max_col=ws2.max_column):
        for cell in r:
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = border
            if cell.row in [1, 2]:
                cell.font = Font(bold=True)
    # ========== Sheet3 商洛市公司各区县分布统计 ==========
    ws3 = wb.create_sheet(title='Sheet3')

    header3 = [
        '单位', '优于平均水平指标数', '排名前三指标数', '改善指标数',
        '指标排名提升数', '劣于平均水平指标数', '排名末三位指标数'
    ]

    # 标题行
    title3 = f"{current_quarter}我市各单位在三市县级分公司对标指标分布情况统计表"
    ws3.append([title3] + [''] * (len(header3) - 1))
    ws3.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(header3))
    ws3.append(header3)

    districts = shangluo_districts

    # 初始化计数字典
    result_count = {d: {
        '优于平均水平指标数': 0,
        '排名前三指标数': 0,
        '改善指标数': 0,
        '指标排名提升数': 0,
        '劣于平均水平指标数': 0,
        '排名末三位指标数': 0
    } for d in districts}

    for idx in valid_rows:
        indicator = df_2024.loc[idx, indicator_col_2024]
        if pd.isna(indicator): continue
        lower_better = is_lower_better(indicator)

        # --- 本期所有区县数值 ---
        vals_2024 = []
        for city in cities:
            for district in city_map[city]:
                col_2024 = (df_2024.columns[0][0], city, district)
                v2024 = df_2024.loc[idx, col_2024] if col_2024 in df_2024.columns else np.nan
                vals_2024.append(v2024)
        # 只取商洛市公司
        sl_idx = [all_districts.index(d) for d in districts]
        sl_vals = [vals_2024[i] for i in sl_idx]

        # --- 1. 优于平均水平指标数 ---
        avg_2024 = np.mean([v for v in vals_2024 if pd.notnull(v)])
        for d, v in zip(districts, sl_vals):
            if pd.notnull(v) and v > avg_2024:
                result_count[d]['优于平均水平指标数'] += 1
            if pd.notnull(v) and v < avg_2024:
                result_count[d]['劣于平均水平指标数'] += 1

        # --- 2. 排名前三指标数 ---
        s = pd.Series(vals_2024, index=all_districts)
        s_valid = s.dropna()
        n = 3
        ascending = lower_better
        s_sorted = s_valid.sort_values(ascending=ascending)
        unique_vals = s_sorted.unique()
        if len(unique_vals) < n:
            nth_val = unique_vals[-1]
        else:
            nth_val = unique_vals[n - 1]
        if ascending:
            top3 = s_sorted[s_sorted <= nth_val]
            last3 = s_sorted[s_sorted >= sorted(unique_vals)[-n]]
        else:
            top3 = s_sorted[s_sorted >= nth_val]
            last3 = s_sorted[s_sorted <= sorted(unique_vals)[n - 1]]
        for d in districts:
            if d in top3.index:
                result_count[d]['排名前三指标数'] += 1
            if d in last3.index:
                result_count[d]['排名末三位指标数'] += 1

        # --- 3. 改善指标数&指标排名提升数 ---
        # 找到本指标在同期区县的值
        vals_2023 = []
        for city in cities:
            for district in city_map[city]:
                col_2023 = (df_2023.columns[0][0], city, district)
                v2023 = df_2023.loc[idx, col_2023] if col_2023 in df_2023.columns else np.nan
                vals_2023.append(v2023)
        sl_vals_2023 = [vals_2023[i] for i in sl_idx]
        # 改善：本期比同期好
        for i, d in enumerate(districts):
            v_new = sl_vals[i]
            v_old = sl_vals_2023[i]
            if pd.notnull(v_new) and pd.notnull(v_old):
                if lower_better:
                    if v_new < v_old:
                        result_count[d]['改善指标数'] += 1
                else:
                    if v_new > v_old:
                        result_count[d]['改善指标数'] += 1
        # 排名提升
        s_2024 = pd.Series(sl_vals)
        s_2023 = pd.Series(sl_vals_2023)
        rank_2024 = s_2024.rank(method='min', ascending=lower_better)
        rank_2023 = s_2023.rank(method='min', ascending=lower_better)
        for i, d in enumerate(districts):
            if not pd.isnull(rank_2024[i]) and not pd.isnull(rank_2023[i]):
                if rank_2024[i] < rank_2023[i]:
                    result_count[d]['指标排名提升数'] += 1

    # 输出表三
    for d in districts:
        ws3.append([
            d,
            result_count[d]['优于平均水平指标数'],
            result_count[d]['排名前三指标数'],
            result_count[d]['改善指标数'],
            result_count[d]['指标排名提升数'],
            result_count[d]['劣于平均水平指标数'],
            result_count[d]['排名末三位指标数'],
        ])

    # ========== 美化 ==========
    for r in ws3.iter_rows(min_row=1, max_row=ws3.max_row, min_col=1, max_col=ws3.max_column):
        for cell in r:
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = border
            if cell.row in [1, 2]:
                cell.font = Font(bold=True)

    wb.save(output_path)
    return {"file1_path": output_path}


if __name__ == "__main__":
    # 手动指定要用的 workflow_run_id、本地 Excel 路径、年份
    workflow_run_id = "test_run"
    # 改成你的实际文件路径
    file_path = "./season/2024年1-3月商洛安康汉中县级分公司对标指标分析表.xlsx"
    quarter = "2024年1-3月"
    year = "2024"


    result = main(workflow_run_id, file_path,quarter, year)
