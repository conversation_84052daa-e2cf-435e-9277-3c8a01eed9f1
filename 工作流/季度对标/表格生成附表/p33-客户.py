import os
import pandas as pd
import copy
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.cell.cell import MergedCell
import warnings

warnings.filterwarnings('ignore')


def safe_cell(ws, row, col, value, bold=False, size=11):
    cell = ws.cell(row=row, column=col)
    if isinstance(cell, MergedCell):
        return
    cell.value = value
    cell.alignment = Alignment(horizontal='center', vertical='center')
    thin = Side(border_style='thin', color='000000')
    cell.border = Border(left=thin, right=thin, top=thin, bottom=thin)
    if bold:
        cell.font = Font(size=size, bold=True)


def copy_worksheet_complete(source_wb, source_sheet_name, target_wb, target_sheet_name):
    source_ws = source_wb[source_sheet_name]
    if target_sheet_name in target_wb.sheetnames:
        target_wb.remove(target_wb[target_sheet_name])
    target_ws = target_wb.create_sheet(title=target_sheet_name)
    for row in source_ws.iter_rows():
        for cell in row:
            if isinstance(cell, MergedCell):
                continue
            new_cell = target_ws.cell(row=cell.row, column=cell.column, value=cell.value)
            new_cell.font = copy.copy(cell.font)
            new_cell.border = copy.copy(cell.border)
            new_cell.alignment = copy.copy(cell.alignment)
            new_cell.fill = copy.copy(cell.fill)
            new_cell.number_format = cell.number_format
            new_cell.protection = copy.copy(cell.protection)
    for merged in source_ws.merged_cells.ranges:
        target_ws.merge_cells(str(merged))


def set_merged_cell_border(ws, start_row, start_col, end_row, end_col):
    thin = Side(border_style='thin', color='000000')
    for row in range(start_row, end_row + 1):
        for col in range(start_col, end_col + 1):
            ws.cell(row=row, column=col).border = Border(left=thin, right=thin, top=thin, bottom=thin)


def main(workflow_run_id, file_path, year, quarter):
    output_dir = f"./tmp/upload_files/workflow/{workflow_run_id}/"
    os.makedirs(output_dir, exist_ok=True)
    output_path = f"{output_dir}{quarter}客户服务部和客户经理对标指标分析表.xlsx"

    source_wb = load_workbook(file_path, data_only=False)
    sheet_name1 = '客户服务部原始数据'
    sheet_name2 = '客户经理对标原始数据'

    target_wb = Workbook()
    if "Sheet" in target_wb.sheetnames:
        target_wb.remove(target_wb["Sheet"])

    # 复制原表
    copy_worksheet_complete(source_wb, sheet_name1, target_wb, sheet_name1)
    copy_worksheet_complete(source_wb, sheet_name2, target_wb, sheet_name2)

    # ========== 表1 ==========
    df = pd.read_excel(file_path, sheet_name=sheet_name1, header=1)
    area_cols = df.columns[3:]
    ws1 = target_wb.create_sheet("客户服务部分析结果1")
    ws1.column_dimensions[get_column_letter(14)].width = 17.63
    ws1.column_dimensions[get_column_letter(13)].width = 17.63
    ws1.column_dimensions[get_column_letter(2)].width = 20.63

    ws1.row_dimensions[1].height = 35
    # 第1行 大标题
    ws1.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(area_cols) + 7)
    safe_cell(ws1, 1, 1, f"{quarter}全市系统县级分公司客户服务部对标指标汇总表", bold=True, size=20)

    # 第2行 表头
    header = ['序号', '指标名称', '最优指标', '最优单位', '平均值'] + list(area_cols) + ['排名前三位', '排名末三位']
    for j, h in enumerate(header, 1):
        safe_cell(ws1, 2, j, h, bold=True)
    summary_rows = []
    # 数据
    row_idx = 3
    for idx, row in df.iterrows():
        ws1.row_dimensions[row_idx].height = 22
        scores = pd.to_numeric(row[area_cols], errors='coerce').round(2)
        avg = round(scores.mean(), 2)
        max_val = round(scores.max(), 2)

        # 判断是否全部相等
        if (scores == scores.iloc[0]).all():
            best_area = "全部"
            top3 = "全部"
            last3 = ""
            ranks = [1] * len(scores)
        else:
            best_area = scores.idxmax()
            sorted_scores = scores.sort_values(ascending=False)
            top3 = '、'.join(sorted_scores.index[:3])
            last3 = '、'.join(sorted_scores.index[-3:])
            ranks = scores.rank(method='dense', ascending=False).astype(int).tolist()

        line = [row.iloc[0], row.iloc[1], max_val, best_area, avg] + list(scores.values) + [top3, last3]
        for j, v in enumerate(line, 1):
            safe_cell(ws1, row_idx, j, v)

        rank_line = ['', '排名', '', '', ''] + ranks + ['', '']
        for j, v in enumerate(rank_line, 1):
            safe_cell(ws1, row_idx + 1, j, v)
        summary_rows.append([row.iloc[0], row.iloc[1], max_val, best_area, avg, top3, last3])
        row_idx += 2

    # ========== 新增 简单汇总表 ==========
    ws3 = target_wb.create_sheet("客户服务部分析结果2")
    ws3.merge_cells(start_row=1, start_column=1, end_row=1, end_column=7)
    ws3.row_dimensions[1].height = 35
    ws3.column_dimensions[get_column_letter(6)].width = 17.63
    ws3.column_dimensions[get_column_letter(7)].width = 17.63
    ws3.column_dimensions[get_column_letter(2)].width = 20.63
    safe_cell(ws3, 1, 1, f"{quarter}全市系统基层客户服务部对标指标汇总表", bold=True, size=20)

    simple_header = ['序号', '指标名称', '最优指标', '最优单位', '平均值', '排名前三位', '排名末三位']
    for j, h in enumerate(simple_header, 1):
        safe_cell(ws3, 2, j, h, bold=True)

    row_idx = 3
    for line in summary_rows:
        ws3.row_dimensions[row_idx].height = 22
        for j, v in enumerate(line, 1):
            safe_cell(ws3, row_idx, j, v, size=11)
        row_idx += 1

    # 表2
    df2 = pd.read_excel(file_path, sheet_name='客户经理对标原始数据', header=[1, 2])
    df2 = df2.dropna(how='all').reset_index(drop=True)
    df2 = df2[~df2.astype(str).apply(lambda x: x.str.contains('备注')).any(axis=1)].reset_index(drop=True)

    ws2 = target_wb.create_sheet("客户经理对标数据分析结果")
    ws2.column_dimensions[get_column_letter(2)].width = 20.63
    ws2.row_dimensions[1].height = 35
    # 跳过计量单位列
    new_columns = [df2.columns[0], df2.columns[1]] + [col for col in df2.columns[3:]]

    # ========== 第1行大标题 ==========
    ws2.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(new_columns))
    safe_cell(ws2, 1, 1, f"{quarter}全市系统基层客户经理对标指标汇总表", bold=True, size=20)
    set_merged_cell_border(ws2, 1, 1, 1, len(new_columns))

    # ========== 第2行写单位 ==========
    cur_col = 3
    while cur_col <= len(new_columns):
        start = cur_col
        county = new_columns[cur_col - 1][0]
        while cur_col <= len(new_columns) and new_columns[cur_col - 1][0] == county:
            cur_col += 1
        ws2.merge_cells(start_row=2, start_column=start, end_row=2, end_column=cur_col - 1)
        safe_cell(ws2, 2, start, county, bold=True)
        set_merged_cell_border(ws2, 2, start, 2, cur_col - 1)

    # 左两列固定：序号、指标名称，并合并两行
    ws2.merge_cells(start_row=2, start_column=1, end_row=3, end_column=1)
    safe_cell(ws2, 2, 1, "序号", bold=True)
    set_merged_cell_border(ws2, 2, 1, 3, 1)

    ws2.merge_cells(start_row=2, start_column=2, end_row=3, end_column=2)
    safe_cell(ws2, 2, 2, "指标名称", bold=True)
    set_merged_cell_border(ws2, 2, 2, 3, 2)

    # ========== 第3行写人员名 ==========
    for j in range(3, len(new_columns) + 1):
        safe_cell(ws2, 3, j, new_columns[j - 1][1], bold=True)

    # ========== 填写数据和排名 ==========
    # ========== 填写数据行 ==========
    row_idx = 4
    scores_matrix = []

    for idx, row in df2.iterrows():
        ws2.row_dimensions[row_idx].height = 22
        values = [row[0], row[1]] + list(row[3:])
        for j, v in enumerate(values, 1):
            safe_cell(ws2, row_idx, j, v, size=11)
        scores = pd.to_numeric(row[3:], errors='coerce').fillna(0)
        scores_matrix.append(scores.values)
        row_idx += 1

    # ========== 增加合计行 ==========
    scores_matrix = pd.DataFrame(scores_matrix)
    totals_per_person = scores_matrix.sum(axis=0).round(2)  # 每个人总分
    ranks = totals_per_person.rank(method='dense', ascending=False).astype(int)

    # 合计行
    total_line = ["", "合计"] + list(totals_per_person.values)
    for j, v in enumerate(total_line, 1):
        safe_cell(ws2, row_idx, j, v)
    row_idx += 1

    # 综合排名行
    rank_line = ["", "综合排名"] + list(ranks.values)
    for j, v in enumerate(rank_line, 1):
        safe_cell(ws2, row_idx, j, v)

    target_wb.save(output_path)
    print(f"\n✅ 已生成文件: {output_path}")
    return {"file3_path": output_path}


if __name__ == "__main__":
    workflow_run_id = "test_run"
    file_path = "./season/2024年1-3月客户服务部和客户经理对标指标分析表.xlsx"
    quarter = "2024年1-3月"
    year = "2024"
    result = main(workflow_run_id, file_path, year, quarter)
    print(result)
