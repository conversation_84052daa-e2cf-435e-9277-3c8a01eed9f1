import os
import pandas as pd
import copy
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.cell.cell import MergedCell
import warnings

warnings.simplefilter(action='ignore', category=FutureWarning)


def safe_cell(ws, row, col, value, bold=False, size=11):
    cell = ws.cell(row=row, column=col)
    if isinstance(cell, MergedCell):
        return
    cell.value = value
    cell.alignment = Alignment(horizontal='center', vertical='center')
    thin = Side(border_style='thin', color='000000')
    cell.border = Border(left=thin, right=thin, top=thin, bottom=thin)
    if bold:
        cell.font = Font(size=size, bold=True)


def set_merged_cell_border(ws, start_row, start_col, end_row, end_col):
    thin = Side(border_style='thin', color='000000')
    border = Border(left=thin, right=thin, top=thin, bottom=thin)
    for row in range(start_row, end_row + 1):
        for col in range(start_col, end_col + 1):
            ws.cell(row=row, column=col).border = border


def copy_cell_style(source_cell, target_cell):
    target_cell.font = copy.copy(source_cell.font)
    target_cell.border = copy.copy(source_cell.border)
    target_cell.alignment = copy.copy(source_cell.alignment)
    target_cell.fill = copy.copy(source_cell.fill)
    target_cell.number_format = source_cell.number_format
    target_cell.protection = copy.copy(source_cell.protection)


def copy_worksheet_complete(source_wb, source_sheet_name, target_wb, target_sheet_name):
    source_ws = source_wb[source_sheet_name]
    if target_sheet_name in target_wb.sheetnames:
        target_wb.remove(target_wb[target_sheet_name])
    target_ws = target_wb.create_sheet(title=target_sheet_name)
    for row in source_ws.iter_rows():
        for cell in row:
            if isinstance(cell, MergedCell):
                continue
            new_cell = target_ws.cell(row=cell.row, column=cell.column, value=cell.value)
            copy_cell_style(cell, new_cell)
    for row_dim in source_ws.row_dimensions:
        target_ws.row_dimensions[row_dim].height = source_ws.row_dimensions[row_dim].height
    for col_dim in source_ws.column_dimensions:
        target_ws.column_dimensions[col_dim].width = source_ws.column_dimensions[col_dim].width
    for merged in source_ws.merged_cells.ranges:
        target_ws.merge_cells(str(merged))


def main(workflow_run_id, file_path, year: str, quarter: str):
    output_dir = f"./tmp/upload_files/workflow/{workflow_run_id}/"
    os.makedirs(output_dir, exist_ok=True)
    output_path = f"{output_dir}{quarter}专卖股和岗位对标指标分析表.xlsx"

    sheet_name1 = '基层专卖股原始数据'
    sheet_name2 = '专卖管理类岗位原始数据'
    source_wb = load_workbook(file_path, data_only=False)
    target_wb = Workbook()
    if "Sheet" in target_wb.sheetnames:
        target_wb.remove(target_wb["Sheet"])

    # ========== 复制原始两张表 ==========
    copy_worksheet_complete(source_wb, sheet_name1, target_wb, sheet_name1)
    copy_worksheet_complete(source_wb, sheet_name2, target_wb, sheet_name2)

    # ========== 表1 ==========
    df = pd.read_excel(file_path, sheet_name=sheet_name1, header=[1, 2])
    df = df[~df[('序号', 'Unnamed: 0_level_1')].astype(str).str.contains('计分办法', na=False)]
    df = df[~df[('序号', 'Unnamed: 0_level_1')].isnull()].reset_index(drop=True)
    df[('基本分值', 'Unnamed: 3_level_1')] = pd.to_numeric(df[('基本分值', 'Unnamed: 3_level_1')],
                                                           errors='coerce').fillna(0)

    score_cols = [(col[0], '得分') for col in df.columns if col[1] == '得分']
    area_names = [col[0] for col in score_cols]

    ws1 = target_wb.create_sheet("基础专卖股数据分析结果")
    ws1.merge_cells(start_row=1, start_column=1, end_row=1, end_column=15)
    ws1.column_dimensions[get_column_letter(14)].width = 17.63
    ws1.column_dimensions[get_column_letter(15)].width = 17.63
    ws1.column_dimensions[get_column_letter(2)].width = 16.5
    ws1.row_dimensions[1].height = 35
    safe_cell(ws1, 1, 1, f"{quarter}全市系统基层专卖股对标指标汇总表", bold=True,size=20)
    set_merged_cell_border(ws1, 1, 1, 1, 15)

    header = ['序号', '指标名称', '基本分值', '最优指标', '最优单位', '平均值'] + area_names + ['排名前三位',
                                                                                                '排名末三位']
    for j, h in enumerate(header, 1):
        safe_cell(ws1, 2, j, h, bold=True)

    row_idx = 3
    for idx, row in df.iterrows():
        sn = row[('序号', 'Unnamed: 0_level_1')]
        if str(sn) == '合计': continue
        name = row[('指标名称', 'Unnamed: 1_level_1')]
        base = row[('基本分值', 'Unnamed: 3_level_1')]
        scores = row[score_cols].astype(float)
        max_val = scores.max()
        max_area = scores.idxmax()[0]
        avg = int(round(scores.mean()))
        sorted_scores = scores.sort_values(ascending=False)
        top3 = '、'.join(sorted_scores.index[:3].map(lambda x: x[0]))
        last3 = '、'.join(sorted_scores.index[-3:].map(lambda x: x[0]))
        values = [sn, name, base, max_val, max_area, avg] + list(scores.values) + [top3, last3]
        for j, v in enumerate(values, 1):
            safe_cell(ws1, row_idx, j, v)
        ws1.row_dimensions[row_idx].height = 22
        rank_values = ['', '排名', '', '', '', ''] + list(
            scores.rank(method='dense', ascending=False).astype(int).values) + ["", ""]
        for j, v in enumerate(rank_values, 1):
            safe_cell(ws1, row_idx + 1, j, v)
        row_idx += 2

    sum_base = df.loc[df[('序号', 'Unnamed: 0_level_1')] != '合计', ('基本分值', 'Unnamed: 3_level_1')].sum()
    sum_scores = df.loc[df[('序号', 'Unnamed: 0_level_1')] != '合计', score_cols].sum()
    sum_max = sum_scores.max()
    sum_max_area = sum_scores.idxmax()[0]
    sum_avg = int(round(sum_scores.mean()))
    sorted_sum_scores = sum_scores.sort_values(ascending=False)
    sum_top3 = '、'.join(sorted_sum_scores.index[:3].map(lambda x: x[0]))
    sum_last3 = '、'.join(sorted_sum_scores.index[-3:].map(lambda x: x[0]))

    sum_line = ['', '合计', sum_base, sum_max, sum_max_area, sum_avg] + list(sum_scores.values) + [sum_top3, sum_last3]
    for j, v in enumerate(sum_line, 1):
        safe_cell(ws1, row_idx, j, v, bold=True)

    rank_line = ['', '排名', '', '', '', ''] + list(
        sum_scores.rank(method='dense', ascending=False).astype(int).values) + ["", ""]
    for j, v in enumerate(rank_line, 1):
        safe_cell(ws1, row_idx + 1, j, v, bold=True)

    # ========== 表2 ==========
    df2 = pd.read_excel(file_path, sheet_name=sheet_name2, header=[1, 2])
    df2 = df2[~df2.astype(str).apply(lambda x: x.str.contains('备注')).any(axis=1)].dropna(how='all').reset_index(
        drop=True)
    ws2 = target_wb.create_sheet("专卖管理类岗位数据分析结果")
    n_cols = len(df2.columns)
    ws2.merge_cells(start_row=1, start_column=1, end_row=1, end_column=n_cols)
    ws2.column_dimensions[get_column_letter(3)].width = 18.5
    ws2.row_dimensions[1].height = 35
    safe_cell(ws2, 1, 1, f"{quarter}全市系统专卖管理类岗位对标指标汇总表", bold=True,size=20)
    set_merged_cell_border(ws2, 1, 1, 1, n_cols)

    for j, col in enumerate(df2.columns, 1):
        # 计算县区位置，用于合并
        county_row = [col[0] for col in df2.columns]
        name_row = [col[1] for col in df2.columns]
        col_base = 7  # 从第7列开始是人员

        # 前面 1-6 列竖向合并
        for j in range(1, 7):
            ws2.merge_cells(start_row=2, start_column=j, end_row=3, end_column=j)
            safe_cell(ws2, 2, j, df2.columns[j - 1][0], bold=True)
            set_merged_cell_border(ws2, 2, j, 3, j)

        # 从第7列开始按县区合并
        cur = 0
        while col_base + cur <= len(df2.columns):
            start = cur
            county = county_row[col_base + cur - 1]
            while cur + 1 < len(county_row) - col_base + 1 and county_row[col_base + cur] == county:
                cur += 1
            end = cur
            ws2.merge_cells(start_row=2, start_column=col_base + start, end_row=2, end_column=col_base + end)
            safe_cell(ws2, 2, col_base + start, county, bold=True)
            set_merged_cell_border(ws2, 2, col_base + start, 2, col_base + end)
            # 第三行写人员名
            for c in range(start, end + 1):
                safe_cell(ws2, 3, col_base + c, name_row[col_base + c - 1], bold=True)
            cur += 1

    row_idx = 4
    for row in df2.values:
        ws2.row_dimensions[row_idx].height = 22
        for j, v in enumerate(row, 1): safe_cell(ws2, row_idx, j, v)
        row_idx += 1

    sum_row = df2[df2[('序号', 'Unnamed: 0_level_1')] == '合计'].iloc[0]
    sum_values = sum_row.drop(df2.columns[:6]).apply(pd.to_numeric, errors='coerce').fillna(0)
    ranks = sum_values.rank(method='dense', ascending=False).astype(int)

    ws2.merge_cells(start_row=row_idx - 1, start_column=1, end_row=row_idx - 1, end_column=5)
    ws2.merge_cells(start_row=4, start_column=2, end_row=5, end_column=2)
    ws2.merge_cells(start_row=6, start_column=2, end_row=9, end_column=2)

    ws2.merge_cells(start_row=row_idx, start_column=1, end_row=row_idx, end_column=5)
    safe_cell(ws2, row_idx, 1, "排名", bold=False)
    set_merged_cell_border(ws2, row_idx, 1, row_idx, 6)
    for j, v in enumerate(ranks.values, start=7):
        safe_cell(ws2, row_idx, j, v, bold=False)

    target_wb.save(output_path)
    print(f"\n✅ 已生成文件: {output_path}")
    return {"file4_path": output_path}


if __name__ == "__main__":
    workflow_run_id = "test_run"
    file_path = "./season/2024年1-3月专卖股和岗位对标指标分析表.xlsx"
    quarter = "2024年1-3月"
    year = "2024"
    result = main(workflow_run_id, file_path, year, quarter)
    print(result)
