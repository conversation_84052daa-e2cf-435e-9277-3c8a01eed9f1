import os
import numpy as np
import pandas as pd
import copy
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

# 样式
thin = Side(border_style="thin", color="000000")
border = Border(left=thin, right=thin, top=thin, bottom=thin)
align_center = Alignment(horizontal="center", vertical="center", wrap_text=True)
bold_font = Font(bold=True)


def safe_cell(ws, row, col, value, bold=False):
    cell = ws.cell(row=row, column=col, value=value)
    cell.border = border
    cell.alignment = align_center
    if bold:
        cell.font = bold_font


def get_rank(arr, lower_better):
    series = pd.to_numeric(pd.Series(arr), errors='coerce')
    ranks = series.rank(ascending=lower_better, method='min')
    return [int(x) if not np.isnan(x) else '' for x in ranks]


def get_result(delta, indicator_name, lower_better):
    try:
        delta = float(delta)
        if delta == 0:
            return '同比持平'
        if lower_better:
            return '同比改善' if delta < 0 else '劣于同期'
        else:
            return '同比改善' if delta > 0 else '劣于同期'
    except:
        return ''


def safe_float(val):
    try:
        f = float(val)
        return round(f, 2)
    except:
        return ''


def is_lower_better(indicator_name):
    lowers = ['人均水电费', '人均办公费', '修理费', '被查获异常流动真烟数', '查获异常流入真烟数']
    return any(x in str(indicator_name) for x in lowers)


def copy_sheet(source_ws, target_wb, target_sheet_name):
    target_ws = target_wb.create_sheet(title=target_sheet_name)
    for i, row in enumerate(source_ws.iter_rows(), 1):
        for j, cell in enumerate(row, 1):
            new_cell = target_ws.cell(row=i, column=j, value=cell.value)
            if cell.has_style:
                new_cell.font = copy.copy(cell.font)
                new_cell.border = copy.copy(cell.border)
                new_cell.fill = copy.copy(cell.fill)
                new_cell.number_format = copy.copy(cell.number_format)
                new_cell.protection = copy.copy(cell.protection)
                new_cell.alignment = copy.copy(cell.alignment)
    for merged_range in source_ws.merged_cells.ranges:
        target_ws.merge_cells(str(merged_range))


def main(workflow_run_id, file_path, year, quarter):
    output_dir = f"./tmp/upload_files/workflow/{workflow_run_id}/"
    os.makedirs(output_dir, exist_ok=True)
    output_path = f"{output_dir}{quarter}县级分公司对标指标分析表.xlsx"

    sheet_2024 = f"{year}年县级分公司原始数据"
    sheet_2023 = f"{int(year) - 1}年县级分公司原始数据"

    # 加载 pandas
    df_24 = pd.read_excel(file_path, sheet_name=sheet_2024, header=1)
    df_23 = pd.read_excel(file_path, sheet_name=sheet_2023, header=1)

    # 加载 openpyxl 用于复制原表
    source_wb = load_workbook(file_path, data_only=False)

    # 自动过滤掉备注列
    cols_base = [c for c in df_24.columns if '备注' not in str(c)]
    districts = cols_base[2:]
    print("检测到的区县列:", districts)

    wb = Workbook()
    wb.remove(wb.active)  # 清空默认sheet

    # ========== 复制两张原始数据表 ==========
    copy_sheet(source_wb[sheet_2024], wb, sheet_2024)
    copy_sheet(source_wb[sheet_2023], wb, sheet_2023)

    # ========== 生成表1 ==========
    ws = wb.create_sheet(f"{year}年县级分公司分析结果1")

    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=4 + 4 * len(districts))
    safe_cell(ws, 1, 1, f"{quarter}全市系统县级分公司对标指标汇总表", True)

    col = 1
    safe_cell(ws, 2, col, '序号', True);
    ws.merge_cells(start_row=2, end_row=3, start_column=col, end_column=col);
    col += 1
    safe_cell(ws, 2, col, '指标', True);
    ws.merge_cells(start_row=2, end_row=3, start_column=col, end_column=col);
    col += 1
    safe_cell(ws, 2, col, '最优指标', True);
    ws.merge_cells(start_row=2, end_row=2, start_column=col, end_column=col + 1);
    col += 2
    safe_cell(ws, 2, col, '最优单位', True);
    ws.merge_cells(start_row=2, end_row=2, start_column=col, end_column=col + 1);
    col += 2
    safe_cell(ws, 2, col, '平均', True);
    ws.merge_cells(start_row=2, end_row=2, start_column=col, end_column=col + 3);
    col += 4
    for d in districts:
        safe_cell(ws, 2, col, d, True)
        ws.merge_cells(start_row=2, end_row=2, start_column=col, end_column=col + 3)
        col += 4
    safe_cell(ws, 2, col, '排名前三位', True);
    ws.merge_cells(start_row=2, end_row=2, start_column=col, end_column=col + 1);
    col += 2
    safe_cell(ws, 2, col, '排名末三位', True);
    ws.merge_cells(start_row=2, end_row=2, start_column=col, end_column=col + 1)

    col = 3
    for _ in range(2): safe_cell(ws, 3, col, '本期', True); safe_cell(ws, 3, col + 1, '同期', True); col += 2
    safe_cell(ws, 3, col, '本期', True);
    safe_cell(ws, 3, col + 1, '同期', True);
    safe_cell(ws, 3, col + 2, '同比增减', True);
    safe_cell(ws, 3, col + 3, '结果评价', True);
    col += 4
    for _ in districts:
        safe_cell(ws, 3, col, '本期', True);
        safe_cell(ws, 3, col + 1, '同期', True);
        safe_cell(ws, 3, col + 2, '同比增减', True);
        safe_cell(ws, 3, col + 3, '结果评价', True);
        col += 4
    for _ in range(2): safe_cell(ws, 3, col, '本期', True); safe_cell(ws, 3, col + 1, '同期', True); col += 2

    improve_count = [0] * len(districts)
    rankup_count = [0] * len(districts)
    row_idx = 4

    for i in range(1, len(df_24)):
        seq = df_24.iloc[i, 0]
        indicator = df_24.iloc[i, 1]
        vals_24 = [safe_float(df_24.iloc[i][d]) for d in districts]
        vals_23 = [safe_float(df_23.iloc[i][d]) for d in districts]
        lower_better = is_lower_better(indicator)
        print(f"处理指标: {indicator} (越{'小' if lower_better else '大'}越好)")

        valid_24 = [v for v in vals_24 if v != '']
        valid_23 = [v for v in vals_23 if v != '']

        best_val_24 = min(valid_24) if lower_better else max(valid_24) if valid_24 else ''
        best_unit_24 = '全部' if len(set(valid_24)) == 1 else next(
            (districts[j] for j, v in enumerate(vals_24) if v == best_val_24), '全部') if best_val_24 != '' else ''
        best_val_23 = min(valid_23) if lower_better else max(valid_23) if valid_23 else ''
        best_unit_23 = '全部' if len(set(valid_23)) == 1 else next(
            (districts[j] for j, v in enumerate(vals_23) if v == best_val_23), '全部') if best_val_23 != '' else ''

        avg_24 = round(np.mean(valid_24), 2) if valid_24 else ''
        avg_23 = round(np.mean(valid_23), 2) if valid_23 else ''
        avg_delta = round(avg_24 - avg_23, 2) if avg_24 != '' and avg_23 != '' else ''
        avg_eval = get_result(avg_delta, indicator, lower_better)

        data_line = [seq, indicator, best_val_24, best_val_23, best_unit_24, best_unit_23, avg_24, avg_23, avg_delta,
                     avg_eval]
        for j in range(len(districts)):
            cur, pre = vals_24[j], vals_23[j]
            delta = round(cur - pre, 2) if cur != '' and pre != '' else ''
            eval_res = get_result(delta, indicator, lower_better)
            data_line += [cur, pre, delta, eval_res]
            if (lower_better and delta < 0) or (not lower_better and delta > 0): improve_count[j] += 1

        order_24 = np.argsort(vals_24) if lower_better else np.argsort(vals_24)[::-1]
        order_23 = np.argsort(vals_23) if lower_better else np.argsort(vals_23)[::-1]
        data_line += ['、'.join(np.array(districts)[order_24[:3]]), '、'.join(np.array(districts)[order_23[:3]])]
        data_line += ['、'.join(np.array(districts)[order_24[-3:]]), '、'.join(np.array(districts)[order_23[-3:]])]

        for col_idx, v in enumerate(data_line, 1): safe_cell(ws, row_idx, col_idx, v)
        row_idx += 1

        ranks_24 = get_rank(vals_24, lower_better)
        ranks_23 = get_rank(vals_23, lower_better)
        for j in range(len(districts)):
            if ranks_24[j] != '' and ranks_23[j] != '' and ranks_24[j] < ranks_23[j]: rankup_count[j] += 1
        rank_line = ['', '排名', '', '', '', '', '', '', '', '']
        for j in range(len(districts)): rank_line += [ranks_24[j], ranks_23[j], '', '']
        rank_line += ['', '', '', '']
        for col_idx, v in enumerate(rank_line, 1): safe_cell(ws, row_idx, col_idx, v)
        row_idx += 1

    improve_line = ['', '改善指标数', '', '', '', '', '', '', '', ''] + [n for cnt in improve_count for n in
                                                                         (cnt, '', '', '')] + ['', '', '', '']
    rankup_line = ['', '指标排名提升数', '', '', '', '', '', '', '', ''] + [n for cnt in rankup_count for n in
                                                                            (cnt, '', '', '')] + ['', '', '', '']
    for col_idx, v in enumerate(improve_line, 1): safe_cell(ws, row_idx, col_idx, v)
    row_idx += 1
    for col_idx, v in enumerate(rankup_line, 1): safe_cell(ws, row_idx, col_idx, v)

    # ========== 生成表2 ==========
    ws2 = wb.create_sheet(f"{year}年县级分公司分析结果2")
    ws2.merge_cells(start_row=1, start_column=1, end_row=1, end_column=9)
    safe_cell(ws2, 1, 1, f"{quarter}全市系统县级分公司对标指标汇总表", True)
    header2 = ['序号', '指标', '最优指标', '最优单位', '平均指标', '排名前三单位', '排名后三单位', '指标改善单位数',
               '指标排名提升单位数']
    for col_idx, h in enumerate(header2, 1): safe_cell(ws2, 2, col_idx, h, True)

    row2_idx = 3
    for i in range(1, len(df_24)):
        seq = df_24.iloc[i, 0]
        indicator = df_24.iloc[i, 1]
        lower_better = is_lower_better(indicator)
        vals_24 = [safe_float(df_24.iloc[i][d]) for d in districts]
        vals_23 = [safe_float(df_23.iloc[i][d]) for d in districts]
        valid_24 = [v for v in vals_24 if v != '']
        avg_24 = round(np.mean(valid_24), 2) if valid_24 else ''
        best_val_24 = min(valid_24) if lower_better else max(valid_24) if valid_24 else ''
        best_unit_24 = '全部' if len(set(valid_24)) == 1 else next(
            (districts[j] for j, v in enumerate(vals_24) if v == best_val_24), '全部')
        order_24 = np.argsort(vals_24) if lower_better else np.argsort(vals_24)[::-1]
        top3_units = '、'.join(np.array(districts)[order_24[:3]])
        last3_units = '、'.join(np.array(districts)[order_24[-3:]])
        improve_cnt = sum(1 for j in range(len(districts)) if
                          isinstance(vals_24[j], float) and isinstance(vals_23[j], float) and (
                                  (lower_better and vals_24[j] < vals_23[j]) or (
                                  not lower_better and vals_24[j] > vals_23[j])))
        ranks_24 = pd.Series(vals_24).rank(ascending=lower_better, method='dense').tolist()
        ranks_23 = pd.Series(vals_23).rank(ascending=lower_better, method='dense').tolist()
        rankup_cnt = sum(1 for j in range(len(districts)) if
                         not pd.isna(ranks_24[j]) and not pd.isna(ranks_23[j]) and ranks_24[j] < ranks_23[j])
        data_line2 = [seq - 1, indicator, best_val_24, best_unit_24, avg_24, top3_units, last3_units, improve_cnt,
                      rankup_cnt]
        for col_idx, v in enumerate(data_line2, 1): safe_cell(ws2, row2_idx, col_idx, v)
        row2_idx += 1
    ws2.column_dimensions['B'].width = 20.38
    ws2.column_dimensions['F'].width = 15.6
    ws2.column_dimensions['G'].width = 15.6

    wb.save(output_path)
    print(f"\n✅ 已生成文件: {output_path}")
    return {"file2_path": output_path}


if __name__ == "__main__":
    workflow_run_id = "test_run"
    file_path = "./season/2024年1-3月商洛县级分公司对标指标分析表.xlsx"
    quarter = "2024年1-3月"
    year = "2024"

    main(workflow_run_id, file_path, year, quarter)
