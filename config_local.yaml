database:
  host: "localhost"
  port: 25432
  user: "postgres"
  password: "difyai123456"
  dbname: "dify"

difyStoragePath: "F:/other code/DifyServer/docker_deploy/volumes/app/storage"
difyUrl: "http://localhost"
admins:
  - "<EMAIL>"
  - <EMAIL>
syncPlugin:
  - langgenius/siliconflow/siliconflow
  - langgenius/openai_api_compatible/openai_api_compatible
  - langgenius/huggingface_tei/huggingface_tei

logLevel: debug

# 模拟数据生成配置
mock_data:
  enabled: true                # 是否启用模拟数据生成
  mock_name: "myTest"        # 模拟用户名称
  mock_email: "<EMAIL>"  # 模拟用户邮箱
  app_percentage: 0.3          # 每天生成数据的app百分比 (30%)
  min_click_count: 5          # 最小点击数
  max_click_count: 15          # 最大点击数
  min_message_count: 5        # 最小消息数
  max_message_count: 15        # 最大消息数