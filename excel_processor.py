#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import json
import re

def chinese_to_pinyin(chinese_name):
    """
    将中文姓名转换为拼音
    这是一个简化版本，对于常见姓名应该能工作
    """
    # 常见姓氏拼音映射
    surname_map = {
        '李': 'li', '王': 'wang', '张': 'zhang', '刘': 'liu', '陈': 'chen',
        '杨': 'yang', '赵': 'zhao', '黄': 'huang', '周': 'zhou', '吴': 'wu',
        '徐': 'xu', '孙': 'sun', '胡': 'hu', '朱': 'zhu', '高': 'gao',
        '林': 'lin', '何': 'he', '郭': 'guo', '马': 'ma', '罗': 'luo',
        '梁': 'liang', '宋': 'song', '郑': 'zheng', '谢': 'xie', '韩': 'han',
        '唐': 'tang', '冯': 'feng', '于': 'yu', '董': 'dong', '萧': 'xiao',
        '程': 'cheng', '曹': 'cao', '袁': 'yuan', '邓': 'deng', '许': 'xu',
        '傅': 'fu', '沈': 'shen', '曾': 'zeng', '彭': 'peng', '吕': 'lv',
        '苏': 'su', '卢': 'lu', '蒋': 'jiang', '蔡': 'cai', '贾': 'jia',
        '丁': 'ding', '魏': 'wei', '薛': 'xue', '叶': 'ye', '阎': 'yan',
        '余': 'yu', '潘': 'pan', '杜': 'du', '戴': 'dai', '夏': 'xia',
        '钟': 'zhong', '汪': 'wang', '田': 'tian', '任': 'ren', '姜': 'jiang',
        '范': 'fan', '方': 'fang', '石': 'shi', '姚': 'yao', '谭': 'tan',
        '廖': 'liao', '邹': 'zou', '熊': 'xiong', '金': 'jin', '陆': 'lu',
        '郝': 'hao', '孔': 'kong', '白': 'bai', '崔': 'cui', '康': 'kang',
        '毛': 'mao', '邱': 'qiu', '秦': 'qin', '江': 'jiang', '史': 'shi',
        '顾': 'gu', '侯': 'hou', '邵': 'shao', '孟': 'meng', '龙': 'long',
        '万': 'wan', '段': 'duan', '漕': 'cao', '钱': 'qian', '汤': 'tang'
    }
    
    # 常见名字拼音映射
    name_map = {
        '伟': 'wei', '芳': 'fang', '娜': 'na', '秀': 'xiu', '敏': 'min',
        '静': 'jing', '丽': 'li', '强': 'qiang', '磊': 'lei', '军': 'jun',
        '洋': 'yang', '勇': 'yong', '艳': 'yan', '杰': 'jie', '娟': 'juan',
        '涛': 'tao', '明': 'ming', '超': 'chao', '秀英': 'xiuying', '华': 'hua',
        '健': 'jian', '辉': 'hui', '亮': 'liang', '佳': 'jia', '慧': 'hui',
        '鹏': 'peng', '霞': 'xia', '红': 'hong', '刚': 'gang', '平': 'ping',
        '燕': 'yan', '丹': 'dan', '阳': 'yang', '飞': 'fei', '玲': 'ling',
        '桂': 'gui', '英': 'ying', '梅': 'mei', '鑫': 'xin', '斌': 'bin',
        '升': 'sheng', '建': 'jian', '文': 'wen', '波': 'bo', '成': 'cheng',
        '荣': 'rong', '东': 'dong', '元': 'yuan', '春': 'chun', '学': 'xue',
        '义': 'yi', '康': 'kang', '星': 'xing', '光': 'guang', '天': 'tian',
        '达': 'da', '安': 'an', '岩': 'yan', '中': 'zhong', '茂': 'mao',
        '进': 'jin', '林': 'lin', '有': 'you', '坚': 'jian', '和': 'he',
        '彪': 'biao', '博': 'bo', '诚': 'cheng', '先': 'xian', '敬': 'jing',
        '震': 'zhen', '振': 'zhen', '壮': 'zhuang', '会': 'hui', '思': 'si',
        '群': 'qun', '豪': 'hao', '心': 'xin', '邦': 'bang', '承': 'cheng',
        '乐': 'le', '绍': 'shao', '功': 'gong', '松': 'song', '善': 'shan',
        '厚': 'hou', '庆': 'qing', '磊': 'lei', '民': 'min', '友': 'you',
        '裕': 'yu', '河': 'he', '哲': 'zhe', '江': 'jiang', '超': 'chao',
        '浩': 'hao', '亮': 'liang', '政': 'zheng', '谦': 'qian', '亨': 'heng',
        '奇': 'qi', '固': 'gu', '之': 'zhi', '轮': 'lun', '翰': 'han',
        '朗': 'lang', '伯': 'bo', '宏': 'hong', '言': 'yan', '若': 'ruo',
        '鸣': 'ming', '朋': 'peng', '斌': 'bin', '梁': 'liang', '栋': 'dong',
        '维': 'wei', '启': 'qi', '克': 'ke', '伦': 'lun', '翔': 'xiang',
        '旭': 'xu', '鹏': 'peng', '泽': 'ze', '晨': 'chen', '辰': 'chen',
        '士': 'shi', '以': 'yi', '建': 'jian', '家': 'jia', '致': 'zhi',
        '树': 'shu', '炎': 'yan', '德': 'de', '行': 'xing', '时': 'shi',
        '泰': 'tai', '盛': 'sheng', '雄': 'xiong', '琛': 'chen', '钧': 'jun',
        '冠': 'guan', '策': 'ce', '腾': 'teng', '楠': 'nan', '榕': 'rong',
        '风': 'feng', '航': 'hang', '弘': 'hong', '秀': 'xiu', '娴': 'xian',
        '婉': 'wan', '晓': 'xiao', '欢': 'huan', '霖': 'lin', '倩': 'qian',
        '云': 'yun', '嘉': 'jia', '雯': 'wen', '萱': 'xuan', '颖': 'ying',
        '娇': 'jiao', '姣': 'jiao', '婷': 'ting', '姿': 'zi', '琼': 'qiong',
        '勤': 'qin', '珍': 'zhen', '贞': 'zhen', '莉': 'li', '兰': 'lan',
        '凤': 'feng', '洁': 'jie', '梦': 'meng', '素': 'su', '梧': 'wu',
        '桐': 'tong', '蓓': 'bei', '莎': 'sha', '锦': 'jin', '黛': 'dai',
        '青': 'qing', '倩': 'qian', '婕': 'jie', '馨': 'xin', '瑗': 'yuan',
        '琰': 'yan', '韵': 'yun', '融': 'rong', '园': 'yuan', '艺': 'yi',
        '咏': 'yong', '卿': 'qing', '聪': 'cong', '澜': 'lan', '纯': 'chun',
        '毓': 'yu', '悦': 'yue', '昭': 'zhao', '冰': 'bing', '爽': 'shuang',
        '琬': 'wan', '茗': 'ming', '羽': 'yu', '希': 'xi', '宁': 'ning',
        '欣': 'xin', '飘': 'piao', '育': 'yu', '滢': 'ying', '馥': 'fu',
        '筠': 'yun', '柔': 'rou', '竹': 'zhu', '霭': 'ai', '凝': 'ning',
        '晓': 'xiao', '欢': 'huan', '霄': 'xiao', '枫': 'feng', '芸': 'yun',
        '菲': 'fei', '寒': 'han', '伊': 'yi', '亚': 'ya', '宜': 'yi',
        '可': 'ke', '姬': 'ji', '舒': 'shu', '影': 'ying', '荔': 'li',
        '枝': 'zhi', '思': 'si', '丽': 'li',
        # 添加更多常见字符的拼音
        '骥': 'ji', '远': 'yuan', '鸿': 'hong', '喻': 'yu', '永': 'yong',
        '扬': 'yang', '峰': 'feng', '少': 'shao', '宏': 'hong', '雯': 'wen',
        '智': 'zhi', '良': 'liang', '社': 'she', '岗': 'gang',
        '志': 'zhi', '淑': 'shu', '娥': 'e', '伟': 'wei',
        '凯': 'kai', '泽': 'ze', '宇': 'yu', '旻': 'min',
        '慎': 'shen', '喜': 'xi', '文': 'wen', '斐': 'fei', '丹': 'dan',
        '军': 'jun', '晓': 'xiao', '凡': 'fan', '建': 'jian', '民': 'min',
        '维': 'wei', '华': 'hua', '涛': 'tao', '家': 'jia', '怡': 'yi',
        '宁': 'ning', '静': 'jing', '驰': 'chi', '柏': 'bai', '彦': 'yan',
        '雪': 'xue', '珊': 'shan', '小': 'xiao', '辉': 'hui', '雁': 'yan',
        '冰': 'bing', '叶': 'ye', '若': 'ruo', '洋': 'yang', '弘': 'hong',
        '致': 'zhi', '源': 'yuan', '悦': 'yue', '昱': 'yu', '思': 'si',
        '勉': 'mian', '斌': 'bin', '俊': 'jun', '钊': 'zhao', '泉': 'quan',
        '艳': 'yan', '琳': 'lin', '红': 'hong', '莉': 'li', '萌': 'meng',
        '宵': 'xiao', '飞': 'fei', '欢': 'huan', '引': 'yin', '启': 'qi',
        '超': 'chao', '端': 'duan', '芳': 'fang', '班': 'ban', '平': 'ping',
        '娜': 'na', '佳': 'jia', '宝': 'bao', '金': 'jin', '婉': 'wan',
        '娴': 'xian', '梦': 'meng', '昕': 'xin', '勇': 'yong', '泳': 'yong',
        '堃': 'kun', '鹏': 'peng', '朵': 'duo', '彤': 'tong', '杰': 'jie',
        '博': 'bo', '力': 'li', '锐': 'rui', '琥': 'hu', '强': 'qiang',
        '磊': 'lei', '刚': 'gang', '张': 'zhang', '朋': 'peng', '楠': 'nan',
        '老': 'lao', '虎': 'hu', '一': 'yi', '霄': 'xiao', '有': 'you',
        '滋': 'zi', '魁': 'kui', '敏': 'min', '杉': 'shan', '东': 'dong',
        '雯': 'wen', '子': 'zi', '涵': 'han', '婷': 'ting', '锋': 'feng',
        '越': 'yue', '国': 'guo', '喆': 'zhe', '宁': 'ning', '娟': 'juan',
        '盺': 'xi', '玥': 'yue', '剑': 'jian', '皓': 'hao', '振': 'zhen',
        '波': 'bo', '正': 'zheng', '茂': 'mao', '月': 'yue', '春': 'chun',
        '煜': 'yu', '琨': 'kun', '柯': 'ke', '英': 'ying', '树': 'shu',
        '勋': 'xun', '诗': 'shi', '珺': 'jun', '美': 'mei', '玲': 'ling',
        '勃': 'bo', '仁': 'ren', '德': 'de', '武': 'wu', '光': 'guang',
        '慧': 'hui', '骆': 'luo', '项': 'xiang', '乔': 'qiao', '鹿': 'lu',
        '汪': 'wang', '殷': 'yin', '冀': 'ji', '阮': 'ruan', '廖': 'liao',
        '曹': 'cao', '惠': 'hui', '孙': 'sun', '詹': 'zhan', '解': 'xie',
        '邹': 'zou', '薛': 'xue', '雷': 'lei', '吕': 'lv', '丁': 'ding',
        '崔': 'cui', '沈': 'shen', '董': 'dong', '罗': 'luo', '井': 'jing',
        '屈': 'qu', '田': 'tian', '周': 'zhou', '冯': 'feng', '任': 'ren',
        '范': 'fan', '程': 'cheng', '赵': 'zhao', '方': 'fang', '魏': 'wei',
        '林': 'lin', '陈': 'chen', '毛': 'mao', '刘': 'liu', '胡': 'hu',
        '王': 'wang', '关': 'guan', '祝': 'zhu', '郭': 'guo', '候': 'hou',
        '费': 'fei', '彭': 'peng', '岳': 'yue', '庞': 'pang', '和': 'he',
        '蔡': 'cai', '马': 'ma'
    }
    
    if not chinese_name or len(chinese_name) < 2:
        return "unknown"
    
    # 移除空格和特殊字符
    chinese_name = re.sub(r'[^\u4e00-\u9fff]', '', chinese_name)
    
    if len(chinese_name) < 2:
        return "unknown"
    
    # 处理姓氏
    surname = chinese_name[0]
    surname_pinyin = surname_map.get(surname, surname.lower())
    
    # 处理名字
    given_name = chinese_name[1:]
    given_name_pinyin = ""
    
    # 如果是两个字的名字，尝试整体匹配
    if len(given_name) == 2 and given_name in name_map:
        given_name_pinyin = name_map[given_name]
    else:
        # 逐字匹配
        for char in given_name:
            char_pinyin = name_map.get(char, char.lower())
            given_name_pinyin += char_pinyin
    
    return surname_pinyin + given_name_pinyin

def process_excel_file(file_path):
    """
    处理Excel文件并返回JSON格式数据
    """
    try:
        # 读取Excel文件，跳过第一行，使用第二行作为表头
        df = pd.read_excel(file_path, header=1)

        print("Excel文件列名:")
        print(df.columns.tolist())
        print("\n前几行数据:")
        print(df.head(10))

        # 根据实际的Excel结构，列应该是：单位、部门、姓名、备注
        # 重新命名列
        if len(df.columns) >= 4:
            df.columns = ['单位', '部门', '姓名', '备注']
        elif len(df.columns) >= 3:
            df.columns = ['单位', '部门', '姓名']
        else:
            print("Excel文件格式不正确，列数不足")
            return None

        print(f"\n重新命名后的列名: {df.columns.tolist()}")

        # 现在可以正确使用列名
        name_col = '姓名'
        dept_col = '部门'
        unit_col = '单位'
        
        result = []
        
        for index, row in df.iterrows():
            name = str(row[name_col]).strip() if pd.notna(row[name_col]) else ""
            
            if not name or name == 'nan':
                continue
                
            # 生成邮箱
            pinyin_name = chinese_to_pinyin(name)
            email = f"{pinyin_name}@slyc.com"
            
            # 获取部门和单位信息
            department = str(row[dept_col]).strip() if dept_col and pd.notna(row[dept_col]) else ""
            unit = str(row[unit_col]).strip() if unit_col and pd.notna(row[unit_col]) else ""
            
            # 如果部门或单位为空，使用默认值
            if not department or department == 'nan':
                department = "unknown"
            if not unit or unit == 'nan':
                unit = "unknown"
            
            user_data = {
                "email": email,
                "name": name,
                "department": department,
                "unit": unit
            }
            
            result.append(user_data)
        
        return result
        
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        return None

if __name__ == "__main__":
    file_path = "工作流/导入用户/副本AI平台测试账号申请表(1)(1).xlsx"
    
    result = process_excel_file(file_path)
    
    if result:
        # 输出JSON格式
        json_output = json.dumps(result, ensure_ascii=False, indent=2)
        print("\n生成的JSON数据:")
        print(json_output)
        
        # 保存到文件
        with open("user_data.json", "w", encoding="utf-8") as f:
            f.write(json_output)
        print("\n数据已保存到 user_data.json 文件")
    else:
        print("处理失败，请检查Excel文件格式")
