import React, { useState, useEffect } from 'react';
import { Select, message } from 'antd';
import { TeamOutlined } from '@ant-design/icons';
import { tenantApi } from '../services/api';
import { Tenant } from '../types';

const { Option } = Select;

interface TeamSwitcherProps {
  onTeamSwitch?: () => void; // 团队切换后的回调函数，用于刷新数据
}

const TeamSwitcher: React.FC<TeamSwitcherProps> = ({ onTeamSwitch }) => {
  const [teams, setTeams] = useState<Tenant[]>([]);
  const [currentTeamId, setCurrentTeamId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  // 获取团队列表
  const fetchTeams = async () => {
    try {
      const response = await tenantApi.getTenants(1);
      setTeams(response.data.data || []);
    } catch (error) {
      console.error('获取团队列表失败:', error);
      message.error('获取团队列表失败');
    }
  };

  // 获取当前团队
  const fetchCurrentTeam = async () => {
    try {
      const response = await tenantApi.getCurrentTenant();
      setCurrentTeamId(response.data.id || '');
    } catch (error) {
      console.error('获取当前团队失败:', error);
    }
  };

  // 切换团队
  const handleTeamChange = async (teamId: string) => {
    setLoading(true);
    try {
      await tenantApi.switchTenant(teamId);
      setCurrentTeamId(teamId);
      message.success('切换团队成功');
      
      // 调用回调函数刷新数据
      if (onTeamSwitch) {
        onTeamSwitch();
      }
    } catch (error) {
      console.error('切换团队失败:', error);
      message.error('切换团队失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeams();
    fetchCurrentTeam();
  }, []);

  return (
    <div>
      <Select
        style={{ width: '100%' }}
        value={currentTeamId}
        onChange={handleTeamChange}
        loading={loading}
        placeholder="请选择团队"
        disabled={loading}
        size="middle"
      >
        {teams.map(team => (
          <Option key={team.ID} value={team.ID}>
            {team.Name}
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default TeamSwitcher;
