import React, { useState,  useRef } from 'react';
import { Spin, Alert } from 'antd';

const DifyApps: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  // 嵌入的URL
  const embedUrl = 'http://localhost/apps';
  
  // 处理iframe加载事件
  const handleIframeLoad = () => {
    setLoading(false);
    
    try {
      // 第一种方法：注入CSS
      if (iframeRef.current?.contentDocument) {
        const style = iframeRef.current.contentDocument.createElement('style');
        style.textContent = `
          /* 隐藏导航栏 - 使用多种选择器 */
          div[class="sticky top-0 left-0 right-0 z-30 flex flex-col grow-0 shrink-0 basis-auto min-h-[56px] border-b border-divider-regular"],
          div[class="sticky top-0 left-0 right-0 z-30 flex flex-col grow-0 shrink-0 basis-auto min-h-[56px]"],
            div[class="flex h-full flex-col border-l-[0.5px] border-divider-regular"],
           footer.shrink-0.grow-0.px-12.py-6 {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            pointer-events: none !important;
          }

        `;
        iframeRef.current.contentDocument.head.appendChild(style);
        
        
      }
    } catch (error) {
      console.error('隐藏导航栏失败:', error);
    }
  };
  
  // 处理iframe错误
  const handleIframeError = () => {
    setError('无法加载嵌入页面，请检查URL是否正确或者目标服务是否可用');
    setLoading(false);
  };
  
  return (
    <div style={{ width: '100%', height: 'calc(100vh - 112px)', position: 'relative' }}>
      {loading && (
        <div style={{ 
          position: 'absolute', 
          top: '50%', 
          left: '50%', 
          transform: 'translate(-50%, -50%)' 
        }}>
          <Spin tip="正在加载..." size="large" />
        </div>
      )}
      
      {error && (
        <Alert
          message="加载错误"
          description={error}
          type="error"
          showIcon
          style={{ maxWidth: '80%', margin: '20px auto' }}
        />
      )}
      
      <iframe
        ref={iframeRef}
        id="embedded-frame"
        src={embedUrl}
        title="Explore Embedded Page"
        width="100%"
        height="100%"
        style={{ 
          border: 'none',
          display: loading ? 'none' : 'block'
        }}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
      />
    </div>
  );
};

export default DifyApps;
