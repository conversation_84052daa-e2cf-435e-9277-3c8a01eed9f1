import React from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { accountApi } from '../services/api';

const Login: React.FC = () => {
  const navigate = useNavigate();
  
  // 修改检查登录状态的逻辑
  React.useEffect(() => {
    const console_token = localStorage.getItem('console_token');
    if (console_token) {
      try {
        // 只有当用户数据和token都存在时才重定向
        if (console_token) {
          navigate('/accounts');
        }
      } catch (e) {
        // 如果解析失败，清除无效的用户数据
        localStorage.removeItem('user');
      }
    }
  }, [navigate]);

  const onFinish = async (values: { email: string; password: string }) => {
    try {
      const response = await accountApi.login(values);
      // 现在 response.data 可能已经被响应拦截器展平，或者保持原来的结构
      const userData = response.data;
      if (userData) {
        // 判断是否有 token，如果没有，可能是仍然包含在 data 属性里
        const token = userData.token || (userData.data && userData.data.token);
        const refreshToken = userData.refresh_token || (userData.data && userData.data.refresh_token);
        
        if (token && refreshToken) {
          localStorage.setItem('user', JSON.stringify(userData));
          localStorage.setItem("console_token", token);
          localStorage.setItem("refresh_token", refreshToken);
          setTimeout(() => {
            navigate('/accounts');
          }, 1000);
        } else {
          message.error('登录响应缺失必要信息');
        }
      }
    } catch (error: any) {
      // 错误处理现在已由拦截器统一处理，这里只需要显示错误消息
      if (error.status === 401 || error.code === 'UNAUTHORIZED') {
        message.error(error.message || '用户名或密码错误');
      } else {
        message.error(error.message || '登录失败，请稍后重试');
      }
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      background: '#f0f2f5' 
    }}>
      <Card title="用户登录" style={{ width: 400 }}>
        <Form
          name="login"
          onFinish={onFinish}
          layout="vertical"
        >
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 8, message: '密码长度至少为8位' }
            ]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login;