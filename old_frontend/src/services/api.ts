import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// const api = axios.create({
//     baseURL: 'http://localhost:8080/api'
// });
const baseURL = 'http://localhost:8080/api' || '/api';

// 创建一个新的axios实例
const api = axios.create({
    baseURL,
});

// 控制刷新token的状态
let isRefreshing = false;
// 存储等待token刷新的请求队列
let refreshSubscribers: ((token: string) => void)[] = [];

// 添加请求拦截器
api.interceptors.request.use(config => {
    // 确保headers对象存在
    config.headers = config.headers || {};
    const console_token = localStorage.getItem('console_token');
    if (console_token) {
        config.headers.Authorization = `Bearer ${console_token}`;
    }
    return config;
});

// 刷新token的函数
const refreshToken = async (): Promise<string> => {
    try {
        const refresh_token = localStorage.getItem('refresh_token');
        if (!refresh_token) {
            throw new Error('No refresh token available');
        }
        
        // 调用刷新token的API
        const response = await axios.post(`${baseURL}/refresh_token.json`, {
            refresh_token: refresh_token
        });
        
        // 处理响应数据，提取新的token
        let newToken = '';
        let newRefreshToken = '';
        
        // 根据响应格式解析token
        if (response.data.result === 'success') {
            // 如果是DifyResult格式
            newToken = response.data.data.token;
            newRefreshToken = response.data.data.refresh_token;
        } else if (response.data.token) {
            // 如果是直接包含token的格式
            newToken = response.data.token;
            newRefreshToken = response.data.refresh_token;
        }
        
        if (newToken && newRefreshToken) {
            // 更新本地存储的token
            localStorage.setItem('console_token', newToken);
            localStorage.setItem('refresh_token', newRefreshToken);
            return newToken;
        } else {
            throw new Error('Invalid token response');
        }
    } catch (error) {
        console.error('Token refresh failed:', error);
        // 清除登录状态
        localStorage.removeItem('user');
        localStorage.removeItem('console_token');
        localStorage.removeItem('refresh_token');
        // 跳转到登录页
        window.location.href = '/login';
        throw error;
    }
};

// 将请求添加到等待队列
const subscribeTokenRefresh = (callback: (token: string) => void) => {
    refreshSubscribers.push(callback);
};

// 刷新token成功后执行队列中的请求
const onRefreshed = (token: string) => {
    refreshSubscribers.forEach(callback => callback(token));
    refreshSubscribers = [];
};

// 添加响应拦截器
api.interceptors.response.use(
    response => {
        // 检查是否是错误响应 (当HTTP状态码是200但响应体中包含 code 字段时)
        if (response.status === 200 && response.data.code) {
            // 这是一个错误响应，构建错误对象
            const error = new Error(response.data.message || '未知错误');
            // @ts-ignore
            error.code = response.data.code;
            // @ts-ignore
            error.status = response.data.status;
            // @ts-ignore
            error.response = { data: response.data };
            
            // 如果是未授权错误，尝试刷新token
            if (response.data.status === 401 && response.config && !response.config.url?.includes('/refresh_token.json')) {
                return handleTokenRefresh(response);
            }
            
            return Promise.reject(error);
        }
        
        // 如果是成功响应，判断是否有 result 字段
        if (response.data && typeof response.data === 'object' && response.data.result === 'success') {
            // 将 DifyResult 结构展平，保持与现有前端代码兼容
            const originalData = response.data;
            response.data = originalData.data;
        }
        
        return response;
    },
    (error: AxiosError) => {
        // 处理网络错误或服务器响应的非 200 状态码
        const originalRequest = error.config;
        console.log('error', error);
        console.log('error status', error.response?.status)
        // 检查是否是token过期错误
        if (error.response?.status === 401 && originalRequest && !originalRequest.url?.includes('/refresh_token.json')) {
            return handleTokenRefresh(error);
        }
        
        return Promise.reject(error);
    }
);

// 处理token刷新和请求重试的函数
const handleTokenRefresh = async (errorOrResponse: AxiosError | AxiosResponse) => {
    // 确保config存在并且是AxiosRequestConfig类型
    if (!('config' in errorOrResponse) || !errorOrResponse.config) {
        // 没有config属性或config为undefined，无法处理
        return Promise.reject(errorOrResponse);
    }
    
    // 此时TypeScript知道config存在且非空
    const originalRequest = errorOrResponse.config as AxiosRequestConfig;
    
    // 如果当前没有在刷新token
    if (!isRefreshing) {
        isRefreshing = true;
        
        try {
            // 刷新token
            const newToken = await refreshToken();
            
            // 刷新成功，执行所有等待的请求
            onRefreshed(newToken);
            
            // 重试当前请求
            if (originalRequest.headers) {
                originalRequest.headers.Authorization = `Bearer ${newToken}`;
            }
            return axios(originalRequest);
        } catch (error) {
            return Promise.reject(error);
        } finally {
            isRefreshing = false;
        }
    } else {
        // 如果已经在刷新token，则将当前请求加入等待队列
        return new Promise<AxiosResponse>(resolve => {
            subscribeTokenRefresh(token => {
                // 更新请求的Authorization header
                if (originalRequest.headers) {
                    originalRequest.headers.Authorization = `Bearer ${token}`;
                }
                // 重试请求
                resolve(axios(originalRequest));
            });
        });
    }
};

// 刷新token API
export const authApi = {
    refreshToken: () => {
        const refresh_token = localStorage.getItem('refresh_token');
        return api.post('/refresh_token.json', { refresh_token });
    }
};

export const accountApi = {
    login: (data: { email: string; password: string }) =>
        api.post('/login.json', data),
    getAccounts: (page: number) =>
        api.get('/accounts.json', { params: { page } }),
    addAccount: (data: { name: string; email: string }) =>
        api.post('/add_account.json', data),
    deleteAccount: (id: string) =>
        api.post('/del_account.json', { id }),
    setPassword: (id: string, password: string) =>
        api.post('/set_account_password.json', { id, password }),
};

export const tenantApi = {
    getTenants: (page: number) =>
        api.get('/tenants.json', { params: { page } }),
    addTenant: (data: {
        name: string;
        plan: 'basic' | 'pro' | 'enterprise';
        status: string;
    }) =>
        api.post('/add_tenant.json', data),
    deleteTenant: (tenantId: string) =>
        api.post('/del_tenant.json', { tenant_id: tenantId }),
    getCurrentTenant: () =>
        api.get('/current_tenant.json'),
    switchTenant: (tenantId: string) =>
        api.post('/switch_tenant.json', { tenant_id: tenantId }),
};

export const datasetApi = {
    getDatasets: (page: number) =>
        api.get('/datasets.json', { params: { page } }),
    addDataset: (data: {
        name: string;
        description: string;
        permission: string;
        data_source_type: string;
        indexing_technique: string;
    }) =>
        api.post('/add_dataset.json', data),
    addDatasetTenant: (datasetId: string, tenantId: string) =>
        api.post('/add_dataset_tenant.json', { dataset_id: datasetId, tenant_id: tenantId }),
    deleteDatasetTenant: (datasetId: string, tenantId: string) =>
        api.post('/del_dataset_tenant.json', { dataset_id: datasetId, tenant_id: tenantId }),
    listDatasetTenant: () =>
        api.get('/list_dataset_tenant.json'),
};

export const tenantAccountApi = {
    listTenantAccounts: (page: number) =>
        api.get('/list_tenant_account.json', { params: { page } }),
    listByAccount: (accountId: string, page: number) =>
        api.get('/list_tenant_account_by_account.json', { params: { account_id: accountId, page } }),
    listByTenant: (tenantId: string, page: number) =>
        api.get('/list_tenant_account_by_tenant.json', { params: { tenant_id: tenantId, page } }),
    addTenantAccount: (data: { account_id: string; tenant_id: string; role: string }) =>
        api.post('/add_tenant_account.json', data),
    deleteTenantAccount: (accountId: string, tenantId: string) =>
        api.post('/del_tenant_account.json', { account_id: accountId, tenant_id: tenantId }),
    updateRole: (data: { account_id: string; tenant_id: string; role: string }) =>
        api.post('/update_tenant_account_role.json', data),
};

// 修改最后的导出语句，只导出未使用 export const 导出的内容
export { api };