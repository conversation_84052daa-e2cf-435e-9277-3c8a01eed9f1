package models

import (
	"time"

	"github.com/google/uuid"
)

// TempData 临时数据模型
type TempData struct {
	UUID      uuid.UUID `json:"uuid" gorm:"primaryKey;type:uuid;default:uuid_generate_v4()"`
	Title     string    `json:"title" gorm:"type:varchar(255);not null"`
	Content   string    `json:"content" gorm:"type:text;not null"`
	CreatedAt time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (TempData) TableName() string {
	return "temp_data"
}
