package models

// DashboardSummary Dashboard总览数据
type DashboardSummary struct {
	TotalApps             int              `json:"total_apps"`
	TotalAccounts         int              `json:"total_accounts"`
	TotalDatasets         int              `json:"total_data_sets"`
	TotalDocuments        int              `json:"total_documents"`
	TotalAnchorStatistics int              `json:"total_anchor_statistics"`
	TotalMessages         int              `json:"total_messages"`
	MessageStatistics     []DateStatistics `json:"message_statistics"`
	AnchorStatistics      []DateStatistics `json:"anchor_statistics"`
	AppStatistics         []AppStatistics  `json:"app_statistics"`
}

// DateStatistics 按日期统计数据
type DateStatistics struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

// AppStatistics 每个app的统计数据
type AppStatistics struct {
	AppID        string `json:"app_id"`
	AppName      string `json:"app_name"`
	ClickCount   int64  `json:"click_count"`
	MessageCount int64  `json:"message_count"`
}

// DashboardQueryRequest Dashboard查询请求
type DashboardQueryRequest struct {
	StatType  string `json:"stat_type" form:"stat_type"`   // daily 或 monthly
	StartDate string `json:"start_date" form:"start_date"` // YYYY-MM-DD
	EndDate   string `json:"end_date" form:"end_date"`     // YYYY-MM-DD
}
