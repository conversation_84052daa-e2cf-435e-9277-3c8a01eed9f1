package models

import (
	"database/sql"
	"time"

	"github.com/google/uuid"
)

// FeedbackStatus defines the enum type for feedback status
type FeedbackStatus string

const (
	FeedbackStatusPending  FeedbackStatus = "pending"
	FeedbackStatusAnswered FeedbackStatus = "answered"
)

// Feedback represents the structure of the 'feedbacks' table
type Feedback struct {
	ID              uuid.UUID      `json:"id" db:"id"`
	UserID          uuid.UUID      `json:"user_id" db:"user_id"`
	TenantID        uuid.UUID      `json:"tenant_id" db:"tenant_id"`
	FeedbackContent string         `json:"feedback_content" db:"feedback_content"`
	Response        sql.NullString `json:"response,omitempty" db:"response"` // Nullable text
	Status          FeedbackStatus `json:"status" db:"status"`
	CreatedAt       time.Time      `json:"created_at,omitempty" db:"created_at"` // Nullable timestamp
	UpdatedAt       sql.NullTime   `json:"updated_at,omitempty" db:"updated_at"` // Nullable timestamp
}

type FeedbackResponse struct {
	ID              uuid.UUID      `json:"id" db:"id"`
	UserID          uuid.UUID      `json:"user_id" db:"user_id"`
	TenantID        uuid.UUID      `json:"tenant_id" db:"tenant_id"`
	UserName        string         `json:"user_name" db:"user_name"`
	FeedbackContent string         `json:"feedback_content" db:"feedback_content"`
	Response        sql.NullString `json:"response,omitempty" db:"response"` // Nullable text
	Status          FeedbackStatus `json:"status" db:"status"`
	CreatedAt       time.Time      `json:"created_at,omitempty" db:"created_at"` // Nullable timestamp
	UpdatedAt       sql.NullTime   `json:"updated_at,omitempty" db:"updated_at"` // Nullable timestamp

}
