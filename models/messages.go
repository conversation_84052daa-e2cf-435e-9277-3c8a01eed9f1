package models

import (
	"time"
)

// Messages 消息表模型
type Messages struct {
	ID                      string    `json:"id" gorm:"primaryKey"`
	AppID                   string    `json:"app_id" gorm:"column:app_id"`
	ModelProvider           *string   `json:"model_provider" gorm:"column:model_provider"`
	ModelID                 *string   `json:"model_id" gorm:"column:model_id"`
	OverrideModelConfigs    *string   `json:"override_model_configs" gorm:"column:override_model_configs"`
	ConversationID          string    `json:"conversation_id" gorm:"column:conversation_id"`
	Inputs                  string    `json:"inputs" gorm:"type:json;default:'{}'"`
	Query                   string    `json:"query"`
	Message                 string    `json:"message" gorm:"type:json;default:'{}'"`
	MessageTokens           int       `json:"message_tokens" gorm:"column:message_tokens;default:0"`
	MessageUnitPrice        float64   `json:"message_unit_price" gorm:"column:message_unit_price;default:0"`
	Answer                  string    `json:"answer"`
	AnswerTokens            int       `json:"answer_tokens" gorm:"column:answer_tokens;default:0"`
	AnswerUnitPrice         float64   `json:"answer_unit_price" gorm:"column:answer_unit_price;default:0"`
	ProviderResponseLatency float64   `json:"provider_response_latency" gorm:"column:provider_response_latency;default:0"`
	TotalPrice              *float64  `json:"total_price" gorm:"column:total_price"`
	Currency                string    `json:"currency" gorm:"default:'USD'"`
	FromSource              string    `json:"from_source" gorm:"column:from_source"`
	InvokeFrom              string    `json:"invoke_from" gorm:"column:invoke_from"`
	FromEndUserID           *string   `json:"from_end_user_id" gorm:"column:from_end_user_id"`
	FromAccountID           *string   `json:"from_account_id" gorm:"column:from_account_id"`
	CreatedAt               time.Time `json:"created_at"`
	UpdatedAt               time.Time `json:"updated_at"`
}

func (Messages) TableName() string {
	return "messages"
}
