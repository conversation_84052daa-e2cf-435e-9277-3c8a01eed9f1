package models

import (
	"time"
)

// Conversations 对话表模型
type Conversations struct {
	ID                      string     `json:"id" gorm:"primaryKey"`
	AppID                   string     `json:"app_id" gorm:"column:app_id"`
	AppModelConfigID        *string    `json:"app_model_config_id" gorm:"column:app_model_config_id"`
	ModelProvider           *string    `json:"model_provider" gorm:"column:model_provider"`
	OverrideModelConfigs    *string    `json:"override_model_configs" gorm:"column:override_model_configs"`
	ModelID                 *string    `json:"model_id" gorm:"column:model_id"`
	Mode                    string     `json:"mode"`
	Name                    string     `json:"name"`
	Summary                 *string    `json:"summary"`
	Inputs                  string     `json:"inputs" gorm:"type:json;default:'{}'"`
	Introduction            *string    `json:"introduction"`
	SystemInstruction       *string    `json:"system_instruction" gorm:"column:system_instruction"`
	SystemInstructionTokens int        `json:"system_instruction_tokens" gorm:"column:system_instruction_tokens;default:0"`
	Status                  string     `json:"status"`
	FromSource              string     `json:"from_source" gorm:"column:from_source"`
	InvokeFrom              string     `json:"invoke_from" gorm:"column:invoke_from"`
	FromEndUserID           *string    `json:"from_end_user_id" gorm:"column:from_end_user_id"`
	FromAccountID           *string    `json:"from_account_id" gorm:"column:from_account_id"`
	ReadAt                  *time.Time `json:"read_at" gorm:"column:read_at"`
	ReadAccountID           *string    `json:"read_account_id" gorm:"column:read_account_id"`
	CreatedAt               time.Time  `json:"created_at"`
	UpdatedAt               time.Time  `json:"updated_at"`
}

func (Conversations) TableName() string {
	return "conversations"
}
