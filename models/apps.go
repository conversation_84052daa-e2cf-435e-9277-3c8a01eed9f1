package models

type Apps struct {
	Id                  string  `json:"id"  gorm:"primaryKey"`
	TenantId            string  `json:"tenant_id" gorm:"column:tenant_id"`
	Name                string  `json:"name"`
	Mode                string  `json:"mode"`
	Icon                string  `json:"icon"`
	IconBackground      string  `json:"icon_background"`
	AppModelConfigId    string  `json:"app_model_config_id"`
	Status              string  `json:"status"`
	EnableSite          bool    `json:"enable_site"`
	EnableApi           bool    `json:"enable_api"`
	ApiRpm              int     `json:"api_rpm"`
	ApiRph              int     `json:"api_rph"`
	IsDemo              bool    `json:"is_demo"`
	IsPublic            bool    `json:"is_public"`
	CreatedAt           string  `json:"created_at"`
	UpdatedAt           string  `json:"updated_at"`
	IsUniversal         bool    `json:"is_universal"`
	WorkflowId          *string `json:"workflow_id"`
	Description         string  `json:"description"`
	Tracing             *string `json:"tracing"`
	MaxActiveRequests   *int    `json:"max_active_requests"`
	IconType            string  `json:"icon_type"`
	CreatedBy           string  `json:"created_by"`
	UpdatedBy           string  `json:"updated_by"`
	UseIconAsAnswerIcon bool    `json:"use_icon_as_answer_icon"`
	OnlyMe              bool    `json:"only_me" gorm:"column:only_me"`
}

func (Apps) TableName() string {
	return "apps"
}
