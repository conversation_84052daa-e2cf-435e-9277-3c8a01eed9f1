package models

import (
	"time"

	"github.com/google/uuid"
)

// AnchorStatistics 锚点统计模型
type AnchorStatistics struct {
	ID           int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID     uuid.UUID `json:"tenant_id" gorm:"type:uuid;not null;index"`
	UserID       uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	ResourceType string    `json:"resource_type" gorm:"type:varchar(100);not null;index"`
	ResourceID   string    `json:"resource_id" gorm:"type:varchar(255);not null;index"`
	ResourceName string    `json:"resource_name" gorm:"type:varchar(500);index"`
	ActionType   string    `json:"action_type" gorm:"type:varchar(100);not null;index"`
	ActionCount  int       `json:"action_count" gorm:"default:1"`
	RecordDate   time.Time `json:"record_date" gorm:"type:date;not null;index"`
	CreatedAt    time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP"`
}

func (AnchorStatistics) TableName() string {
	return "anchor_statistics"
}

// AnchorStatisticsRequest 记录锚点统计的请求结构
type AnchorStatisticsRequest struct {
	ResourceType string `json:"resource_type" binding:"required" example:"app"`
	ResourceID   string `json:"resource_id" binding:"required" example:"123e4567-e89b-12d3-a456-426614174000"`
	ActionType   string `json:"action_type" binding:"required" example:"click"`
}

// AnchorStatisticsQueryRequest 查询锚点统计的请求结构
type AnchorStatisticsQueryRequest struct {
	ResourceType string `json:"resource_type" form:"resource_type"`
	ResourceID   string `json:"resource_id" form:"resource_id"`
	ActionType   string `json:"action_type" form:"action_type"`
	UserID       string `json:"user_id" form:"user_id"`
	StartDate    string `json:"start_date" form:"start_date" example:"2024-01-01"`
	EndDate      string `json:"end_date" form:"end_date" example:"2024-12-31"`
	GroupBy      string `json:"group_by" form:"group_by" example:"day"` // day, month, year
	Page         int    `json:"page" form:"page" example:"1"`
	PageSize     int    `json:"page_size" form:"page_size" example:"20"`
}

// AnchorStatisticsResponse 锚点统计响应结构
type AnchorStatisticsResponse struct {
	ID           int64     `json:"id"`
	TenantID     uuid.UUID `json:"tenant_id"`
	UserID       uuid.UUID `json:"user_id"`
	UserName     string    `json:"user_name,omitempty"`
	ResourceType string    `json:"resource_type"`
	ResourceID   string    `json:"resource_id"`
	ResourceName string    `json:"resource_name,omitempty"`
	ActionType   string    `json:"action_type"`
	ActionCount  int       `json:"action_count"`
	RecordDate   time.Time `json:"record_date"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// AnchorStatisticsAggregateResponse 聚合统计响应结构
type AnchorStatisticsAggregateResponse struct {
	Period       string `json:"period"`        // 时间周期 (2024-01-01, 2024-01, 2024)
	ResourceType string `json:"resource_type"` // 资源类型
	ResourceID   string `json:"resource_id"`   // 资源ID
	ResourceName string `json:"resource_name,omitempty"` // 资源名称
	ActionType   string `json:"action_type"`   // 操作类型
	TotalCount   int64  `json:"total_count"`   // 总次数
	UserCount    int64  `json:"user_count"`    // 用户数
}

// AnchorStatisticsListResponse 分页列表响应结构
type AnchorStatisticsListResponse struct {
	Data       []AnchorStatisticsResponse `json:"data"`
	Total      int64                      `json:"total"`
	Page       int                        `json:"page"`
	PageSize   int                        `json:"page_size"`
	TotalPages int                        `json:"total_pages"`
}

// AnchorStatisticsAggregateListResponse 聚合统计分页列表响应结构
type AnchorStatisticsAggregateListResponse struct {
	Data       []AnchorStatisticsAggregateResponse `json:"data"`
	Total      int64                               `json:"total"`
	Page       int                                 `json:"page"`
	PageSize   int                                 `json:"page_size"`
	TotalPages int                                 `json:"total_pages"`
}

// AnchorStatisticsAnalysisRequest 分析统计请求结构
type AnchorStatisticsAnalysisRequest struct {
	ResourceType string `json:"resource_type" form:"resource_type" binding:"required" example:"app"`
	ResourceID   string `json:"resource_id" form:"resource_id" example:"123e4567-e89b-12d3-a456-426614174000"`
	ActionType   string `json:"action_type" form:"action_type" binding:"required" example:"click"`
	Days         int    `json:"days" form:"days" binding:"required,min=1,max=365" example:"30"`
	Step         int    `json:"step" form:"step" binding:"required,min=1,max=30" example:"7"`
}

// AnchorStatisticsAnalysisResponse 分析统计响应结构
type AnchorStatisticsAnalysisResponse struct {
	StartDate    string `json:"start_date"`    // 开始日期
	EndDate      string `json:"end_date"`      // 结束日期
	TotalCount   int64  `json:"total_count"`   // 总操作次数
	UserCount    int64  `json:"user_count"`    // 用户数
	ResourceType string `json:"resource_type"` // 资源类型
	ActionType   string `json:"action_type"`   // 操作类型
}

// AnchorStatisticsAnalysisListResponse 分析统计列表响应结构
type AnchorStatisticsAnalysisListResponse struct {
	Data         []AnchorStatisticsAnalysisResponse `json:"data"`
	ResourceType string                             `json:"resource_type"`
	ActionType   string                             `json:"action_type"`
	TotalDays    int                                `json:"total_days"`
	StepDays     int                                `json:"step_days"`
	Summary      AnchorStatisticsAnalysisSummary    `json:"summary"`
}

// AnchorStatisticsAnalysisSummary 分析统计汇总信息
type AnchorStatisticsAnalysisSummary struct {
	TotalCount       int64   `json:"total_count"`        // 总操作次数
	TotalUserCount   int64   `json:"total_user_count"`   // 总用户数
	AveragePerPeriod float64 `json:"average_per_period"` // 每个周期平均次数
	MaxPeriodCount   int64   `json:"max_period_count"`   // 单个周期最大次数
	MinPeriodCount   int64   `json:"min_period_count"`   // 单个周期最小次数
}

// AnchorStatisticsResourceSummaryRequest 资源汇总统计请求结构
type AnchorStatisticsResourceSummaryRequest struct {
	ResourceType string `json:"resource_type" form:"resource_type" binding:"required" example:"app"`
	ActionType   string `json:"action_type" form:"action_type" binding:"required" example:"click"`
}

// AnchorStatisticsResourceSummaryItem 单个资源的统计信息
type AnchorStatisticsResourceSummaryItem struct {
	ResourceType string `json:"resource_type"` // 资源类型
	ResourceID   string `json:"resource_id"`   // 资源ID
	ResourceName string `json:"resource_name"` // 资源名称
	ActionType   string `json:"action_type"`   // 操作类型
	TotalCount   int64  `json:"total_count"`   // 总操作次数
	UserCount    int64  `json:"user_count"`    // 用户数
}

// AnchorStatisticsResourceSummaryResponse 资源汇总统计响应结构（以resource_id为key的map）
type AnchorStatisticsResourceSummaryResponse map[string]AnchorStatisticsResourceSummaryItem

// 常用的资源类型常量
const (
	ResourceTypeApp      = "app"
	ResourceTypeDataset  = "dataset"
	ResourceTypeFeedback = "feedback"
	ResourceTypeMenu     = "menu"
	ResourceTypeUser     = "user"
	ResourceTypeTenant   = "tenant"
)

// 常用的操作类型常量
const (
	ActionTypeClick  = "click"
	ActionTypeView   = "view"
	ActionTypeCreate = "create"
	ActionTypeEdit   = "edit"
	ActionTypeDelete = "delete"
	ActionTypeLogin  = "login"
	ActionTypeLogout = "logout"
)
