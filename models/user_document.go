package models

import (
	"time"
)

// UserDocument 用户文档模型
type UserDocument struct {
	ID        int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	DocType   string    `json:"doc_type" gorm:"type:varchar(255);not null;index"`
	Content   string    `json:"content" gorm:"type:text;not null"`
	CreatedBy string    `json:"created_by" gorm:"type:varchar(255);not null;index"`
	CreatedAt time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP"`
}

func (UserDocument) TableName() string {
	return "user_documents"
}

// UserDocumentRequest 用户文档请求结构
type UserDocumentRequest struct {
	DocType string `json:"doc_type" binding:"required" example:"user_manual"`
	Content string `json:"content" binding:"required" example:"这是用户手册的内容..."`
}

// UserDocumentUpdateRequest 用户文档更新请求结构
type UserDocumentUpdateRequest struct {
	Content string `json:"content" binding:"required" example:"这是更新后的内容..."`
}

// UserDocumentUpdateByIDRequest 根据ID更新用户文档请求结构
type UserDocumentUpdateByIDRequest struct {
	Content string `json:"content" binding:"required" example:"这是更新后的内容..."`
}

// UserDocumentResponse 用户文档响应结构
type UserDocumentResponse struct {
	ID        int64     `json:"id"`
	DocType   string    `json:"doc_type"`
	Content   string    `json:"content"`
	CreatedBy string    `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UserDocumentListResponse 用户文档列表响应结构
type UserDocumentListResponse struct {
	ID        int64     `json:"id"`
	DocType   string    `json:"doc_type"`
	CreatedBy string    `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
