package models

// LoginRequest 登录请求
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// SetPasswordRequest 设置密码请求
type SetPasswordRequest struct {
	ID          string `json:"id"`
	NewPassword string `json:"password"`
}

// DatasetTenantRequest 数据集与租户关联请求
type DatasetTenantRequest struct {
	DatasetID string `json:"dataset_id"`
	TenantID  string `json:"tenant_id"`
}

// TenantAccountRequest 租户账户关联请求
type TenantAccountRequest struct {
	TenantID  string `json:"tenant_id"`
	AccountID string `json:"account_id"`
	Role      string `json:"role,omitempty"`
}

type DeleteTenantAccount struct {
	ID string `json:"id"`
}

type UpdateTenantAccountRole struct {
	ID   string `json:"id"`
	Role string `json:"role"`
}

// AccountIDRequest 账户ID请求
type AccountIDRequest struct {
	ID string `json:"id"`
}

// GenericResponse 通用响应
type GenericResponse struct {
	Message string `json:"message"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Message string  `json:"message"`
	Data    Account `json:"data"`
	Token   string  `json:"token"`
}
