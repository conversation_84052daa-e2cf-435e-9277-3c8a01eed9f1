package models

import (
	"time"
)

type Providers struct {
	Id              string     `json:"id"`
	TenantId        string     `json:"tenant_id"`
	ProviderName    string     `json:"provider_name"`
	ProviderType    string     `json:"provider_type"`
	EncryptedConfig string     `json:"encrypted_config"`
	IsValid         bool       `json:"is_valid"`
	LastUsed        *time.Time `json:"last_used"`
	QuotaType       string     `json:"quota_type"`
	QuotaLimit      *int64     `json:"quota_limit"`
	QuotaUsed       int        `json:"quota_used"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

type ProviderModelSettings struct {
	Id                   string    `json:"id"`
	TenantId             string    `json:"tenant_id"`
	ProviderName         string    `json:"provider_name"`
	ModelName            string    `json:"model_name"`
	ModelType            string    `json:"model_type"`
	Enabled              bool      `json:"enabled"`
	LoadBalancingEnabled bool      `json:"load_balancing_enabled"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

type ProviderModel struct {
	ID              string    `gorm:"type:uuid;default:uuid_generate_v4();primaryKey" json:"id"`
	TenantID        string    `gorm:"type:uuid;not null;uniqueIndex:idx_unique_provider_model_name" json:"tenant_id"`
	ProviderName    string    `gorm:"type:varchar(255);not null;uniqueIndex:idx_unique_provider_model_name" json:"provider_name"`
	ModelName       string    `gorm:"type:varchar(255);not null;uniqueIndex:idx_unique_provider_model_name" json:"model_name"`
	ModelType       string    `gorm:"type:varchar(40);not null;uniqueIndex:idx_unique_provider_model_name" json:"model_type"`
	EncryptedConfig string    `gorm:"type:text" json:"encrypted_config"`
	IsValid         bool      `gorm:"default:false;not null" json:"is_valid"`
	CreatedAt       time.Time `gorm:"default:CURRENT_TIMESTAMP(0);not null" json:"created_at"`
	UpdatedAt       time.Time `gorm:"default:CURRENT_TIMESTAMP(0);not null" json:"updated_at"`
}

// TableName explicitly sets the table name for GORM
func (ProviderModel) TableName() string {
	return "provider_models"
}

type PluginDeclaration struct {
	Id                     string    `json:"id"`
	CreatedAt              time.Time `json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`
	PluginUniqueIdentifier string    `json:"plugin_unique_identifier"`
	PluginId               string    `json:"plugin_id"`
	Declaration            string    `json:"declaration"`
}

type PluginInstallation struct {
	Id                     string    `json:"id"`
	CreatedAt              time.Time `json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`
	TenantId               string    `json:"tenant_id"`
	PluginId               string    `json:"plugin_id"`
	PluginUniqueIdentifier string    `json:"plugin_unique_identifier"`
	RuntimeType            string    `json:"runtime_type"`
	EndpointsSetups        int       `json:"endpoints_setups"`
	EndpointsActive        int       `json:"endpoints_active"`
	Source                 string    `json:"source"`
	Meta                   string    `json:"meta"`
}

type AiModelInstallation struct {
	Id                     string    `json:"id"`
	CreatedAt              time.Time `json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`
	Provider               string    `json:"provider"`
	TenantId               string    `json:"tenant_id"`
	PluginUniqueIdentifier string    `json:"plugin_unique_identifier"`
	PluginId               string    `json:"plugin_id"`
}
