package models

import "time"

type Permission string

// SystemMenu 系统菜单表结构
type SystemMenu struct {
	ID           int64      `gorm:"primaryKey;column:id;type:bigserial" json:"id"`
	PID          *int64     `gorm:"column:pid;comment:父级菜单ID" json:"pid"`
	Title        string     `gorm:"column:title;not null;comment:菜单名称" json:"title"`
	Icon         *string    `gorm:"column:icon;comment:菜单图标" json:"icon"`
	Index        *string    `gorm:"column:index;comment:路由路径" json:"index"`
	Permiss      *string    `gorm:"column:permiss;comment:权限标识" json:"permiss"`
	PermissionID *int64     `gorm:"column:permission_id;comment:权限标识" json:"permission_id"`
	CreatedAt    *time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    *time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (SystemMenu) TableName() string {
	return "system_menus"
}
