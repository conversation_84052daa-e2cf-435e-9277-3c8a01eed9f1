package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// JSON 自定义JSON类型，用于处理JSONB字段
type JSON map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into JSON", value)
	}

	return json.Unmarshal(bytes, j)
}

// QuizSubmission 答题记录模型
type QuizSubmission struct {
	ID             uuid.UUID              `json:"id" gorm:"primaryKey;type:uuid;default:uuid_generate_v4()"`
	QuizUUID       uuid.UUID              `json:"quiz_uuid" gorm:"type:uuid;not null;index"`                    // 关联到temp_data的uuid
	QuizTitle      string                 `json:"quiz_title" gorm:"type:varchar(255);not null;index"`          // 批次标题，用于分组统计
	UserID         *uuid.UUID             `json:"user_id,omitempty" gorm:"type:uuid;index"`                    // 用户ID（可选，用于已登录用户）
	SessionID      string                 `json:"session_id" gorm:"type:varchar(255);index"`                   // 会话ID（用于匿名用户）
	UserAnswers    JSON                   `json:"user_answers" gorm:"type:jsonb;not null"`                     // 用户答案 JSON
	Score          int                    `json:"score" gorm:"not null;default:0"`                             // 得分
	TotalQuestions int                    `json:"total_questions" gorm:"not null;default:0"`                   // 总题数
	CorrectAnswers int                    `json:"correct_answers" gorm:"not null;default:0"`                   // 正确答案数
	Percentage     float64                `json:"percentage" gorm:"type:decimal(5,2);not null;default:0.00"`   // 得分百分比
	UserInfo       JSON                   `json:"user_info,omitempty" gorm:"type:jsonb"`                       // 用户信息（从session获取）
	SubmittedAt    time.Time              `json:"submitted_at" gorm:"default:CURRENT_TIMESTAMP;index"`         // 提交时间
	CreatedAt      time.Time              `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`                 // 创建时间
}

// TableName 指定表名
func (QuizSubmission) TableName() string {
	return "quiz_submissions"
}

// QuizStatistics 答题统计模型
type QuizStatistics struct {
	ID                uuid.UUID `json:"id" gorm:"primaryKey;type:uuid;default:uuid_generate_v4()"`
	QuizUUID          uuid.UUID `json:"quiz_uuid" gorm:"type:uuid;not null;uniqueIndex"`             // 关联到temp_data的uuid
	QuizTitle         string    `json:"quiz_title" gorm:"type:varchar(255);not null;index"`         // 批次标题
	TotalSubmissions  int       `json:"total_submissions" gorm:"not null;default:0"`                // 总提交数
	AverageScore      float64   `json:"average_score" gorm:"type:decimal(5,2);not null;default:0"`  // 平均分
	HighestScore      int       `json:"highest_score" gorm:"not null;default:0"`                    // 最高分
	LowestScore       int       `json:"lowest_score" gorm:"not null;default:0"`                     // 最低分
	PassRate          float64   `json:"pass_rate" gorm:"type:decimal(5,2);not null;default:0"`      // 及格率（假设60分及格）
	LastSubmissionAt  time.Time `json:"last_submission_at" gorm:"default:CURRENT_TIMESTAMP"`        // 最后提交时间
	CreatedAt         time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`                // 创建时间
	UpdatedAt         time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP"`                // 更新时间
}

// TableName 指定表名
func (QuizStatistics) TableName() string {
	return "quiz_statistics"
}
