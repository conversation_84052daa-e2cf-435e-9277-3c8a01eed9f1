package models

import (
	"time"
)

type AccountStatus string

const (
	PENDING       AccountStatus = "pending"
	UNINITIALIZED AccountStatus = "uninitialized"
	ACTIVE        AccountStatus = "active"
	BANNED        AccountStatus = "banned"
	CLOSED        AccountStatus = "closed"
)

type Account struct {
	ID                string        `json:"ID"`
	Name              string        `json:"Name"`
	Email             string        `json:"Email"`
	Password          string        `json:"-"`
	PasswordSalt      string        `json:"-"`
	Avatar            string        `json:"Avatar"`
	InterfaceLanguage string        `json:"InterfaceLanguage"`
	InterfaceTheme    string        `json:"InterfaceTheme"`
	Timezone          string        `json:"Timezone"`
	Status            AccountStatus `json:"Status"`
	Unit              string        `json:"unit"`
	Department        string        `json:"Department"`
}

func (Account) TableName() string {
	return "accounts"
}

type TenantPlan string

const (
	TenantPlanBasic      TenantPlan = "basic"
	TenantPlanPro        TenantPlan = "pro"
	TenantPlanEnterprise TenantPlan = "enterprise"
)

type Tenant struct {
	ID               string `gorm:"primaryKey"`
	Name             string
	EncryptPublicKey string
	Plan             TenantPlan
	Status           string
	CreatedAt        time.Time
	UpdatedAt        time.Time
	CustomConfig     *string
}

type TenantAccountJoin struct {
	ID        string `gorm:"primaryKey"`
	TenantID  string
	AccountID string
	Role      string
	InvitedBy *string
	CreatedAt time.Time
	UpdatedAt time.Time
	Current   bool
}

type Dataset struct {
	ID                     string `gorm:"primaryKey"`
	TenantID               string
	Name                   string
	Description            string
	Provider               string
	Permission             string
	DataSourceType         string
	IndexingTechnique      string
	IndexStruct            string
	CreatedBy              string
	CreatedAt              time.Time
	UpdatedBy              string
	UpdatedAt              time.Time
	EmbeddingModel         string
	EmbeddingModelProvider string
	CollectionBindingID    string
	RetrievalModel         string
}

type UploadFiles struct {
	Id            string    `json:"id"`
	TenantId      string    `json:"tenant_id"`
	StorageType   string    `json:"storage_type"`
	Key           string    `json:"key"`
	Name          string    `json:"name"`
	Size          int       `json:"size"`
	Extension     string    `json:"extension"`
	MimeType      string    `json:"mime_type"`
	CreatedBy     string    `json:"created_by"`
	CreatedAt     time.Time `json:"created_at"`
	Used          bool      `json:"used"`
	UsedBy        string    `json:"used_by"`
	UsedAt        time.Time `json:"used_at"`
	Hash          string    `json:"hash"`
	CreatedByRole string    `json:"created_by_role"`
	SourceUrl     string    `json:"source_url"`
}

func (UploadFiles) TableName() string {
	return "upload_files"
}
