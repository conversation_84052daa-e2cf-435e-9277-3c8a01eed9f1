# 用户文档管理API文档

## 概述

用户文档管理系统用于存储和管理各种类型的用户文档内容。只有配置文件中的超管（config.Admins）才能进行用户文档的增删改操作，普通用户只能查看用户文档。

## 数据库表结构

### user_documents 表

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | BIGSERIAL | 主键 | PRIMARY KEY |
| doc_type | VARCHAR(255) | 文档类型 | NOT NULL |
| content | TEXT | 文档内容 | NOT NULL |
| created_by | VARCHAR(255) | 创建人邮箱 | NOT NULL |
| created_at | TIMESTAMP | 创建时间 | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | 更新时间 | DEFAULT CURRENT_TIMESTAMP |

### 索引说明

- 主键索引：id
- 普通索引：doc_type, created_by

## 权限说明

- **查看权限**：所有登录用户都可以查看用户文档列表和用户文档内容
- **管理权限**：只有配置文件中 `config.Admins` 列表中的用户才能创建、更新、删除用户文档

## API接口

### 1. 获取用户文档列表

**接口地址：** `GET /api/documents.json`

**权限要求：** 普通用户权限

**请求参数：** 无

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "doc_type": "user_manual",
      "created_by": "<EMAIL>",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    },
    {
      "id": 2,
      "doc_type": "api_guide",
      "created_by": "<EMAIL>",
      "created_at": "2024-01-01T11:00:00Z",
      "updated_at": "2024-01-01T11:00:00Z"
    }
  ]
}
```

### 2. 根据文档类型获取用户文档

**接口地址：** `GET /api/documents/{doc_type}`

**权限要求：** 普通用户权限

**路径参数：**
- `doc_type`: 文档类型（如：user_manual, api_guide）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "doc_type": "user_manual",
    "content": "这是用户手册的详细内容...",
    "created_by": "<EMAIL>",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 3. 根据ID获取用户文档详情

**接口地址：** `GET /api/documents/id/{id}`

**权限要求：** 普通用户权限

**路径参数：**
- `id`: 用户文档ID

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "doc_type": "user_manual",
    "content": "这是用户手册的详细内容...",
    "created_by": "<EMAIL>",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 4. 创建用户文档

**接口地址：** `POST /api/documents.json`

**权限要求：** 超管权限（config.Admins）

**请求参数：**
```json
{
  "doc_type": "user_manual",
  "content": "这是用户手册的内容..."
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "doc_type": "user_manual",
    "content": "这是用户手册的内容...",
    "created_by": "<EMAIL>",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 5. 更新用户文档（根据类型）

**接口地址：** `PUT /api/documents/{doc_type}`

**权限要求：** 超管权限（config.Admins）

**路径参数：**
- `doc_type`: 文档类型

**请求参数：**
```json
{
  "content": "这是更新后的内容..."
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "doc_type": "user_manual",
    "content": "这是更新后的内容...",
    "created_by": "<EMAIL>",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 6. 更新用户文档（根据ID）

**接口地址：** `PUT /api/documents/id/{id}`

**权限要求：** 超管权限（config.Admins）

**路径参数：**
- `id`: 用户文档ID

**请求参数：**
```json
{
  "content": "这是更新后的内容..."
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "doc_type": "user_manual",
    "content": "这是更新后的内容...",
    "created_by": "<EMAIL>",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 7. 根据文档类型删除用户文档

**接口地址：** `DELETE /api/documents/{doc_type}`

**权限要求：** 超管权限（config.Admins）

**路径参数：**
- `doc_type`: 文档类型

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": "用户文档删除成功"
}
```

### 8. 根据ID删除用户文档

**接口地址：** `DELETE /api/documents/id/{id}`

**权限要求：** 超管权限（config.Admins）

**路径参数：**
- `id`: 用户文档ID

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": "用户文档删除成功"
}
```

## 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| INVALID_PARAMS | 400 | 参数错误 |
| UNAUTHORIZED | 401 | 未授权 |
| FORBIDDEN | 403 | 没有超级管理员权限 |
| NOT_FOUND | 404 | 文档不存在 |
| CONFLICT | 409 | 文档类型已存在 |
| DB_ERROR | 500 | 数据库操作失败 |

## 使用示例

### 创建一个用户手册文档

```bash
curl -X POST "http://localhost:8080/api/documents.json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_type": "user_manual",
    "content": "# 用户手册\n\n这是系统的用户手册..."
  }'
```

### 获取用户手册文档

```bash
curl -X GET "http://localhost:8080/api/documents/user_manual" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 更新用户手册文档

```bash
curl -X PUT "http://localhost:8080/api/documents/user_manual" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "# 用户手册（更新版）\n\n这是更新后的用户手册..."
  }'
```

## 注意事项

1. **文档类型唯一性**：每个用户文档类型在系统中是唯一的，不能重复创建
2. **权限控制**：只有配置文件中指定的超管邮箱才能进行用户文档的增删改操作
3. **内容格式**：用户文档内容支持任意文本格式，包括Markdown、HTML等
4. **创建人记录**：系统会自动记录用户文档的创建人邮箱
5. **时间戳**：创建时间和更新时间会自动维护
