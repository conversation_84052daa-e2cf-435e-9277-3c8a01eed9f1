{
    "go.goroot": "F:/go/pkg/mod/golang.org/<EMAIL>-amd64",  // 替换为您想使用的Go版本路径
    "go.toolsEnvVars": {
        "GOROOT": "F:/go/pkg/mod/golang.org/<EMAIL>-amd64"  // 保持与上面相同
    },
    "sqltools.connections": [
        {
            "previewLimit": 50,
            "server": "localhost",
            "port": 25432,
            "driver": "PostgreSQL",
            "name": "localhost",
            "password": "difyai123456",
            "username": "postgres",
            "database": "dify"
        }
    ]
}